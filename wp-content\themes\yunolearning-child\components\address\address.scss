@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
	.addressCard {
		border: 1px solid $grey;
		padding: $gap15;
		border-radius: 4px;

		&:hover {
			.actionList {
				visibility: visible;
			}	
		}

		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: $gap15;
			text-transform: capitalize;
		}

		.actionList {
			visibility: hidden;

			li {
				width: 28px;
				height: 28px;
				text-align: center;

				a {
					color: $primaryColor;
				}
			}
		}

		.addressType {
			font-size: $fontSizeLarger + 6;
			@include setFontColor($primaryCopyColor, 0.5);
			font-family: "Roboto Slab";
			font-weight: 700;
		}

		p {
			margin: 0;
		}
	}

	.action {
		text-align: center;

		p {
			margin-bottom: $gapLargest;
		}
	}
}