@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
	.dark87 {
        @include setFontColor($primaryCopyColor, 0.87);
    }

    .dark60 {
        @include setFontColor($primaryCopyColor, 0.6);
    }

	.yunoCategories {
		padding: $gapLargest 0;

		@media (min-width: 768px) {
			padding: $gapLargest * 2 0 $gapLargest;
		}

		.sectionTitle {
			font-size: $headline3;
			text-align: center;
			margin: 0;
			@extend .dark87;
		}

		.categoryWrapper {
			padding-top: $gapLargest;

			.col-12 {
				margin-bottom: $gapLargest;

				@media (min-width: 768px) {
					margin-bottom: $gapLargest;
				}

				@media (min-width: 992px) {
					margin-bottom: 0;
				}
			}
		}

		.categoryCard {
			background: $whiteBG;
			border-radius: 4px;
			overflow: hidden;
			box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
			padding: 24px;
			height: 100%;

			// &:hover {
			// 	box-shadow: rgba(0,0,0,.117647) 0 0 20px;
			// }

			.cardImg {
				img {
					width: 100%;
					height: auto;
				}
			}

			.cardBody {
				margin: 0;
				background: $whiteBG;
				padding-top: $gap15;

				.cardTitle {
					font-size: $subtitle1;
					text-transform: capitalize;
					line-height: 24px;
					@extend .dark87;
				}

				.cardDescription {
					font-size: $body2;
					margin: 0;
					min-height: 120px;
					@extend .dark60;
				}
			}

			.cardFooter {
				padding-top: 0;

				.button {
					overflow: hidden;
					position: relative;
					
					.makeIndent {
						display: inline-block;
						text-indent: -99999px;
						position: absolute;
						left: 0;
						top: 0;
					}
				}
			}
		}
	}
}