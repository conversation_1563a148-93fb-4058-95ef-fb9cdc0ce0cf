{"version": 3, "file": "static/css/main.874565c2.css", "mappings": "AAscA,gBAIA,CAzcA,KACE,qBACF,CAEA,iBAGE,kBACF,CAEA,YAEE,YAAa,CACb,qBAAsB,CAFtB,UAGF,CAKA,mBAEE,4BAAgC,CADhC,iBAEF,CAKA,iBACE,kBACF,CACA,uBAQE,iDAAgE,CAPhE,UAAW,CAGX,aAAc,CAKd,+GAAmH,CAPnH,WAAY,CAGZ,iBAAkB,CAFlB,UAOF,CACA,yBACE,uBACE,YACF,CACF,CACA,wBAIE,YAAa,CAFb,gBAAiB,CACjB,iBAAkB,CAFlB,gBAAiB,CAIjB,cACF,CACA,yBACE,wBACE,aAAc,CACd,cACF,CACF,CAKA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CACA,oBAEE,qBAAsB,CADtB,SAEF,CACA,yBACE,oBACE,eACF,CACF,CACA,yBACE,oBAUE,wBAAyB,CARzB,YAAa,CAIb,WAAY,CADZ,MAAO,CAGP,QAAS,CAGT,aAAc,CAFd,mBAAyB,CANzB,cAAe,CACf,KAAM,CAGN,UAAW,CANX,UAWF,CACF,CACA,6BAEE,6BAA+B,CAD/B,aAEF,CAEA,yBACE,2BAWE,qBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAHlB,aAAc,CATd,aAAc,CAId,uFAAgG,CAGhG,cAAe,CADf,eAAgB,CAEhB,eAAgB,CAHhB,eAAgB,CAHhB,eAAgB,CAChB,YAAa,CAFb,UAYF,CACA,+CACE,kBACF,CACF,CAKA,iBAEE,wBAAyB,CADzB,SAEF,CACA,yBACE,iBAEE,cAAe,CADf,UAEF,CACF,CAQA,uDAFE,kBAAmB,CAHnB,YAAa,CAEb,6BAA8B,CAD9B,UAWF,CAPA,+BAEE,aAAc,CADd,cAMF,CAEA,wBAEE,kBAAmB,CADnB,YAAa,CAEb,4BACF,CAEA,kBAIE,SAMF,CACA,6CAHE,kBAAmB,CAFnB,YAAa,CALb,uFAAgG,CAEhG,eAAgB,CAMhB,0BAA2B,CAF3B,sBAAuB,CALvB,eAAgB,CAGhB,WAgBF,CAVA,2BAIE,UAMF,CAEA,WAME,QAAS,CALT,uFAAgG,CAEhG,eAAgB,CADhB,eAAgB,CAEhB,QAAS,CACT,SAEF,CAIA,8CACE,eACF,CACA,kBAGE,aAAc,CADd,cAAe,CAEf,kBAAmB,CACnB,SAAU,CAJV,wBAKF,CACA,iBAEE,cAAe,CADf,eAAgB,CAEhB,YAAa,CACb,SACF,CACA,kBACE,UAAc,CAGd,oBAAqB,CAFrB,eAAiB,CACjB,gBAAiB,CAEjB,eACF,CAEA,0BACE,aAAc,CACd,cAAe,CACf,YACF,CACA,wCAEE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAEb,6BAEF,CACA,gDACE,QACF,CACA,oCAEE,cAAe,CADf,gBAEF,CACA,wCACE,UAAc,CACd,eAAiB,CACjB,gBACF,CAEA,qBAUE,wEAAiB,CAAjB,kBAAiB,CAFjB,cAAa,CALb,aAAc,CADd,cAAe,CADf,aAAc,CAId,mBAAoB,CADpB,cAAe,CAEf,mBAAoB,CAGpB,eAAc,CAFd,eAIF,CACA,sDACE,wBAAyB,CACzB,yBACF,CAEA,kBACE,cACF,CACA,8BAME,qBAAyB,CACzB,iBAAkB,CAFlB,mBAAoB,CAHpB,WAAY,CAEZ,QAAS,CAIT,YAAa,CALb,WAAY,CAFZ,UAQF,CACA,oCACE,wBACF,CAEA,mBACE,aAAc,CACd,cAAe,CACf,YACF,CACA,iCAEE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAEb,6BAEF,CACA,yCACE,QACF,CACA,6BAEE,cAAe,CADf,gBAEF,CACA,iCACE,UAAc,CACd,eAAiB,CACjB,gBACF,CAEA,yBACE,cAAe,CAGf,eAAgB,CAFhB,YAAa,CACb,SAEF,CACA,+BACE,YAAa,CACb,6BACF,CACA,+BACE,aAAc,CAKd,eAAiB,CAFjB,eAAgB,CAChB,SAAU,CAHV,iBAAkB,CAClB,oBAIF,CACA,qCAQE,oBAAoC,CAPpC,UAAW,CAMX,uBAAwB,CAFxB,SAAU,CAHV,SAAU,CAOV,mBAAoB,CANpB,iBAAkB,CAClB,QAAS,CAET,uBAIF,CACA,qCACE,aAAc,CACd,eAAiB,CACjB,YACF,CACA,qCACE,aAAc,CACd,eACF,CACA,2CACE,SACF,CACA,mCACE,eAAgB,CAChB,eACF,CACA,qCACE,eAAgB,CAChB,aACF,CACA,iCACE,UAAW,CACX,gBACF,CAEA,eAGE,cAAe,CAFf,QAAS,CACT,SAEF,CAEA,oCAIE,eAAgB,CAFhB,QAAS,CACT,SAEF,CAEA,qBAEE,UAAW,CAIX,WAAY,CALZ,aAAc,CAEd,QAAS,CACT,eAAgB,CAChB,iBAEF,CAUA,8CAPE,oBAAqB,CACrB,WAAY,CAEZ,gBAAiB,CADjB,gBAAiB,CAEjB,qBAmBF,CAhBA,oBAUE,qBAAsB,CACtB,wBAAyB,CACzB,iBAAkB,CAElB,cAAe,CATf,iBAAkB,CAIlB,eAAgB,CAPhB,cAAe,CAWf,SAAU,CANV,iBAAkB,CAQlB,wBAAiB,CAAjB,gBACF,CAEA,sBAGE,eAA0B,CAF1B,aAAc,CACd,aAAc,CAEd,eACF,CAEA,4BACE,oBACF,CAEA,oDAEE,oBAAqB,CACrB,kBACF,CAEA,wDAEE,aACF,CAEA,2BAEE,eAAgB,CAChB,oBAAqB,CAFrB,eAGF,CAEA,6BACE,aACF,CAEA,kEAEE,oBACF,CAEA,sEAEE,aACF,CAEA,kDAEE,SACF,CAEA,gEAEE,gBAAuB,CACvB,WAAY,CAEZ,UAAW,CADX,cAEF,CAEA,4EAGE,aAAc,CADd,aAEF,CAEA,sEAGE,gBACF,CAEA,0FAaE,iBAAkB,CANlB,eAA0B,CAO1B,cAAe,CAVf,oBAAqB,CAIrB,iBAAkB,CAFlB,WAAY,CAGZ,gBAAiB,CAGjB,eAAgB,CAPhB,cAAe,CAKf,iBAAkB,CAKlB,kBAAoB,CAJpB,qBAKF,CAEA,wCAEE,SACF,CAEA,sDAEE,eAA0B,CAC1B,cAAe,CACf,wBAAiB,CAAjB,gBACF,CAEA,kEAEE,oBACF,CAEA,0FAOE,qBAAsB,CACtB,wBAAyB,CACzB,iBAAkB,CAPlB,aAAc,CAGd,cAAe,CADf,WAAY,CAMZ,YAAa,CAJb,iBAAkB,CAKlB,kBAAoB,CARpB,UASF,CAEA,4MAKE,oBAAqB,CADrB,aAEF,CAEA,iCACE,WAAY,CACZ,aACF,CAEA,iCACE,WAAY,CACZ,aACF,CAEA,oFAGE,kBACF,CAEA,+JAIE,oBAAqB,CADrB,eAA0B,CAE1B,kBACF,CAEA,qBACE,mBACF,CAEA,uBACE,oBAAqB,CACrB,gBAAiB,CACjB,qBACF,CAEA,gCACE,6DAEE,kBACF,CACF,CACA,8CACE,oBAAqB,CAErB,gBAAiB,CADjB,UAEF,CAEA,oCACE,oBAAqB,CACrB,WAAY,CACZ,gBAAiB,CACjB,kBACF,CAEA,0CAEE,YAAa,CADb,UAEF,CAEA,oFAEE,WAAY,CACZ,gBAAiB,CACjB,kBACF,CAEA,sIAGE,wBAA6B,CAC7B,QAAS,CAFT,WAGF,CAEA,kJAEE,WAAY,CACZ,gBACF,CAEA,kDACE,oBAAqB,CACrB,WAAY,CACZ,gBACF,CAEA,wDAME,qBAAsB,CACtB,wBAAyB,CACzB,iBAAkB,CAPlB,qBAAsB,CACtB,WAAY,CACZ,gBAAiB,CAMjB,YAAa,CALb,aAAc,CACd,iBAAkB,CAKlB,2BACF,CAEA,8DACE,oBACF,CAEA,sCACE,kBACF,CAEA,0DACE,kBAAmB,CACnB,oBAAqB,CACrB,kBACF,CAEA,4DAEE,gBAAuB,CACvB,WAAY,CAFZ,eAA0B,CAG1B,kBACF,CAEA,iEACE,kBAAmB,CACnB,kBACF,CAEA,mEACE,UACF,CAEA,+DAEE,kBAAmB,CACnB,oBAAqB,CAFrB,eAA0B,CAG1B,kBACF,CAEA,oEACE,SACF,CAEA,mEACE,SACF,CAEA,yCACE,yEAEE,YACF,CACF,CACA,yCACE,uBACE,YACF,CACF,CACA,eAEE,gBAAuB,CADvB,WAAY,CAEZ,YACF,CACA,sCACE,UAAW,CACX,UACF,CACA,kCACE,aAAc,CACd,oBACF,CACA,sCACE,kBACF,CACA,wCACE,aAAc,CACd,oBACF,CACA,yCACE,aAAc,CACd,eACF,CACA,6CACE,gBAAuB,CACvB,kBACF,CACA,+CACE,aAAc,CACd,kBACF,CACA,sCACE,kBACF,CACA,wCACE,aACF,CACA,2CACE,kBACF,CACA,6CACE,aACF,CACA,iDACE,aAAc,CACd,WAAY,CACZ,cAAe,CACf,eACF,CACA,2CACE,kBACF,CACA,6CACE,aACF,CACA,iDACE,aAAc,CACd,WAAY,CACZ,cAAe,CACf,eACF,CAEA,iBAIE,aAAc,CAEd,oBAAqB,CADrB,cAEF,CAEA,6BARE,uFAAgG,CAEhG,eAAgB,CADhB,eAqBF,CAdA,YASE,eAAiB,CADjB,wBAAyB,CAEzB,iBAAkB,CAClB,8BAA8C,CAJ9C,aAAc,CAHd,eAAgB,CAShB,eAAgB,CADhB,wBAAyB,CAPzB,cAAe,CACf,oBAQF,CACA,wBACE,eACF,CACA,eAEE,aAAc,CAEd,kBAAmB,CADnB,eAAgB,CAFhB,iBAIF,CACA,qBAOE,oBAAoC,CANpC,UAAW,CAKX,uBAAwB,CAFxB,SAAU,CAIV,mBAAoB,CANpB,iBAAkB,CAClB,QAAS,CAET,sBAIF,CACA,oBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,cACF,CACA,mBAGE,UAAc,CAFd,eAAgB,CAChB,eAAgB,CAEhB,oBACF,CACA,wBACE,aAAc,CACd,oBACF,CACA,iBAKE,UAAc,CADd,YAAa,CAHb,qBAAsB,CAEtB,cAAe,CADf,eAIF,CACA,wBACE,WACF,CACA,uBACE,aACF,CACA,mBAEE,cAAe,CADf,eAEF,CACA,qBAEE,cAAe,CADf,cAAe,CAEf,qBACF,CACA,qBAIE,qBAAsB,CACtB,iBAAkB,CAHlB,UAAc,CACd,oBAAqB,CAFrB,cAAe,CAKf,aAAc,CACd,mBACF,CACA,kBAGE,YAAa,CAFb,eAAgB,CAChB,YAEF,CACA,oBACE,QACF,CACA,mBAGE,gBAAiB,CADjB,iBAAkB,CADlB,gBAGF,CACA,uBAEE,aAAc,CACd,WAAY,CAFZ,cAGF,CACA,qBACE,QAAO,CACP,eAAgB,CAEhB,QAAS,CADT,iBAEF,CAEA,uBAEE,eAAgB,CADhB,SAEF,CAEA,sBAOE,kBAAmB,CAHnB,aAAc,CAEd,YAAa,CALb,uFAAgG,CAIhG,cAAe,CAFf,eAAgB,CAKhB,WAAY,CANZ,eAOF,CACA,6BACE,gBACF,CACA,2CACE,kBACF,CACA,iDACE,iBACF,CAEA,gBAIE,mBAAoB,CAHpB,YAAa,CAIb,uFAAgG,CAFhG,sBAAuB,CADvB,iBAIF,CACA,wBAUE,mDAAqD,CAFrD,WAAY,CAIZ,iBAAkB,CAHlB,sDAAyF,CAFzF,UAAY,CAIZ,cAAe,CAVf,aAAc,CACd,mBAAoB,CACpB,cAAe,CAEf,gBAAiB,CADjB,YAAa,CAEb,+BAOF,CACA,8BAEE,mDAAqD,CADrD,wDAEF,CACA,wCACE,YACF,CACA,yBAME,mBAAoB,CACpB,iBAAkB,CAHlB,YAAa,CACb,qBAAsB,CAHtB,WAAY,CACZ,YAAa,CAKb,iBAAkB,CAPlB,UAQF,CACA,4BAEE,qBAAsB,CADtB,iBAAkB,CAKlB,mBAAoB,CACpB,cAAe,CAHf,YAAa,CADb,YAAa,CAEb,iBAAkB,CAGlB,UACF,CACA,kCAKE,wBAAgC,CAJhC,8BAKF,CACA,0CACE,8BACF,CACA,wCASE,eAAiB,CAMjB,qBAA6B,CAG7B,6BAA8B,CAC9B,8BAA+B,CAH/B,0BAA2B,CAC3B,2BAA4B,CAN5B,8BAA6C,CAV7C,YAAa,CACb,qBAAsB,CACtB,MAAO,CAKP,eAAgB,CAFhB,QAAS,CACT,mBAAsB,CAGtB,iBAAkB,CANlB,OAAQ,CACR,QAeF,CACA,sDACE,YAAa,CACb,SACF,CACA,2CAIE,gBAAuB,CACvB,iBAAkB,CAJlB,eAAgB,CAChB,QAAS,CACT,gBAGF,CACA,sDACE,SACF,CACA,2CAIE,iBAAkB,CAClB,UAAW,CACX,cAAe,CAJf,cAAgB,CADhB,aAAc,CAEd,gBAIF,CACA,8CAGE,kBAAmB,CADnB,aAAc,CADd,iBAGF,CACA,iDACE,kBAAmB,CACnB,UACF,CACA,oDACE,gBAAuB,CACvB,UACF,CACA,+DACE,kBAAmB,CACnB,UACF,CACA,kEACE,gBAAuB,CACvB,UACF,CACA,+BACE,UAAW,CACX,cAAgB,CAEhB,eAAmB,CADnB,kBAAmB,CAEnB,oBAAqB,CACrB,wBACF,CAEA,aAIE,oBAAqB,CAHrB,uFAAgG,CAEhG,eAAgB,CADhB,eAAgB,CAGhB,UACF,CACA,oBAEE,aAAc,CADd,cAAe,CAGf,kBAAmB,CADnB,wBAEF,CAEA,YACE,uFAAgG,CAGhG,iBAAmB,CADnB,eAAgB,CADhB,eAAgB,CAGhB,cAAe,CACf,UACF,CACA,oBACE,YACF,CACA,yBACE,UACF,CACA,qBAKE,mBAAoB,CAJpB,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,YAAa,CAEb,6BACF,CACA,iCACE,wBACF,CACA,6BAEE,gBAAiB,CADjB,aAEF,CACA,wCAEE,UAAc,CADd,eAEF,CACA,yBAIE,UAAc,CAFd,eAAgB,CAChB,cAAe,CAFf,kBAIF,CACA,gCAIE,kBAAmB,CAHnB,YAAa,CACb,WAAY,CAGZ,sBAAuB,CAFvB,UAGF,CACA,0BACE,UAAc,CACd,cACF,CACA,0BACE,aACF,CACA,oBAGE,kBAAmB,CAGnB,cAAe,CALf,YAAa,CAGb,eAAgB,CAFhB,6BAA8B,CAG9B,gBAEF,CACA,iCAEE,eAAmB,CADnB,UAAc,CAEd,eACF,CACA,kEACE,UAAc,CACd,iBACF,CACA,0BACE,kBACF", "sources": ["../node_modules/@elastic/react-search-ui-views/lib/styles/styles.css"], "sourcesContent": ["@charset \"UTF-8\";\nhtml {\n  box-sizing: border-box;\n}\n\n*,\n*:before,\n*:after {\n  box-sizing: inherit;\n}\n\n.sui-layout {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n/**\n * Header / Searchbox\n */\n.sui-layout-header {\n  padding: 32px 24px;\n  border-bottom: 1px solid #eeeeee;\n}\n\n/**\n * Body\n */\n.sui-layout-body {\n  background: #fcfcfc;\n}\n.sui-layout-body:after {\n  content: \"\";\n  height: 80px;\n  width: 100%;\n  display: block;\n  position: relative;\n  background: -moz-linear-gradient(top, #fcfcfc 0%, #ffffff 100%);\n  background: -webkit-linear-gradient(top, #fcfcfc 0%, #ffffff 100%);\n  background: linear-gradient(to bottom, #fcfcfc 0%, #ffffff 100%);\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\"#fcfcfc\", endColorstr=\"#ffffff\",GradientType=0 );\n}\n@media (max-width: 800px) {\n  .sui-layout-body:after {\n    display: none;\n  }\n}\n.sui-layout-body__inner {\n  max-width: 1300px;\n  margin-left: auto;\n  margin-right: auto;\n  display: flex;\n  padding: 0 24px;\n}\n@media (max-width: 800px) {\n  .sui-layout-body__inner {\n    display: block;\n    padding: 0 15px;\n  }\n}\n\n/**\n * Sidebar / Filters\n */\n@keyframes fadein {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n.sui-layout-sidebar {\n  width: 24%;\n  padding: 32px 32px 0 0;\n}\n@media (max-width: 975px) {\n  .sui-layout-sidebar {\n    padding-right: 0;\n  }\n}\n@media (max-width: 800px) {\n  .sui-layout-sidebar {\n    z-index: 99;\n    display: none;\n    position: fixed;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n    margin: 0;\n    padding: 0 15px 30px 15px;\n    background-color: #fcfcfc;\n    overflow: auto;\n  }\n}\n.sui-layout-sidebar--toggled {\n  display: block;\n  animation: fadein 0.2s ease-out;\n}\n\n@media (max-width: 800px) {\n  .sui-layout-sidebar-toggle {\n    display: block;\n    width: 100%;\n    margin-top: 20px;\n    padding: 10px;\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n    line-height: 1.5;\n    font-weight: 400;\n    font-size: 14px;\n    font-weight: 700;\n    color: #3a56e4;\n    background-color: #ffffff;\n    border: 1px solid #3a56e4;\n    border-radius: 4px;\n  }\n  .sui-layout-sidebar .sui-layout-sidebar-toggle {\n    margin-bottom: 20px;\n  }\n}\n\n/**\n * Main / Results\n */\n.sui-layout-main {\n  width: 76%;\n  padding: 32px 0 32px 32px;\n}\n@media (max-width: 800px) {\n  .sui-layout-main {\n    width: 100%;\n    padding-left: 0;\n  }\n}\n\n.sui-layout-main-header {\n  display: flex;\n  width: 100%;\n  justify-content: space-between;\n  align-items: center;\n}\n.sui-layout-main-header__inner {\n  font-size: 12px;\n  color: #4a4b4b;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.sui-layout-main-footer {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n}\n\n.sui-search-error {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  line-height: 1.5;\n  font-weight: 400;\n  color: red;\n  margin: auto;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: calc(100vh - 180px);\n}\n.sui-search-error.no-error {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  line-height: 1.5;\n  font-weight: 400;\n  color: #333333;\n  margin: auto;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: calc(100vh - 180px);\n}\n\n.sui-facet {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  line-height: 1.5;\n  font-weight: 400;\n  margin: 0;\n  padding: 0;\n  border: 0;\n}\n.sui-facet + .sui-facet {\n  margin-top: 32px;\n}\n.sui-sorting + .sui-facet {\n  margin-top: 32px;\n}\n.sui-facet__title {\n  text-transform: uppercase;\n  font-size: 12px;\n  color: #8b9bad;\n  letter-spacing: 1px;\n  padding: 0;\n}\n.sui-facet__list {\n  line-height: 1.5;\n  font-size: 13px;\n  margin: 8px 0;\n  padding: 0;\n}\n.sui-facet__count {\n  color: #888888;\n  font-size: 0.85em;\n  margin-left: 20px;\n  display: inline-block;\n  padding-top: 2px;\n}\n\n.sui-multi-checkbox-facet {\n  color: #4f4f4f;\n  font-size: 13px;\n  margin: 8px 0;\n}\n.sui-multi-checkbox-facet__option-label {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  cursor: pointer;\n}\n.sui-multi-checkbox-facet__option-input-wrapper {\n  flex: 1;\n}\n.sui-multi-checkbox-facet__checkbox {\n  margin-right: 8px;\n  cursor: pointer;\n}\n.sui-multi-checkbox-facet__option-count {\n  color: #888888;\n  font-size: 0.85em;\n  margin-left: 24px;\n}\n\n.sui-facet-view-more {\n  display: block;\n  cursor: pointer;\n  color: #3a56e4;\n  font-size: 13px;\n  font-family: inherit;\n  line-height: inherit;\n  text-align: left;\n  border: unset;\n  padding: unset;\n  background: unset;\n}\n.sui-facet-view-more:hover, .sui-facet-view-more:focus {\n  background-color: #f8f8f8;\n  outline: 4px solid #f8f8f8;\n}\n\n.sui-facet-search {\n  margin: 6px 0px 0px 0px;\n}\n.sui-facet-search__text-input {\n  width: 100%;\n  height: 100%;\n  padding: 6px;\n  margin: 0;\n  font-family: inherit;\n  border: 1px solid #cccccc;\n  border-radius: 4px;\n  outline: none;\n}\n.sui-facet-search__text-input:focus {\n  border: 1px solid #3a56e4;\n}\n\n.sui-boolean-facet {\n  color: #4f4f4f;\n  font-size: 13px;\n  margin: 8px 0;\n}\n.sui-boolean-facet__option-label {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  cursor: pointer;\n}\n.sui-boolean-facet__option-input-wrapper {\n  flex: 1;\n}\n.sui-boolean-facet__checkbox {\n  margin-right: 8px;\n  cursor: pointer;\n}\n.sui-boolean-facet__option-count {\n  color: #888888;\n  font-size: 0.85em;\n  margin-left: 24px;\n}\n\n.sui-single-option-facet {\n  font-size: 13px;\n  margin: 8px 0;\n  padding: 0;\n  list-style: none;\n}\n.sui-single-option-facet__item {\n  display: flex;\n  justify-content: space-between;\n}\n.sui-single-option-facet__link {\n  color: #4f4f4f;\n  position: relative;\n  text-decoration: none;\n  list-style: none;\n  padding: 0;\n  font-weight: bold;\n}\n.sui-single-option-facet__link:after {\n  content: \"\";\n  opacity: 0;\n  position: absolute;\n  top: -1px;\n  left: -5px;\n  width: calc(100% + 10px);\n  height: calc(100% + 2px);\n  background: rgba(37, 139, 248, 0.08);\n  pointer-events: none;\n}\n.sui-single-option-facet__link:focus {\n  color: #3a56e4;\n  font-weight: bold;\n  outline: none;\n}\n.sui-single-option-facet__link:hover {\n  color: #3a56e4;\n  font-weight: bold;\n}\n.sui-single-option-facet__link:hover:after {\n  opacity: 1;\n}\n.sui-single-option-facet__selected {\n  font-weight: 900;\n  list-style: none;\n}\n.sui-single-option-facet__selected a {\n  font-weight: 100;\n  padding: 0 2px;\n}\n.sui-single-option-facet__remove {\n  color: #666;\n  margin-left: 10px;\n}\n\n.rc-pagination {\n  margin: 0;\n  padding: 0;\n  font-size: 14px;\n}\n\n.rc-pagination ul,\n.rc-pagination ol {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n}\n\n.rc-pagination::after {\n  display: block;\n  clear: both;\n  height: 0;\n  overflow: hidden;\n  visibility: hidden;\n  content: \" \";\n}\n\n.rc-pagination-total-text {\n  display: inline-block;\n  height: 28px;\n  margin-right: 8px;\n  line-height: 26px;\n  vertical-align: middle;\n}\n\n.rc-pagination-item {\n  display: inline-block;\n  min-width: 28px;\n  height: 28px;\n  margin-right: 8px;\n  font-family: Arial;\n  line-height: 26px;\n  text-align: center;\n  vertical-align: middle;\n  list-style: none;\n  background-color: #fff;\n  border: 1px solid #d9d9d9;\n  border-radius: 2px;\n  outline: 0;\n  cursor: pointer;\n  user-select: none;\n}\n\n.rc-pagination-item a {\n  display: block;\n  padding: 0 6px;\n  color: rgba(0, 0, 0, 0.85);\n  transition: none;\n}\n\n.rc-pagination-item a:hover {\n  text-decoration: none;\n}\n\n.rc-pagination-item:focus,\n.rc-pagination-item:hover {\n  border-color: #1890ff;\n  transition: all 0.3s;\n}\n\n.rc-pagination-item:focus a,\n.rc-pagination-item:hover a {\n  color: #1890ff;\n}\n\n.rc-pagination-item-active {\n  font-weight: 500;\n  background: #fff;\n  border-color: #1890ff;\n}\n\n.rc-pagination-item-active a {\n  color: #1890ff;\n}\n\n.rc-pagination-item-active:focus,\n.rc-pagination-item-active:hover {\n  border-color: #40a9ff;\n}\n\n.rc-pagination-item-active:focus a,\n.rc-pagination-item-active:hover a {\n  color: #40a9ff;\n}\n\n.rc-pagination-jump-prev,\n.rc-pagination-jump-next {\n  outline: 0;\n}\n\n.rc-pagination-jump-prev button,\n.rc-pagination-jump-next button {\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  color: #666;\n}\n\n.rc-pagination-jump-prev button:after,\n.rc-pagination-jump-next button:after {\n  display: block;\n  content: \"•••\";\n}\n\n.rc-pagination-prev,\n.rc-pagination-jump-prev,\n.rc-pagination-jump-next {\n  margin-right: 8px;\n}\n\n.rc-pagination-prev,\n.rc-pagination-next,\n.rc-pagination-jump-prev,\n.rc-pagination-jump-next {\n  display: inline-block;\n  min-width: 28px;\n  height: 28px;\n  color: rgba(0, 0, 0, 0.85);\n  font-family: Arial;\n  line-height: 28px;\n  text-align: center;\n  vertical-align: middle;\n  list-style: none;\n  border-radius: 2px;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.rc-pagination-prev,\n.rc-pagination-next {\n  outline: 0;\n}\n\n.rc-pagination-prev button,\n.rc-pagination-next button {\n  color: rgba(0, 0, 0, 0.85);\n  cursor: pointer;\n  user-select: none;\n}\n\n.rc-pagination-prev:hover button,\n.rc-pagination-next:hover button {\n  border-color: #40a9ff;\n}\n\n.rc-pagination-prev .rc-pagination-item-link,\n.rc-pagination-next .rc-pagination-item-link {\n  display: block;\n  width: 100%;\n  height: 100%;\n  font-size: 12px;\n  text-align: center;\n  background-color: #fff;\n  border: 1px solid #d9d9d9;\n  border-radius: 2px;\n  outline: none;\n  transition: all 0.3s;\n}\n\n.rc-pagination-prev:focus .rc-pagination-item-link,\n.rc-pagination-next:focus .rc-pagination-item-link,\n.rc-pagination-prev:hover .rc-pagination-item-link,\n.rc-pagination-next:hover .rc-pagination-item-link {\n  color: #1890ff;\n  border-color: #1890ff;\n}\n\n.rc-pagination-prev button:after {\n  content: \"‹\";\n  display: block;\n}\n\n.rc-pagination-next button:after {\n  content: \"›\";\n  display: block;\n}\n\n.rc-pagination-disabled,\n.rc-pagination-disabled:hover,\n.rc-pagination-disabled:focus {\n  cursor: not-allowed;\n}\n\n.rc-pagination-disabled .rc-pagination-item-link,\n.rc-pagination-disabled:hover .rc-pagination-item-link,\n.rc-pagination-disabled:focus .rc-pagination-item-link {\n  color: rgba(0, 0, 0, 0.25);\n  border-color: #d9d9d9;\n  cursor: not-allowed;\n}\n\n.rc-pagination-slash {\n  margin: 0 10px 0 5px;\n}\n\n.rc-pagination-options {\n  display: inline-block;\n  margin-left: 16px;\n  vertical-align: middle;\n}\n\n@media all and (-ms-high-contrast: none) {\n  .rc-pagination-options *::-ms-backdrop,\n.rc-pagination-options {\n    vertical-align: top;\n  }\n}\n.rc-pagination-options-size-changer.rc-select {\n  display: inline-block;\n  width: auto;\n  margin-right: 8px;\n}\n\n.rc-pagination-options-quick-jumper {\n  display: inline-block;\n  height: 28px;\n  line-height: 28px;\n  vertical-align: top;\n}\n\n.rc-pagination-options-quick-jumper input {\n  width: 50px;\n  margin: 0 8px;\n}\n\n.rc-pagination-simple .rc-pagination-prev,\n.rc-pagination-simple .rc-pagination-next {\n  height: 24px;\n  line-height: 24px;\n  vertical-align: top;\n}\n\n.rc-pagination-simple .rc-pagination-prev .rc-pagination-item-link,\n.rc-pagination-simple .rc-pagination-next .rc-pagination-item-link {\n  height: 24px;\n  background-color: transparent;\n  border: 0;\n}\n\n.rc-pagination-simple .rc-pagination-prev .rc-pagination-item-link::after,\n.rc-pagination-simple .rc-pagination-next .rc-pagination-item-link::after {\n  height: 24px;\n  line-height: 24px;\n}\n\n.rc-pagination-simple .rc-pagination-simple-pager {\n  display: inline-block;\n  height: 24px;\n  margin-right: 8px;\n}\n\n.rc-pagination-simple .rc-pagination-simple-pager input {\n  box-sizing: border-box;\n  height: 100%;\n  margin-right: 8px;\n  padding: 0 6px;\n  text-align: center;\n  background-color: #fff;\n  border: 1px solid #d9d9d9;\n  border-radius: 2px;\n  outline: none;\n  transition: border-color 0.3s;\n}\n\n.rc-pagination-simple .rc-pagination-simple-pager input:hover {\n  border-color: #1890ff;\n}\n\n.rc-pagination.rc-pagination-disabled {\n  cursor: not-allowed;\n}\n\n.rc-pagination.rc-pagination-disabled .rc-pagination-item {\n  background: #f5f5f5;\n  border-color: #d9d9d9;\n  cursor: not-allowed;\n}\n\n.rc-pagination.rc-pagination-disabled .rc-pagination-item a {\n  color: rgba(0, 0, 0, 0.25);\n  background: transparent;\n  border: none;\n  cursor: not-allowed;\n}\n\n.rc-pagination.rc-pagination-disabled .rc-pagination-item-active {\n  background: #dbdbdb;\n  border-color: transparent;\n}\n\n.rc-pagination.rc-pagination-disabled .rc-pagination-item-active a {\n  color: #fff;\n}\n\n.rc-pagination.rc-pagination-disabled .rc-pagination-item-link {\n  color: rgba(0, 0, 0, 0.25);\n  background: #f5f5f5;\n  border-color: #d9d9d9;\n  cursor: not-allowed;\n}\n\n.rc-pagination.rc-pagination-disabled .rc-pagination-item-link-icon {\n  opacity: 0;\n}\n\n.rc-pagination.rc-pagination-disabled .rc-pagination-item-ellipsis {\n  opacity: 1;\n}\n\n@media only screen and (max-width: 992px) {\n  .rc-pagination-item-after-jump-prev,\n.rc-pagination-item-before-jump-next {\n    display: none;\n  }\n}\n@media only screen and (max-width: 576px) {\n  .rc-pagination-options {\n    display: none;\n  }\n}\n.sui-paging > li {\n  border: none;\n  background: transparent;\n  outline: none;\n}\n.sui-paging .rc-pagination-disabled a {\n  color: #ccc;\n  opacity: 0.5;\n}\n.sui-paging .rc-pagination-item a {\n  color: #3a56e4;\n  text-decoration: none;\n}\n.sui-paging .rc-pagination-item:hover {\n  background: #f8f8f8;\n}\n.sui-paging .rc-pagination-item:hover a {\n  color: #3a56e4;\n  text-decoration: none;\n}\n.sui-paging .rc-pagination-item-active a {\n  color: #4f4f4f;\n  font-weight: 700;\n}\n.sui-paging .rc-pagination-item-active:hover {\n  background: transparent;\n  cursor: not-allowed;\n}\n.sui-paging .rc-pagination-item-active:hover a {\n  color: #4f4f4f;\n  cursor: not-allowed;\n}\n.sui-paging .rc-pagination-next:hover {\n  background: #f8f8f8;\n}\n.sui-paging .rc-pagination-next:hover a {\n  color: #3a56e4;\n}\n.sui-paging .rc-pagination-jump-next:hover {\n  background: #f8f8f8;\n}\n.sui-paging .rc-pagination-jump-next:hover a {\n  color: #3a56e4;\n}\n.sui-paging .rc-pagination-jump-next:hover:after {\n  color: #3a56e4;\n  content: \"»\";\n  font-size: 16px;\n  line-height: 1.5;\n}\n.sui-paging .rc-pagination-jump-prev:hover {\n  background: #f8f8f8;\n}\n.sui-paging .rc-pagination-jump-prev:hover a {\n  color: #3a56e4;\n}\n.sui-paging .rc-pagination-jump-prev:hover:after {\n  color: #3a56e4;\n  content: \"«\";\n  font-size: 16px;\n  line-height: 1.5;\n}\n\n.sui-paging-info {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  line-height: 1.5;\n  font-weight: 400;\n  color: #4a4b4b;\n  font-size: 12px;\n  display: inline-block;\n}\n\n.sui-result {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  line-height: 1.5;\n  font-weight: 400;\n  list-style: none;\n  padding: 24px 0;\n  text-decoration: none;\n  display: block;\n  border: 1px solid #f0f0f0;\n  background: white;\n  border-radius: 4px;\n  box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.1);\n  overflow-wrap: break-word;\n  overflow: hidden;\n}\n.sui-result + .sui-result {\n  margin-top: 32px;\n}\n.sui-result em {\n  position: relative;\n  color: #3a56e4;\n  font-weight: 700;\n  font-style: inherit;\n}\n.sui-result em:after {\n  content: \"\";\n  position: absolute;\n  top: -3px;\n  left: -3px;\n  width: calc(100% + 6px);\n  height: calc(100% + 6px);\n  background: rgba(37, 139, 248, 0.08);\n  pointer-events: none;\n}\n.sui-result__header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 24px;\n}\n.sui-result__title {\n  font-size: 1.8em;\n  font-weight: 400;\n  color: #333333;\n  text-decoration: none;\n}\n.sui-result__title-link {\n  color: #3a56e4;\n  text-decoration: none;\n}\n.sui-result__key {\n  font-family: monospace;\n  font-weight: 400;\n  font-size: 14px;\n  flex: 0 1 50%;\n  color: #777777;\n}\n.sui-result__key:before {\n  content: '\"';\n}\n.sui-result__key:after {\n  content: '\": ';\n}\n.sui-result__value {\n  font-weight: 400;\n  font-size: 14px;\n}\n.sui-result__version {\n  font-size: 12px;\n  display: inline;\n  vertical-align: bottom;\n}\n.sui-result__license {\n  font-size: 12px;\n  color: #999999;\n  display: inline-block;\n  border: 1px solid #ccc;\n  border-radius: 3px;\n  line-height: 1;\n  padding: 4px 4px 3px 4px;\n}\n.sui-result__body {\n  line-height: 1.5;\n  margin-top: 0;\n  display: flex;\n}\n.sui-result__body p {\n  margin: 0;\n}\n.sui-result__image {\n  padding-top: 12px;\n  padding-left: 24px;\n  flex-basis: 220px;\n}\n.sui-result__image img {\n  max-width: 100%;\n  display: block;\n  height: auto;\n}\n.sui-result__details {\n  flex: 1;\n  list-style: none;\n  padding: 12px 24px;\n  margin: 0;\n}\n\n.sui-results-container {\n  padding: 0;\n  list-style: none;\n}\n\n.sui-results-per-page {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  line-height: 1.5;\n  font-weight: 400;\n  color: #4a4b4b;\n  font-size: 12px;\n  display: flex;\n  align-items: center;\n  height: 100%;\n}\n.sui-results-per-page__label {\n  margin-right: 8px;\n}\n.sui-results-per-page .sui-select__control {\n  align-items: center;\n}\n.sui-results-per-page .sui-select__control input {\n  position: absolute;\n}\n\n.sui-search-box {\n  display: flex;\n  position: relative;\n  justify-content: center;\n  align-items: stretch;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n}\n.sui-search-box__submit {\n  flex-shrink: 0;\n  font-family: inherit;\n  font-size: 14px;\n  padding: 16px;\n  margin-left: 10px;\n  text-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px;\n  color: white;\n  border: none;\n  box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px inset, rgba(59, 69, 79, 0.05) 0px 1px 0px;\n  background: linear-gradient(#2da0fa, #3158ee) #2f7cf4;\n  cursor: pointer;\n  border-radius: 4px;\n}\n.sui-search-box__submit:hover {\n  box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 0px 1px inset, rgba(59, 69, 79, 0.3) 0px 2px 4px;\n  background: linear-gradient(#3cabff, #4063f0) #3d84f7;\n}\n.live-filtering .sui-search-box__submit {\n  display: none;\n}\n.sui-search-box__wrapper {\n  width: 100%;\n  height: 100%;\n  outline: none;\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n  border-radius: 3px;\n  position: relative;\n}\n.sui-search-box__text-input {\n  border-radius: 4px;\n  border: 1px solid #ccc;\n  padding: 16px;\n  outline: none;\n  position: relative;\n  font-family: inherit;\n  font-size: 14px;\n  width: 100%;\n}\n.sui-search-box__text-input:focus {\n  box-shadow: rgba(59, 69, 79, 0.3) 0px 2px 4px;\n  border-top: 1px solid #3a56e4;\n  border-left: 1px solid #3a56e4;\n  border-right: 1px solid #3a56e4;\n  border-bottom: 1px solid #3a56e4;\n}\n.autocomplete .sui-search-box__text-input {\n  box-shadow: rgba(59, 69, 79, 0.3) 0px 2px 4px;\n}\n.sui-search-box__autocomplete-container {\n  display: none;\n  flex-direction: column;\n  left: 0;\n  right: 0;\n  top: 110%;\n  margin: 0;\n  padding: 24px 0 12px 0;\n  line-height: 1.5;\n  background: white;\n  position: absolute;\n  box-shadow: rgba(59, 69, 79, 0.3) 0px 2px 4px;\n  border-top: 1px solid #ccc;\n  border-left: 1px solid #ccc;\n  border-right: 1px solid #ccc;\n  border-bottom: 1px solid #ccc;\n  border-top-left-radius: 4px;\n  border-top-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n  border-bottom-right-radius: 4px;\n}\n.autocomplete .sui-search-box__autocomplete-container {\n  display: flex;\n  z-index: 1;\n}\n.sui-search-box__autocomplete-container ul {\n  list-style: none;\n  margin: 0;\n  padding: 0 0 24px 0;\n  background: transparent;\n  border-radius: 3px;\n}\n.sui-search-box__autocomplete-container ul:last-child {\n  padding: 0;\n}\n.sui-search-box__autocomplete-container li {\n  margin: 0 12px;\n  font-size: 0.9em;\n  padding: 4px 12px;\n  border-radius: 4px;\n  color: #555;\n  cursor: default;\n}\n.sui-search-box__autocomplete-container li em {\n  font-style: normal;\n  color: #3a56e4;\n  background: #edf0fd;\n}\n.sui-search-box__autocomplete-container li:hover {\n  background: #3a56e4;\n  color: #ffffff;\n}\n.sui-search-box__autocomplete-container li:hover em {\n  background: transparent;\n  color: #ffffff;\n}\n.sui-search-box__autocomplete-container li[aria-selected=true] {\n  background: #3a56e4;\n  color: #ffffff;\n}\n.sui-search-box__autocomplete-container li[aria-selected=true] em {\n  background: transparent;\n  color: #ffffff;\n}\n.sui-search-box__section-title {\n  color: #888;\n  font-size: 0.7em;\n  letter-spacing: 1px;\n  font-weight: normal;\n  padding: 0 0 4px 24px;\n  text-transform: uppercase;\n}\n\n.sui-sorting {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  line-height: 1.5;\n  font-weight: 400;\n  display: inline-block;\n  width: 100%;\n}\n.sui-sorting__label {\n  font-size: 12px;\n  color: #8b9bad;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n}\n\n.sui-select {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;\n  line-height: 1.5;\n  font-weight: 400;\n  font-size: 0.875rem;\n  margin-top: 8px;\n  width: 100%;\n}\n.sui-select--inline {\n  margin-top: 0;\n}\n.sui-select--is-disabled {\n  opacity: 0.5;\n}\n.sui-select__control {\n  background-color: #f8f8f8;\n  border: 1px solid #a6a6a6;\n  border-radius: 4px;\n  display: flex;\n  align-items: stretch;\n  justify-content: space-between;\n}\n.sui-select__control--is-focused {\n  border: 1px solid #3a56e4;\n}\n.sui-select__value-container {\n  padding-top: 0;\n  padding-bottom: 0;\n}\n.sui-select__value-container--has-value {\n  font-weight: 700;\n  color: #333333;\n}\n.sui-select__placeholder {\n  white-space: nowrap;\n  position: static;\n  transform: none;\n  color: #333333;\n}\n.sui-select__dropdown-indicator {\n  display: flex;\n  height: 32px;\n  width: 32px;\n  align-items: center;\n  justify-content: center;\n}\n.sui-select__option-count {\n  color: #888888;\n  font-size: 0.8em;\n}\n.sui-select__option-label {\n  color: #4f4f4f;\n}\n.sui-select__option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 400;\n  padding: 8px 12px;\n  cursor: pointer;\n}\n.sui-select__option--is-selected {\n  color: #333333;\n  background: #ffffff;\n  font-weight: 700;\n}\n.sui-select__option--is-selected .sui-search-select__option-label {\n  color: #333333;\n  position: relative;\n}\n.sui-select__option:hover {\n  background: #f8f8f8;\n}\n"], "names": [], "sourceRoot": ""}