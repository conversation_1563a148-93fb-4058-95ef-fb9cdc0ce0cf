const YUNOCategories = (function($) {
    
    const categories = function() {
        Vue.component('yuno-categories', {
            props: ["data"],
            template: `
                <section id="yunoCategories" class="yunoCategories">
                    <div class="container">
                        <h1 class="sectionTitle">{{data.title}}</h1>
                        <div class="row categoryWrapper">
                            <div 
                                v-for="(category, categoryIndex) in data.data.slice(0, 3)"
                                :key="categoryIndex" 
                                class="col-12 col-md-12"
                                :class="setColumns">
                                <article class="categoryCard">
                                    <figure class="cardImg">
                                        <img width="348" height="196" :src="category.image + imgParameter" :alt="category.name">
                                    </figure>
                                    <div class="cardBody">
                                        <h2 class="cardTitle">{{category.name}}</h2>
                                        <p class="cardDescription">{{setDescription(category.desciption)}}</p>
                                    </div>
                                    <div class="cardFooter">
                                        <b-button tag="a"
                                            :href="category.url"
                                            class="yunoPrimaryCTA wired small">
                                            <span class="makeIndent">To explore our {{category.name}} channel </span>Learn more
                                        </b-button>
                                    </div>
                                </article>
                            </div>
                        </div>
                    </div>
                </section>
            `,
            computed: {
                imgParameter() {
                    return YUNOCommon.config.addVerion(true)
                },
                setColumns: {
                    get() {
                        let grid = "";
                
                        switch (this.$props.data.data.length) {
                            case 2:
                                grid = "col-lg-6";
                                break;
                            case 3:
                                grid = "col-lg-4";
                                break;
                            case 4:
                                grid = "col-lg-4";
                                break;
                            default:
                                // For lengths greater than 4
                                if (this.$props.data.data.length > 4) {
                                    grid = "col-lg-4";
                                }
                                break;
                        }
                
                        return grid;
                    }
                }
            },
            methods: {
                removeTagsAndTruncate(input, maxLength = 200) {
                    // Replace closing tags with a space
                    const stringWithSpaces = input.replace(/<\/[^>]+>/g, " ");

                     // Remove all remaining tags
                    const stringWithoutTags = stringWithSpaces.replace(/<[^>]+>/g, "");
                  
                    if (stringWithoutTags.length > maxLength) {
                      return stringWithoutTags.substr(0, maxLength) + "...";
                    }
                  
                    return stringWithoutTags;
                },
                setDescription(str) {
                    return this.removeTagsAndTruncate(str)
                },
            }
        });
    };

    return {
        categories: categories
    };
})(jQuery);

