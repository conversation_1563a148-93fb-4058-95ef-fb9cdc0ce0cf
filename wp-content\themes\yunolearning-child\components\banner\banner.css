.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.dark87, #app .banner, #app .banner .wrapper .yunoWhiteCTA {
  color: rgba(0, 0, 0, 0.87);
}

.dark60 {
  color: rgba(0, 0, 0, 0.6);
}

#app .banner {
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .banner {
    padding: 100px 0 0;
  }
}

#app .banner .container {
  background-color: #A81E22;
  border-radius: 0;
  padding: 15px;
  text-align: center;
}

@media (min-width: 768px) {
  #app .banner .container {
    border-radius: 4px;
    padding: 30px;
    text-align: left;
  }
}

#app .banner .wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  color: white;
}

#app .banner .wrapper h2 {
  font-size: 48px;
  line-height: 62px;
  font-weight: 700;
  margin-bottom: 10px;
}

#app .banner .wrapper small {
  font-size: 24px;
  line-height: 28px;
  font-weight: 700;
  margin-bottom: 0;
}

#app .banner .wrapper .yunoWhiteCTA {
  height: 56px;
  font-size: 16px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
  padding-left: 30px;
  padding-right: 30px;
}

#app .banner .wrapper .yunoWhiteCTA:hover {
  text-decoration: none;
  color: #A81E22;
}

#app .banner .lftCol {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

@media (min-width: 768px) {
  #app .banner .lftCol {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
}

#app .banner .ritCol {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-top: 15px;
}

@media (min-width: 768px) {
  #app .banner .ritCol {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
}
/*# sourceMappingURL=banner.css.map */