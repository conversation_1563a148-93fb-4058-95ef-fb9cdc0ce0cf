#app .classCard {
  border: 1px solid #E6E6E6;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 30px;
}

#app .classCard:last-child {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  #app .classCard {
    padding: 30px;
  }
}

#app .classCard .classID {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  background-color: #E6E6E6;
  font-size: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 5px 15px;
  border-radius: 20px;
  margin-top: 10px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

#app .classCard .classID .idField {
  position: absolute;
  left: -99999px;
}

#app .classCard .classID .idLabel {
  padding-right: 5px;
  font-weight: 500;
}

#app .classCard .classID .material-icons {
  font-size: 14px;
  padding-left: 5px;
}

#app .classCard .actionList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 15px;
}

@media (min-width: 768px) {
  #app .classCard .actionList {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

#app .classCard .actionList li {
  width: 36px;
  height: 36px;
  margin-right: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  cursor: pointer;
}

#app .classCard .actionList li:last-child {
  margin-right: 0;
}

#app .classCard .actionList li .fa {
  font-size: 18px;
}

#app .classCard .classSchedule {
  margin-right: 15px;
  text-align: center;
}

@media (min-width: 768px) {
  #app .classCard .classSchedule {
    margin-right: 30px;
  }
}

#app .classCard .classSchedule .date {
  font-size: 32px;
  font-weight: 600;
  line-height: 32px;
}

#app .classCard .classSchedule .month {
  color: rgba(0, 0, 0, 0.5);
  font-size: 18px;
  text-transform: uppercase;
  margin-bottom: 10px;
  line-height: 18px;
}

#app .classCard .classSchedule .year {
  background-color: rgba(0, 0, 0, 0.1);
  font-size: 14px;
  border-radius: 3px;
  display: inline-block;
  padding: 3px 8px;
}

@media (min-width: 768px) {
  #app .classCard .classSchedule.noMD-LG {
    display: none;
  }
}

#app .classCard .classSchedule.noSM {
  display: none;
}

@media (min-width: 768px) {
  #app .classCard .classSchedule.noSM {
    display: block;
  }
}

#app .classCard .classIntro {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .classCard .classIntro .wrapper {
  padding-bottom: 15px;
}

@media (min-width: 768px) {
  #app .classCard .classIntro .wrapper {
    padding-bottom: 0;
  }
}

#app .classCard .classIntro .classTitle {
  color: #000;
  font-size: 18px;
}

#app .classCard .classIntro .classTitle a {
  color: #000;
}

#app .classCard .classIntro .classType {
  line-height: 14px;
  background: #edc264;
  display: inline-block;
  text-transform: uppercase;
  padding: 2px 10px;
  border-radius: 30px;
  font-size: 11px;
}

#app .classCard .classMeta {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 75%;
          flex: 0 0 75%;
}

#app .classCard .classMeta li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  margin-bottom: 5px;
}

#app .classCard .classMeta li:last-child {
  margin-bottom: 0;
}

#app .classCard .classMeta li .caption {
  color: rgba(0, 0, 0, 0.5);
  padding-right: 10px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 33%;
          flex: 0 0 33%;
}

@media (min-width: 768px) {
  #app .classCard .classMeta {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
  #app .classCard .classMeta li .caption {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
}

#app .classCard .classMetaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  #app .classCard .classMetaWrapper {
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
}

#app .classCard .classMetaWrapper .ctaWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding-top: 15px;
}

@media (min-width: 768px) {
  #app .classCard .classMetaWrapper .ctaWrapper {
    padding-top: 0;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
}

#app .classCard .classIntroWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoModal.learnerDashboard .modalMsg {
  color: #000;
  margin-bottom: 15px;
}

#app .yunoModal.learnerDashboard .classFields > li {
  font-size: 16px;
  line-height: normal;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 15px;
}

#app .yunoModal.learnerDashboard .classFields > li .listSubtitle {
  margin-bottom: 10px;
  font-size: 14px;
}

#app .yunoModal.learnerDashboard .classFields > li .caption {
  font-weight: 500;
  color: #000;
  display: block;
  margin-bottom: 5px;
}

#app .yunoModal.learnerDashboard .classFields > li:last-child {
  margin-bottom: 0;
}

#app .yunoModal.learnerDashboard .classFields > li .selectedLearners {
  margin-top: 10px;
  max-height: 245px;
  overflow-y: auto;
}

#app .yunoModal.learnerDashboard .classFields > li .selectedLearners li {
  padding: 5px 10px;
  font-weight: 400;
}

#app .yunoModal.learnerDashboard .classFields > li .selectedLearners li .caption {
  font-weight: 400;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
}

#app .yunoModal.learnerDashboard .classFields > li .clipboard {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
}

#app .yunoModal.learnerDashboard .classFields > li .clipboard .control {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 85%;
          flex: 0 0 85%;
}

#app .yunoModal.learnerDashboard .classFields > li .clipboard .trigger {
  margin-left: 15px;
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
/*# sourceMappingURL=classCard.css.map */