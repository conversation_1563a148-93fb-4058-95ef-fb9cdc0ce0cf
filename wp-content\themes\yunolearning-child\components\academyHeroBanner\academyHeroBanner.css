#app .academyHeroBanner {
  padding: 45px 0;
  color: white;
  background-size: cover;
  background-position: center center;
  position: relative;
}

#app .academyHeroBanner .container {
  z-index: 3;
}

#app .academyHeroBanner::before {
  content: "";
  position: absolute;
  z-index: 2;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(29, 29, 31, 0.6);
}

#app .academyHeroBanner .title {
  font-size: 48px;
  line-height: 62px;
  font-weight: 700;
  margin-bottom: 10px;
  color: white;
}

#app .academyHeroBanner .categories {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .academyHeroBanner .categories li {
  margin-right: 3px;
  font-size: 14px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
}

#app .academyHeroBanner .categories li.label {
  margin-right: 5px;
}

#app .academyHeroBanner .description {
  padding: 30px 0 0;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
  margin-bottom: 15px;
}

#app .academyHeroBanner .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
/*# sourceMappingURL=academyHeroBanner.css.map */