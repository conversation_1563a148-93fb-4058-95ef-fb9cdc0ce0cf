.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yuno-icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

#app .dark87 {
  color: rgba(0, 0, 0, 0.87);
}

#app .dark60 {
  color: rgba(0, 0, 0, 0.6);
}

#app .accordionMedia {
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .accordionMedia {
    padding: 60px 0 40px 0;
  }
}

#app .accordionMedia .collapse:not(.show) {
  display: block;
}

#app .accordionMedia .accordian {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  padding-bottom: 10px;
  width: 100%;
}

@media (min-width: 768px) {
  #app .accordionMedia .accordian {
    width: 70%;
  }
}

#app .accordionMedia .accordian .card-header {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 1rem 0 0.3rem 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: transparent;
  border: none;
  cursor: pointer;
}

#app .accordionMedia .accordian .card-header .card-header-icon {
  -webkit-box-ordinal-group: 0;
      -ms-flex-order: -1;
          order: -1;
  margin-right: 1rem;
  padding: 0;
  text-decoration: none;
}

#app .accordionMedia .accordian .card-header .card-header-icon:hover {
  text-decoration: none;
}

#app .accordionMedia .accordian .card-header .card-header-title {
  margin: 0;
  padding: 0;
}

#app .accordionMedia .accordian .card-content {
  padding: 0 0 0.5rem 2.5rem;
}

#app .accordionMedia .accordian .card-content .content {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
  line-height: 1.5;
}

#app .accordionMedia .accordian:last-child {
  border-bottom: none;
}
/*# sourceMappingURL=accordionMedia.css.map */