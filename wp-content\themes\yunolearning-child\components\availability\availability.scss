@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";
@import "../../assets/scss/icons";

#app {
    .hoursWrapper {
		border: 1px solid $grey;
		border-radius: 4px;
		padding: $gap15 $gap15 0;

        .item {
			display: flex;
			align-items: center;
			margin-bottom: $gap15;
			min-height: 55px;
			border-bottom: 1px solid $grey;
			padding-bottom: $gap15;
			flex-wrap: wrap;

			&:last-child {
				border-bottom: 0;
				padding-bottom: 0;
			}

			.itemWrapper {
				display: flex;
				flex-wrap: wrap;	
				flex: 0 0 100%;

				@media (min-width: 768px) {
					flex: 0 0 30%;
				}		
			}

			.error {
				flex: 0 0 100%;
			}

            .day {
                flex: 0 0 100%;
				font-weight: 500;

				@media (min-width: 768px) {
					flex: 0 0 125px;
				}	
            }

            .slots {
                display: flex;
				flex: 0 0 100%;
				margin-bottom: $gap15;
				margin-top: $gapSmaller;

				@media (min-width: 768px) {
					flex: 0 0 auto;
					margin-bottom: 0;
					margin-top: 0;
				}

				.field {
					margin-bottom: 0;

					.switch {
						margin: 0;
					}
				}
            }

			.button {
				&.iconOnly {
					font-size: 20px;
					margin-left: $gap15;
				}

				&.noBG {
					background: none;
					color: $primaryCopyColor;
				}

				&.addSlot {
					padding-left: 0;
					padding-right: 0;
					padding-bottom: 0;
					font-size: $fontSizeSmaller;
					color: $primaryColor;
					text-decoration: underline;
				}

				.material-icons-outlined {
					font-size: 18px;
					position: relative;
					top: 2px;
				}
			}
        }

		.addSlotWrapper {
			flex: 0 0 100%;
			margin-top: 15px;
		}

        .hours {
			flex: 0 0 100%;

			@media (min-width: 768px) {
				flex: 0 0 70%;
			}	

			.hourWrapper {
				display: flex;
				align-items: flex-start;
				margin-bottom: $gap15;
				flex-wrap: wrap;
				position: relative;

				// &:last-child {
				// 	margin-bottom: 0;
				// }

				.chooseHour {
					&:first-child {
						margin-right: $gapLargest;
					}

					.hasError {
						padding-bottom: $gap15;
					}
				}
			}

			.fa-filter {
				font-size: $fontSizeLarger + 6;
				padding-right: $gap15;
			}

            .fa-minus {
                margin: 0 $gap15;
            }

			.button {
				&.is-primary {
					background-color: $whiteBG;
					color: $primaryCopyColor;
					@include setBorderColor($primaryCopyColor, 0.2);
					font-size: $fontSizeSmall;
					padding: $gapSmall - 2 $gap15;
                    width: 100%;
					justify-content: space-between;

					&:active, &:focus {
						box-shadow: none;
					}
				}

				.icon {
					.mdi {
						&.mdi-menu-down, &.mdi-menu-up {
							&:after {
								content: "\f078";
								@extend .fa;
							}
						}

						&.mdi-menu-up {
							&:after {
								content: "\f077";
							}
						}
					}
				}
				
			}


			.filterMenu {
				margin-bottom: $gap15;
				width: 150px;

				.dropdown-trigger {
					width: 100%;
				}

				@media (min-width: 768px) {
					margin-bottom: 0;
				}

				button {
					width: 100%;
					justify-content: space-between;

					@media (min-width: 768px) {
						width: auto;
						justify-content: center;
					}

					> span {
						text-transform: capitalize;
					}
				}
				.dropdown-content {
					box-shadow: none;
					border:0;
					max-height: 300px;
					overflow: hidden;
					overflow-y: auto;

					a {
						@include setFontColor($primaryCopyColor, 0.5);

						&.is-active {
							background: none;
							@include setFontColor($primaryCopyColor, 1);
						}

						&:active, &:focus {
							background: none;
							outline: none;
						}
					}
				}

				& + .error {
					display: none;
					color: red;
					font-size: $fontSizeSmaller;
					padding-top: $gapSmaller;
				}
				
				& + .filterMenu {
					margin: 0 0 $gap15 0;

					@media (min-width: 768px) {
						margin: 0 0 0 $gap15;
					}
				}

				&.invalid {
					.button {
						&.is-primary {
							border-color: red;
						}
					}

					& + .error {
						display: block;
						position: absolute;
						left: 0;
						bottom: -8px;
					}
				}
			}
		}

        .filterMenu {
            flex: 0 0 100%;
            margin-bottom: $gap15;

            .dropdown-trigger {
                width: 100%;
            }

            @media (min-width: 768px) {
                flex: 0 0 auto;
                margin-bottom: 0;
            }

            button {
                width: 100%;
                justify-content: space-between;

                @media (min-width: 768px) {
                    width: auto;
                    justify-content: center;
                }

                > span {
                    text-transform: capitalize;
                }
            }
            .dropdown-content {
                box-shadow: none;
                border:0;
                max-height: 300px;
                overflow: hidden;
                overflow-y: auto;

                a {
                    @include setFontColor($primaryCopyColor, 0.5);

                    &.is-active {
                        background: none;
                        @include setFontColor($primaryCopyColor, 1);
                    }

                    &:active, &:focus {
                        background: none;
                        outline: none;
                    }
                }
            }

            &.selectColumns {
                @media (min-width: 768px) {
                    margin-left: auto;

                    .dropdown-menu {
                        left: auto;
                        right: 0;
                    }
                }
                
                .dropdown-content {
                    a {
                        padding: 6px 15px 6px 30px;
                        position: relative;

                        &:before {
                            content: "unCheck";
                            @extend .ylIcon;
                            position: absolute;
                            left: 9px;
                            top: 10px;
                        }

                        &.is-active {
                            &:before {
                                content: "checked";
                            }
                        }
                    }
                }
            }

            & + .filterMenu {
                margin: 0 0 $gap15 0;

                @media (min-width: 768px) {
                    margin: 0 0 0 $gap15;
                }

                &.selectColumns {
                    @media (min-width: 768px) {
                        margin-left: auto;

                        .dropdown-menu {
                            left: auto;
                            right: 0;
                        }
                    }	
                }
            }
        }
    }

	.legends {
		display: flex;
		justify-content: flex-end;
		margin-bottom: $gap15;

		li {
			font-size: $fontSizeSmall;
			margin-right: $gap15;
			padding-left: $gapLargest;
			position: relative;

			&:last-child {
				margin-right: 0;
			}

			&::before {
				content: "";
				width: 20px;
				height: 20px;
				position: absolute;
				left: 0;
				top: 0;
			}

			&.yes {
				&::before {
					background-color: #54ac75;
				}
			}

			&.no {
				&::before {
					@include setBGColor($primaryCopyColor, 0.2);
				}
			}
		}
	}

	.yunoTable {
		&.container {
			padding: 0;
		}

		.b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer):hover {
			background: #FFF;
		}
	}

	.availabilityWrapper {
		.ctaWrapper {
			margin: $gapLargest 0 $gapLargest;
	
			.button {
				margin-right: $gap15;
	
				&:last-child {
					margin-right: 0;	
				}
			}
		}
	}

	

	.yunoModal {
		&.preview {
			.modal-close {
				display: block !important;
			}
			.modal-content {
				padding: $gap15;

				@media (min-width: 768px) {
					padding: $gapLargest;
				}		
			}

			.field {
				&.table-mobile-sort {
					display: none;
				}
			}

			.yunoTable {
				min-height: 150px; 
			}

			.b-table {
				.level {
					display: none;
				}
				thead {
					tr {
						th {
							@media (min-width: 768px) {
								border-bottom: 1px solid #E6E6E6;
							}	
						}
					}
				}
				tbody {
					tr {
						td {
							@media (min-width: 768px) {
								border-top: 0;
							}	
						}
					}
				}
			}
		}
	}
}