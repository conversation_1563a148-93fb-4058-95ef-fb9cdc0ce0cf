@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.dark87 {
    @include setFontColor($primaryCopyColor, 0.87);
}

.dark60 {
    @include setFontColor($primaryCopyColor, 0.6);
}

#app {
    .articleBlock {
        padding: $gapLargest 0;
        @extend .dark87;

        &.noTopGap {
            padding-top: 0;
        }

        &.light {
            color: $secondaryCopyColor;
        }

        &.center {
            text-align: center;
        }

        &.btmMargin {
            margin-bottom: $gapLargest;
        }

        &.englishSpeaking, &.requestSuccess {
            .articleDescription {
                font-size: $fontSizeLarge;
            }
        }

        &.requestSuccess {
            .articleDescription {
                margin-bottom: $gap15;
            }
        }

        .articleTitle {
            font-size: $headline3;
            font-weight: 500;
            margin-bottom: $gap15;
            line-height: 40px;

            &.noBold {
                font-weight: normal;
            }
        }

        .articleDescription {
            font-size: $fontSizeLarger + 6;
            margin-bottom: 0;
        }

        .embedDescription {
            margin-top: $gapSmall;

            &.btmMargin {
                margin-bottom: $gapLargest;
            }
            
            h2 {
                font-size: $fontSizeLargest;
                margin-bottom: $gap15;
                @include setFontColor($primaryCopyColor, 1);
            }

            h3 {
                font-size: $fontSizeLarger;
                margin-bottom: $gap15;
                @include setFontColor($primaryCopyColor, 1);
            }

            h4 {
                font-size: $fontSizeLarge;
                margin-bottom: $gap15;
                @include setFontColor($primaryCopyColor, 1);
            }

            p {
                font-size: $body1;
                margin-bottom: $gapLargest;
                @include setFontColor($primaryCopyColor, 1);
            }

            ul, ol {
                padding: 0;
                margin: 0 0 $gap15 18px;

                li {
                    list-style: disc outside;
                    margin-bottom: $gapSmaller;
                    font-size: $fontSizeLarge;
                    @include setFontColor($primaryCopyColor, 1);

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }

            ol {
                li {
                    list-style: decimal outside;
                }
            }
        }

        .videoLPPlayer {
            position: relative;
            padding-bottom: 37.7%;
            overflow: hidden;
            max-width: 100%;
            min-height: 250px;
            margin: $gapLargest auto;
            
            @media (min-width: 768px) {
                min-height: 300px;
                margin-top: 0;
                max-width: 70%;
            }

            iframe,
            object,
            embed {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
        }

        &.hasVideo {
            .articleTitle {
                margin-bottom: $gapLargest;
            }

            .embedDescription {
                text-align: left;
            }
        }

        @media (min-width: 768px) {
            padding: $gapLargest * 2 0;

            &.btmMargin {
                margin-bottom: $gapLargest * 2;
            }
        }
    }
}