.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon, #app .carouselSlide .slide .cardContent:before {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

#app .carouselCards {
  padding: 30px 0;
}

#app .carouselCards.noTopGap {
  padding-top: 0;
}

#app .carouselCards .ctaWrapper {
  text-align: center;
}

@media (min-width: 768px) {
  #app .carouselCards {
    padding: 60px 0;
  }
  #app .carouselCards.noTopGap {
    padding-top: 0;
  }
}

#app .carouselCards.cardListEmpty {
  padding: 0;
}

#app .carouselCards .videoLPPlayer {
  position: relative;
  padding-bottom: 35.7%;
  overflow: hidden;
  max-width: 100%;
  min-height: 250px;
  margin-top: 30px;
}

@media (min-width: 768px) {
  #app .carouselCards .videoLPPlayer {
    min-height: 394px;
    width: 62%;
    margin: 30px auto;
  }
}

#app .carouselCards .videoLPPlayer iframe,
#app .carouselCards .videoLPPlayer object,
#app .carouselCards .videoLPPlayer embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#app .carouselCards .sliderWrapper {
  position: relative;
}

#app .carouselCards .sliderWrapper .slidePlayer {
  position: relative;
  padding-bottom: 37.7%;
  overflow: hidden;
  max-width: 100%;
  min-height: 250px;
  margin: 30px auto;
}

@media (min-width: 768px) {
  #app .carouselCards .sliderWrapper .slidePlayer {
    min-height: 300px;
    margin-top: 0;
    max-width: 100%;
  }
}

#app .carouselCards .sliderWrapper .slidePlayer iframe,
#app .carouselCards .sliderWrapper .slidePlayer object,
#app .carouselCards .sliderWrapper .slidePlayer embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#app .carouselCards .carouselCardTitle {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
}

#app .carouselCards .carouselListControls {
  position: absolute;
  top: calc(50% - 25px);
  width: 100%;
  left: 0;
  display: none;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (min-width: 768px) {
  #app .carouselCards .carouselListControls {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}

#app .carouselCards .carouselListControls .prev, #app .carouselCards .carouselListControls .next {
  position: relative;
  left: 0;
  z-index: 5;
  border: 0;
  background-color: #002F5A;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 50px;
  height: 50px;
}

#app .carouselCards .carouselListControls .prev .fa, #app .carouselCards .carouselListControls .next .fa {
  color: #FFF;
}

@media (min-width: 768px) {
  #app .carouselCards .carouselListControls .prev, #app .carouselCards .carouselListControls .next {
    left: 14%;
  }
}

#app .carouselCards .carouselListControls .next {
  right: 0;
  left: auto;
}

@media (min-width: 768px) {
  #app .carouselCards .carouselListControls .next {
    right: 14%;
  }
}

#app .carouselCards .tns-outer .tns-liveregion {
  display: none;
}

#app .carouselCards .tns-outer button[data-action="stop"], #app .carouselCards .tns-outer button[data-action="start"] {
  display: none;
}

#app .carouselCards .tns-inner {
  overflow: hidden;
}

#app .carouselSlide {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .carouselSlide .slide {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0 0;
}

@media (min-width: 768px) {
  #app .carouselSlide .slide {
    padding: 38px 0;
  }
}

#app .carouselSlide .slide .cardItem {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  -webkit-box-shadow: rgba(0, 0, 0, 0.117647) 0 0 40px;
          box-shadow: rgba(0, 0, 0, 0.117647) 0 0 40px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 30px 30px;
}

@media (min-width: 768px) {
  #app .carouselSlide .slide .cardItem {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 69%;
            flex: 0 0 69%;
    padding: 30px 60px;
  }
}

#app .carouselSlide .slide .cardItem .slideInnerWrapper.hasVideo {
  width: 100%;
}

#app .carouselSlide .slide .cardContent {
  font-family: "Roboto Slab";
  font-size: 18px;
  font-weight: 300;
  text-align: center;
  position: relative;
  margin-bottom: 60px;
}

#app .carouselSlide .slide .cardContent:before {
  content: "quotes";
  font-size: 24px;
  display: block;
  color: rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

#app .carouselSlide .slide .authorImg {
  margin-bottom: 15px;
}

#app .carouselSlide .slide .authorImg img {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  display: block;
  border-radius: 50%;
  overflow: hidden;
}

#app .carouselSlide .slide .cardName {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  color: #000;
}

#app .carouselSlide .slide .cardName a {
  color: #000;
}

#app .carouselSlide .slide .cardName .cardDate {
  display: block;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.4);
  padding-top: 5px;
}
/*# sourceMappingURL=carouselCards.css.map */