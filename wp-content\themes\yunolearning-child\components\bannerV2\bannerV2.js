Vue.component('yuno-banner-v2', {
    props: ["data", "options"],
    template: `
        <section class="bannerV2">
            <div class="container">
                <div class="row">
                    <div class="col-md-8 offset-md-2 wrapper">
                        <div class="lftCol">
                            <h2>
                                {{ data.title }}
                            </h2>
                        </div>
                        <div class="ritCol">
                            <b-button tag="a"
                                :href="data.cta.disabled ? '#' : data.cta.url"
                                :target="data.cta.target"
                                :disabled="data.cta.disabled"
                                @click="onPrimaryCTAClick()"
                                class="yunoPrimaryCTA">
                                {{ data.cta.label }}
                            </b-button>
                            <div class="secondaryCTA" v-if="data.secondaryCTA !== undefined">
                                <p class="smallerBody">{{ data.secondaryCTA.helperText }}</p>
                                <a :href="data.secondaryCTA.url" class="plainLink" target="_blank">{{ data.secondaryCTA.label }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onPrimaryCTAClick() {
            this.$emit('onPrimaryCTAClick');
        },
    }
});