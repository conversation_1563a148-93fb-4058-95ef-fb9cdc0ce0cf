#app .yunoBlocks h2 {
  font-size: 32px;
  margin: 0 0 16px;
  font-weight: 400;
}

#app .yunoBlocks h2 small {
  display: block;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
  padding-top: 10px;
}

#app .yunoBlocks p {
  font-size: 14px;
  margin-bottom: 0;
}

#app .yunoBlocks .blockImg {
  margin-bottom: 16px;
}

#app .yunoBlocks .blockImg img {
  width: 100%;
  height: auto;
}

#app .yunoBlocks .blockImg.withShadow {
  -webkit-box-shadow: 2px 1px 16px 4px #ccc;
          box-shadow: 2px 1px 16px 4px #ccc;
}

#app .yunoBlocks .blockImg.withBorder {
  border-radius: 10px;
  border: 8px solid #eaeaea;
}

@media (min-width: 768px) {
  #app .yunoBlocks .blockImg {
    margin-bottom: 0;
  }
}

#app .yunoBlocks .row {
  padding-bottom: 60px;
}
/*# sourceMappingURL=blocks.css.map */