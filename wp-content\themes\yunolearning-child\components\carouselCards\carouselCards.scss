@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";


.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.ylIcon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'yuno-icon' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    
    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
    font-family: 'Material Icons Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
  
  .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

#app {
    .carouselCards {
        padding: $gapLargest 0;

        &.noTopGap {
            padding-top: 0;
        }

        .ctaWrapper {
            text-align: center;
        }

        @media (min-width: 768px) {
            padding: $gapLargest * 2 0;

            &.noTopGap {
                padding-top: 0;
            }
        }

        &.cardListEmpty {
            padding: 0;
        }

        .videoLPPlayer {
            position: relative;
            padding-bottom: 35.7%;
            overflow: hidden;
            max-width: 100%;
            min-height: 250px;
            margin-top: $gapLargest;
            
            @media (min-width: 768px) {
                min-height: 394px;
                width: 62%;
                margin: $gapLargest auto;
            }

            iframe,
            object,
            embed {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
        }

        .sliderWrapper {
            position: relative;

            .slidePlayer {
                position: relative;
                padding-bottom: 37.7%;
                overflow: hidden;
                max-width: 100%;
                min-height: 250px;
                margin: $gapLargest auto;
                
                @media (min-width: 768px) {
                    min-height: 300px;
                    margin-top: 0;
                    max-width: 100%;
                }
    
                iframe,
                object,
                embed {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .carouselCardTitle {
            font-size: $fontSizeLargest;
            font-weight: 600;
            margin-bottom: $gap15;
            text-align: center;
        }

        .carouselListControls {
            position: absolute;
            top: calc(50% - 25px);
            width: 100%;
            left: 0;
            display: none;
            justify-content: space-between;

            @media (min-width: 768px) {
                display: flex;
            }

            .prev, .next {
                position: relative;
                left: 0;
                z-index: 5;
                border: 0;
                background-color: $primaryColor;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 50px;
                height: 50px;

                .fa {
                    color: $secondaryCopyColor;
                }

                @media (min-width: 768px) {
                    left: 14%;
                }
            }

            .next {
                right: 0;
                left: auto;

                @media (min-width: 768px) {
                    right: 14%
                }
            }

            
        };

        .tns-outer {
            .tns-liveregion {
                display: none;
            }
            button[data-action="stop"], button[data-action="start"] {
                display: none;
            }
        }
        
        .tns-inner {
            overflow: hidden;
        }
    }

    .carouselSlide {
        display: flex;

        .slide {
            display: flex;
            justify-content: center;
            padding: 0 0;

            @media (min-width: 768px) {
                padding: $gapLargest + 8 0;
            }

            .cardItem {
                flex: 0 0 100%;
                // min-height: 350px;
                // flex-direction: column;
                box-shadow: rgba(0,0,0,.117647) 0 0 40px;
                border-radius: 4px;
                display: flex;
                // justify-content: space-between;
                align-items: center;
                box-sizing: border-box;
                padding: $gapLargest $gapLargest;

                @media (min-width: 768px) {
                    flex: 0 0 69%;
                    padding: $gapLargest $gapLargest * 2;
                }

                .slideInnerWrapper {
                    &.hasVideo {
                        width: 100%;
                    }
                }
            }

            .cardContent {
                font-family: "Roboto Slab";
                font-size: $fontSizeLarger;
                font-weight: 300;
                text-align: center;
                position: relative;
                margin-bottom: $gapLargest * 2;
                
                &:before {
                    content: "quotes";
                    @extend .ylIcon;
                    font-size: 24px;
                    display: block;
                    @include setFontColor($primaryCopyColor, 0.1);
                    margin-bottom: $gapLargest;
                }
            }

            .authorImg {
                margin-bottom: $gap15;

                img {
                    width: 60px;
                    height: 60px;
                    margin: 0 auto;
                    display: block;
                    border-radius: 50%;
                    overflow: hidden;
                }
            }

            .cardName {
                font-size: $fontSizeLarge;
                font-weight: 500;
                text-align: center;
                color: $primaryCopyColor;

                a {
                    color: $primaryCopyColor;    
                }

                .cardDate {
                    display: block;
                    font-weight: normal;
                    @include setFontColor($primaryCopyColor, 0.4);
                    padding-top: $gapSmaller;
                }
            }
        }
    }
}