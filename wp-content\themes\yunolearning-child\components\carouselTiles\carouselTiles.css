#app .carouselTiles {
  padding: 30px 0;
  background-color: rgba(0, 47, 90, 0.05);
}

#app .carouselTiles.noTopGap {
  margin-top: 0;
}

#app .carouselTiles.noBottomGap {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  #app .carouselTiles {
    padding: 60px 0;
    margin: 60px 0;
  }
  #app .carouselTiles.noTopGap {
    margin-top: 0;
  }
}

#app .carouselTiles .carouselTilesTitle {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 30px;
  text-align: center;
}

#app .carouselTiles .tns-outer .tns-liveregion {
  display: none;
}

#app .carouselTiles .tns-outer button[data-action="stop"], #app .carouselTiles .tns-outer button[data-action="start"] {
  display: none;
}

#app .carouselTiles .tns-inner {
  overflow: hidden;
  margin: 0 -15px;
}

#app .carouselTiles .carouselTilesControls {
  display: none;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 30px 0 0;
}

@media (min-width: 768px) {
  #app .carouselTiles .carouselTilesControls {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}

#app .carouselTiles .carouselTilesControls .prev, #app .carouselTiles .carouselTilesControls .next {
  border: 0;
  background-color: #002F5A;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 40px;
  height: 40px;
}

#app .carouselTiles .carouselTilesControls .prev .fa, #app .carouselTiles .carouselTilesControls .next .fa {
  color: #FFF;
}

#app .carouselTiles .carouselTilesControls .prev:disabled, #app .carouselTiles .carouselTilesControls .next:disabled {
  background-color: rgba(0, 47, 90, 0.2);
}

#app .carouselTiles .carouselTilesControls .prev {
  margin-right: 15px;
}

#app .carouselTiles .meetInstructors {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .carouselTiles .meetInstructors .slide {
  padding: 0 15px;
}

#app .carouselTiles .meetInstructors .cardItem {
  background-color: #FFF;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 15px;
}

#app .carouselTiles .meetInstructors .cardItem a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: 170px;
}

#app .carouselTiles .meetInstructors .cardItem a:hover {
  text-decoration: none;
}

#app .carouselTiles .meetInstructors .cardItem a:hover h3 {
  color: #a62027;
}

#app .carouselTiles .meetInstructors .cardImg {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
}

#app .carouselTiles .meetInstructors .cardImg img {
  border-radius: 50%;
  overflow: hidden;
  height: 90px;
  width: 90px;
  border: 2px solid #FFF;
  -webkit-box-shadow: rgba(0, 0, 0, 0.117647) 0 0 40px;
          box-shadow: rgba(0, 0, 0, 0.117647) 0 0 40px;
}

#app .carouselTiles .meetInstructors .cardImg figcaption {
  width: 100%;
}

#app .carouselTiles .meetInstructors .cardImg .ratingWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 74%;
  margin: 0 auto;
}

#app .carouselTiles .meetInstructors .cardImg .ratingWrapper.alignC {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .carouselTiles .meetInstructors .cardImg .ratingWrapper .total {
  color: #002F5A;
}

#app .carouselTiles .meetInstructors .cardImg .ratingWrapper .vue-star-rating {
  position: relative;
  top: -2px;
}

@media (min-width: 768px) {
  #app .carouselTiles .meetInstructors .cardImg .ratingWrapper {
    width: 100%;
  }
}

#app .carouselTiles .meetInstructors .cardImg h3 {
  font-size: 18px;
  color: #002F5A;
  text-align: center;
}
/*# sourceMappingURL=carouselTiles.css.map */