Vue.component("yuno-accordion-media", {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  template: `
        <section class="accordionMedia">
            <div class="container">
                <h2 class="headline3 onSurface text-center">{{ data.title }}</h2>
                <p v-if="data.titledescription" class="alignC mb-4">{{ data.titledescription }}</p>
                <div class="row p-top-largest-times-1">
                    <div class="col-md-6">
                        <img :src="data.media.image" :alt="data.title" class="img-fluid">
                    </div>
                    <div class="col-md-6 ">
						<b-collapse 
							class="accordian" 
							animation="slide" 
							v-for="(item, index) in data.items"
							:key="index"
							:open="isOpen == index"
							@open="isOpen = index"
							:aria-id="'accordion-' + index"
						>
							<template #trigger="props">
								<div
									class="card-header"
									role="button"
									:aria-controls="'accordion-' + index"
									:aria-expanded="props.open"
								>
									<a class="card-header-icon">
										<span class="material-icons headline6 onSurface">{{ props.open ? 'expand_less' : 'expand_more' }}</span>
									</a>
									<p class="card-header-title headline6 onSurface">
										{{ item.title }}
									</p>
								</div>
							</template>
							<div class="card-content">
								<div class="content subtitle2 noBold onSurfaceVariant">
									{{ item.description }}
								</div>
							</div>
						</b-collapse>
					</div>
                </div>
            </div>
        </section>
    `,
  data() {
    return {
		isOpen: 0,
	};
  },
  methods: {},
});
