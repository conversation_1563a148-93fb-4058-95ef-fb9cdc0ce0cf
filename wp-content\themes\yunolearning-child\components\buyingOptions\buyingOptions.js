const YUNOBuyingOptions = (function($) {
    
    const buyingOptions = function() {
        
        YUNOArticleBlock.articleBlock();

        Vue.component('yuno-buying-options', {
            props: ["options"],
            template: `
                <section id="buyingOptions" class="buyingOptions">
                    <div class="container">
                        <yuno-article-block :options="options.article"></yuno-article-block>
                        <div class="row justify-content-md-center">
                            <div 
                                v-for="(card, cardIndex) in options.cards"
                                :key="cardIndex"
                                class="col-12 buyingCardWrapper"
                                :class="options.columns !== undefined ? options.columns : 'col-md-4'">
                                <div class="buyingCard">
                                    <h3 class="cardTitle">{{card.title}}</h3>
                                    <p v-if="card.subtitle !== undefined && card.subtitle !== ''" class="cardNote customGap">{{card.subtitle}}</p>
                                    <p class="cardPrice">{{card.price}} <span class="noBold" v-if="false">{{card.priceHelper}}</span></p>
                                    <ul class="featureList">
                                        <li v-for="(list, listIndex) in card.features">
                                            {{list}}
                                        </li>
                                    </ul>
                                    <p class="cardNote">{{card.note}}</p>
                                    <b-button 
                                        tag="a"
                                        :href="card.cta.url"
                                        :class="[card.cta.theme, card.cta.size]">
                                        {{card.cta.label}}
                                    </b-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                
            },
            created() {
                
            },
            mounted() {
                this.richSnippet();
            },
            methods: {
                structuredData(structuredDataObj) {
                    const script = document.createElement('script');
                    
                    script.setAttribute('type', 'application/ld+json');

                    let inlineScript = document.createTextNode(JSON.stringify(structuredDataObj));

                    script.appendChild(inlineScript); 
                    document.head.appendChild(script);
                },
                richSnippet() {
                    let data = this.$props.options;
                        structuredDataText = {
                            "@context": "https://schema.org",
                            "@type": "WebPage",
                            "itemListElement": []
                        }

                    let cards = data.cards;

                    for (var i = 0; i < cards.length; i++) {
                        let dataObj = {
                            "@type": "Course",
                            "url": cards[i].cta.url,
                            "name": cards[i].title,
                            "description": cards[i].note === undefined ? cards[i].description : cards[i].note,
                            "price": cards[i].price,
                            "priceCurrency": "INR",
                            "provider": {
                                "@type": "Organization",
                                "name": "Yuno Learning",
                                "sameAs": "https://www.yunolearning.com"
                            }
                        }

                        structuredDataText.itemListElement.push(dataObj);
                    };
                    this.structuredData(structuredDataText);
                },
            }
        });
    };

    return {
        buyingOptions: buyingOptions
    };
})(jQuery);

