@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";


#app {
	.yunoBlocks {
		h2 {
			font-size: $fontSizeLargest;
			margin: 0 0 $gapLarge;
			font-weight: 400;

			small {
				display: block;
				font-size: $fontSizeSmall;
				@include setFontColor($primaryCopyColor, 0.5);
				padding-top: $gapSmall;
			}
		}

		p {
			font-size: $fontSizeSmall;
			margin-bottom: 0;
		}

		.blockImg {
			margin-bottom: $gapLarge;

			img {
				width: 100%;
				height: auto;
			}

			&.withShadow {
				box-shadow: 2px 1px 16px 4px #ccc;
			}

			&.withBorder {
				border-radius: 10px;
				border: 8px solid #eaeaea;
			}

			@media (min-width: 768px) {
				margin-bottom: 0;
			}
		}

		.row {
			padding-bottom: $gapLargest * 2;
		}
	}
}