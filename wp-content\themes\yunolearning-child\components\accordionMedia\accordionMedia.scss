@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yuno-icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -moz-font-feature-settings: "liga=1";
  -moz-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

#app {
  .dark87 {
    @include setFontColor($primaryCopyColor, 0.87);
  }

  .dark60 {
    @include setFontColor($primaryCopyColor, 0.6);
  }

  %headline6 {
    @include setFont($headline6, 28px, 500, 0);
  }

  .accordionMedia {
    padding: 30px 0;
    @media (min-width: 768px) {
      padding: 60px 0 40px 0;
    }
    .collapse:not(.show) {
      display: block;
    }

    .accordian {
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
      padding-bottom: 10px;
      width: 100%;

      @media (min-width: 768px) {
        width: 70%;
      }

      .card-header {
        box-shadow: none;
        padding: 1rem 0 0.3rem 0;
        display: flex;
        align-items: center;
        background: transparent;
        border: none;
        cursor: pointer;

        .card-header-icon {
          order: -1;
          margin-right: 1rem;
          padding: 0;
          text-decoration: none;

          &:hover {
            text-decoration: none;
          }
        }

        .card-header-title {
          margin: 0;
          padding: 0;
        }
      }

      .card-content {
        padding: 0 0 0.5rem 2.5rem;

        .content {
          color: rgba(0, 0, 0, 0.6);
          font-size: 0.875rem;
          line-height: 1.5;
        }
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
}
