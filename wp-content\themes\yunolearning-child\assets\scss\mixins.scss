@mixin setBGColor ($baseColor, $opacity) { 
	background-color: rgba(red($baseColor),green($baseColor),blue($baseColor), $opacity);
}

@mixin setBorderColor ($baseColor, $opacity) { 
	border-color: rgba(red($baseColor),green($baseColor),blue($baseColor), $opacity);
}

@mixin setFontColor ($baseColor, $opacity) {
	color: rgba(red($baseColor),green($baseColor),blue($baseColor), $opacity);
}

@mixin setFontFamily ($family, $weight) {
	font-family: $family;
	font-weight: $weight;
}

@mixin setFont ($size, $lineheight, $weight: 400, $btmMargin: 0) {
	font-size: $size;
	line-height: $lineheight;
	font-weight: $weight;
	margin-bottom: $btmMargin;
}

@mixin setGap ($top, $right, $bottom, $left) {
	margin: $top $right $bottom $left;
}