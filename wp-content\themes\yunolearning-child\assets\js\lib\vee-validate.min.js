/*
 MIT
 *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
*****************************************************************************/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(m){var p=0;return function(){return p<m.length?{done:!1,value:m[p++]}:{done:!0}}};$jscomp.arrayIterator=function(m){return{next:$jscomp.arrayIteratorImpl(m)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(m,p,t){if(m==Array.prototype||m==Object.prototype)return m;m[p]=t.value;return m};$jscomp.getGlobal=function(m){m=["object"==typeof globalThis&&globalThis,m,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var p=0;p<m.length;++p){var t=m[p];if(t&&t.Math==Math)return t}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(m,p){var t=$jscomp.propertyToPolyfillSymbol[p];if(null==t)return m[p];t=m[t];return void 0!==t?t:m[p]};
$jscomp.polyfill=function(m,p,t,u){p&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(m,p,t,u):$jscomp.polyfillUnisolated(m,p,t,u))};$jscomp.polyfillUnisolated=function(m,p,t,u){t=$jscomp.global;m=m.split(".");for(u=0;u<m.length-1;u++){var v=m[u];if(!(v in t))return;t=t[v]}m=m[m.length-1];u=t[m];p=p(u);p!=u&&null!=p&&$jscomp.defineProperty(t,m,{configurable:!0,writable:!0,value:p})};
$jscomp.polyfillIsolated=function(m,p,t,u){var v=m.split(".");m=1===v.length;u=v[0];u=!m&&u in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var z=0;z<v.length-1;z++){var A=v[z];if(!(A in u))return;u=u[A]}v=v[v.length-1];t=$jscomp.IS_SYMBOL_NATIVE&&"es6"===t?u[v]:null;p=p(t);null!=p&&(m?$jscomp.defineProperty($jscomp.polyfills,v,{configurable:!0,writable:!0,value:p}):p!==t&&($jscomp.propertyToPolyfillSymbol[v]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(v):$jscomp.POLYFILL_PREFIX+v,v=
$jscomp.propertyToPolyfillSymbol[v],$jscomp.defineProperty(u,v,{configurable:!0,writable:!0,value:p})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(m){if(m)return m;var p=function(v,z){this.$jscomp$symbol$id_=v;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:z})};p.prototype.toString=function(){return this.$jscomp$symbol$id_};var t=0,u=function(v){if(this instanceof u)throw new TypeError("Symbol is not a constructor");return new p("jscomp_symbol_"+(v||"")+"_"+t++,v)};return u},"es6","es3");$jscomp.initSymbolIterator=function(){};
$jscomp.polyfill("Symbol.iterator",function(m){if(m)return m;m=Symbol("Symbol.iterator");for(var p="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),t=0;t<p.length;t++){var u=$jscomp.global[p[t]];"function"===typeof u&&"function"!=typeof u.prototype[m]&&$jscomp.defineProperty(u.prototype,m,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return m},"es6",
"es3");$jscomp.initSymbolAsyncIterator=function(){};$jscomp.iteratorPrototype=function(m){m={next:m};m[Symbol.iterator]=function(){return this};return m};
(function(m,p){"object"===typeof exports&&"undefined"!==typeof module?p(exports,require("vue")):"function"===typeof define&&define.amd?define(["exports","vue"],p):(m=m||self,p(m.VeeValidate={},m.Vue))})(this,function(m,p){function t(a,b,c,d){return new (c||(c=Promise))(function(e,k){function h(g){try{l(d.next(g))}catch(n){k(n)}}function f(g){try{l(d["throw"](g))}catch(n){k(n)}}function l(g){g.done?e(g.value):(new c(function(n){n(g.value)})).then(h,f)}l((d=d.apply(a,b||[])).next())})}function u(a,
b){function c(g){return function(n){return d([g,n])}}function d(g){if(k)throw new TypeError("Generator is already executing.");for(;e;)try{if(k=1,h&&(f=g[0]&2?h["return"]:g[0]?h["throw"]||((f=h["return"])&&f.call(h),0):h.next)&&!(f=f.call(h,g[1])).done)return f;if(h=0,f)g=[g[0]&2,f.value];switch(g[0]){case 0:case 1:f=g;break;case 4:return e.label++,{value:g[1],done:!1};case 5:e.label++;h=g[1];g=[0];continue;case 7:g=e.ops.pop();e.trys.pop();continue;default:if(!(f=e.trys,f=0<f.length&&f[f.length-
1])&&(6===g[0]||2===g[0])){e=0;continue}if(3===g[0]&&(!f||g[1]>f[0]&&g[1]<f[3]))e.label=g[1];else if(6===g[0]&&e.label<f[1])e.label=f[1],f=g;else if(f&&e.label<f[2])e.label=f[2],e.ops.push(g);else{f[2]&&e.ops.pop();e.trys.pop();continue}}g=b.call(a,e)}catch(n){g=[6,n],h=0}finally{k=f=0}if(g[0]&5)throw g[1];return{value:g[0]?g[1]:void 0,done:!0}}var e={label:0,sent:function(){if(f[0]&1)throw f[1];return f[1]},trys:[],ops:[]},k,h,f,l;return l={next:c(0),"throw":c(1),"return":c(2)},"function"===typeof Symbol&&
(l[Symbol.iterator]=function(){return this}),l}function v(){for(var a=0,b=0,c=arguments.length;b<c;b++)a+=arguments[b].length;a=Array(a);var d=0;for(b=0;b<c;b++)for(var e=arguments[b],k=0,h=e.length;k<h;k++,d++)a[d]=e[k];return a}function z(a){return null===a||void 0===a}function A(a,b){if(a instanceof RegExp&&b instanceof RegExp)return A(a.source,b.source)&&A(a.flags,b.flags);if(Array.isArray(a)&&Array.isArray(b)){if(a.length!==b.length)return!1;for(var c=0;c<a.length;c++)if(!A(a[c],b[c]))return!1;
return!0}return B(a)&&B(b)?Object.keys(a).every(function(d){return A(a[d],b[d])})&&Object.keys(b).every(function(d){return A(a[d],b[d])}):a!==a&&b!==b?!0:a===b}function Z(a){return""===a?!1:!z(a)}function y(a){return"function"===typeof a}function E(a){return y(a)&&!!a.__locatorRef}function aa(a,b){var c=Array.isArray(a)?a:M(a);if(y(c.findIndex))return c.findIndex(b);for(var d=0;d<c.length;d++)if(b(c[d],d))return d;return-1}function za(a,b){var c=Array.isArray(a)?a:M(a),d=aa(c,b);return-1===d?void 0:
c[d]}function F(a,b){return-1!==a.indexOf(b)}function M(a){if(y(Array.from))return Array.from(a);for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}function N(a){return y(Object.values)?Object.values(a):Object.keys(a).map(function(b){return a[b]})}function O(a,b){Object.keys(b).forEach(function(c){B(b[c])?(a[c]||(a[c]={}),O(a[c],b[c])):a[c]=b[c]});return a}function P(){return{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,
failed:!1}}function Aa(a){return a}function ba(a,b,c){void 0===b&&(b=0);void 0===c&&(c={cancelled:!1});if(0===b)return a;var d;return function(){for(var e=[],k=0;k<arguments.length;k++)e[k]=arguments[k];clearTimeout(d);d=setTimeout(function(){d=void 0;c.cancelled||a.apply(void 0,e)},b)}}function Q(a,b){return a.replace(/{([^}]+)}/g,function(c,d){return d in b?b[d]:"{"+d+"}"})}function Ba(a){var b;if(null===(b=a.params)||void 0===b?0:b.length)a.params=a.params.map(function(c){return"string"===typeof c?
{name:c}:c});return a}function I(a){var b={};Object.defineProperty(b,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1});return a?B(a)&&a._$$isNormalized?a:B(a)?Object.keys(a).reduce(function(c,d){var e=!0===a[d]?[]:Array.isArray(a[d])?a[d]:B(a[d])?a[d]:[a[d]];!1!==a[d]&&(c[d]=ca(d,e));return c},b):"string"!==typeof a?(console.warn("[vee-validate] rules must be either a string or an object."),b):a.split("|").reduce(function(c,d){var e=[],k=d.split(":")[0];F(d,":")&&(e=d.split(":").slice(1).join(":").split(","));
if(!k)return c;c[k]=ca(k,e);return c},b):b}function ca(a,b){var c=x.getRuleDefinition(a);if(!c)return b;var d={};if(!c.params&&!Array.isArray(b))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(b)&&!c.params)return b;if(!c.params||c.params.length<b.length&&Array.isArray(b)){var e;var k=b.map(function(g,n){var q,w=null===(q=c.params)||void 0===q?void 0:q[n];e=w||e;w||(w=e);return w})}else k=c.params;for(var h=0;h<k.length;h++){var f=k[h],l=f["default"];
Array.isArray(b)?h in b&&(l=b[h]):f.name in b?l=b[f.name]:1===k.length&&(l=b);f.isTarget&&(l=da(l,f.cast));"string"===typeof l&&"@"===l[0]&&(l=da(l.slice(1),f.cast));!E(l)&&f.cast&&(l=f.cast(l));d[f.name]?(d[f.name]=Array.isArray(d[f.name])?d[f.name]:[d[f.name]],d[f.name].push(l)):d[f.name]=l}return d}function da(a,b){var c=function(d){d=d[a];return b?b(d):d};c.__locatorRef=a;return c}function Ca(a){return Array.isArray(a)?a.filter(E):Object.keys(a).filter(function(b){return E(a[b])}).map(function(b){return a[b]})}
function ea(a,b,c){void 0===c&&(c={});var d,e,k,h,f,l;return t(this,void 0,void 0,function(){var g,n,q,w,D,J,R;return u(this,function(fa){switch(fa.label){case 0:return g=null===(d=c)||void 0===d?void 0:d.bails,n=null===(e=c)||void 0===e?void 0:e.skipIfEmpty,q={name:(null===(k=c)||void 0===k?void 0:k.name)||"{field}",rules:I(b),bails:null!==g&&void 0!==g?g:!0,skipIfEmpty:null!==n&&void 0!==n?n:!0,forceRequired:!1,crossTable:(null===(h=c)||void 0===h?void 0:h.values)||{},names:(null===(f=c)||void 0===
f?void 0:f.names)||{},customMessages:(null===(l=c)||void 0===l?void 0:l.customMessages)||{}},[4,Da(q,a,c)];case 1:return w=fa.sent(),D=[],J={},R={},w.errors.forEach(function(L){var ha=L.msg();D.push(ha);J[L.rule]=ha;R[L.rule]=L.msg}),[2,{valid:w.valid,errors:D,failedRules:J,regenerateMap:R}]}})})}function Da(a,b,c){c=(void 0===c?{}:c).isInitial;var d=void 0===c?!1:c;return t(this,void 0,void 0,function(){var e,k,h,f,l,g,n,q;return u(this,function(w){switch(w.label){case 0:return[4,Ea(a,b)];case 1:e=
w.sent();k=e.shouldSkip;h=e.errors;if(k)return[2,{valid:!h.length,errors:h}];f=Object.keys(a.rules).filter(function(D){return!x.isRequireRule(D)});l=f.length;g=0;w.label=2;case 2:if(!(g<l))return[3,5];if(d&&x.isLazy(f[g]))return[3,4];n=f[g];return[4,ia(a,b,{name:n,params:a.rules[n]})];case 3:q=w.sent();if(!q.valid&&q.error&&(h.push(q.error),a.bails))return[2,{valid:!1,errors:h}];w.label=4;case 4:return g++,[3,2];case 5:return[2,{valid:!h.length,errors:h}]}})})}function Ea(a,b){return t(this,void 0,
void 0,function(){var c,d,e,k,h,f,l,g,n;return u(this,function(q){switch(q.label){case 0:c=Object.keys(a.rules).filter(x.isRequireRule);d=c.length;e=[];var w;(w=z(b)||""===b)||(w=Array.isArray(b)&&0===b.length);h=(k=w)&&a.skipIfEmpty;f=!1;l=0;q.label=1;case 1:if(!(l<d))return[3,4];g=c[l];return[4,ia(a,b,{name:g,params:a.rules[g]})];case 2:n=q.sent();if(!B(n))throw Error("Require rules has to return an object (see docs)");n.required&&(f=!0);if(!n.valid&&n.error&&(e.push(n.error),a.bails))return[2,
{shouldSkip:!0,errors:e}];q.label=3;case 3:return l++,[3,1];case 4:return k&&!f&&!a.skipIfEmpty||!a.bails&&!h?[2,{shouldSkip:!1,errors:e}]:[2,{shouldSkip:!f&&k,errors:e}]}})})}function ia(a,b,c){return t(this,void 0,void 0,function(){var d,e,k,h,f;return u(this,function(l){switch(l.label){case 0:d=x.getRuleDefinition(c.name);if(!d||!d.validate)throw Error("No such validator '"+c.name+"' exists.");e=d.castValue?d.castValue(b):b;k=Fa(c.params,a.crossTable);return[4,d.validate(e,k)];case 1:h=l.sent();
if("string"===typeof h)return f=r(r({},k||{}),{_field_:a.name,_value_:b,_rule_:c.name}),[2,{valid:!1,error:{rule:c.name,msg:function(){return Q(h,f)}}}];B(h)||(h={valid:h});return[2,{valid:h.valid,required:h.required,error:h.valid?void 0:Ga(a,b,d,c.name,k)}]}})})}function Ga(a,b,c,d,e){var k,h=(k=a.customMessages[d],null!==k&&void 0!==k?k:c.message);k=Ha(a,c,d);c=Ia(a,c,d,h);h=c.userTargets;var f=c.userMessage,l=r(r(r(r({},e||{}),{_field_:a.name,_value_:b,_rule_:d}),k),h);return{msg:function(){var g=
f||C.defaultMessage;var n=a.name;g="function"===typeof g?g(n,l):Q(g,r(r({},l),{_field_:n}));return g},rule:d}}function Ha(a,b,c){b=b.params;if(!b||0>=b.filter(function(f){return f.isTarget}).length)return{};var d={},e=a.rules[c];!Array.isArray(e)&&B(e)&&(e=b.map(function(f){return e[f.name]}));for(c=0;c<b.length;c++){var k=b[c],h=e[c];E(h)&&(h=h.__locatorRef,d[k.name]=a.names[h]||h,d["_"+k.name+"_"]=a.crossTable[h])}return d}function Ia(a,b,c,d){var e={},k=a.rules[c],h=b.params||[];if(!k)return{};
Object.keys(k).forEach(function(f,l){var g=k[f];if(!E(g))return{};var n=h[l];if(!n)return{};g=g.__locatorRef;e[n.name]=a.names[g]||g;e["_"+n.name+"_"]=a.crossTable[g]});return{userTargets:e,userMessage:d}}function Fa(a,b){if(Array.isArray(a))return a;var c={};Object.keys(a).forEach(function(d){var e=a[d];e=E(e)?e(b):e;c[d]=e});return c}function ja(){S.$emit("change:locale")}function Ja(a){var b,c;if(!a||!("undefined"!==typeof Event&&y(Event)&&a instanceof Event||a&&a.srcElement))return a;a=a.target;
return"file"===a.type&&a.files?M(a.files):(null===(b=a._vModifiers)||void 0===b?0:b.number)?(b=parseFloat(a.value),b!==b?a.value:b):(null===(c=a._vModifiers)||void 0===c?0:c.trim)?"string"===typeof a.value?a.value.trim():a.value:a.value}function T(a){if(a.data){var b=a.data;if("model"in b)return b.model;if(a.data.directives)return za(a.data.directives,function(c){return"model"===c.name})}}function U(a){var b,c,d,e=T(a);if(e)return{value:e.value};e=(null===(b=V(a))||void 0===b?void 0:b.prop)||"value";
if((null===(c=a.componentOptions)||void 0===c?0:c.propsData)&&e in a.componentOptions.propsData)return{value:a.componentOptions.propsData[e]};if((null===(d=a.data)||void 0===d?0:d.domProps)&&"value"in a.data.domProps)return{value:a.data.domProps.value}}function Ka(a){return Array.isArray(a)?a:Array.isArray(a.children)?a.children:a.componentOptions&&Array.isArray(a.componentOptions.children)?a.componentOptions.children:[]}function ka(a){return Array.isArray(a)||void 0===U(a)?Ka(a).reduce(function(b,
c){var d=ka(c);d.length&&b.push.apply(b,d);return b},[]):[a]}function V(a){return a.componentOptions?a.componentOptions.Ctor.options.model:null}function K(a,b,c){z(a[b])?a[b]=[c]:y(a[b])&&a[b].fns?(a=a[b],a.fns=Array.isArray(a.fns)?a.fns:[a.fns],F(a.fns,c)||a.fns.push(c)):(y(a[b])&&(a[b]=[a[b]]),Array.isArray(a[b])&&!F(a[b],c)&&a[b].push(c))}function W(a,b,c){a.componentOptions?a.componentOptions&&(a.componentOptions.listeners||(a.componentOptions.listeners={}),K(a.componentOptions.listeners,b,c)):
(a.data||(a.data={}),z(a.data.on)&&(a.data.on={}),K(a.data.on,b,c))}function la(a,b){var c;return a.componentOptions?(V(a)||{event:"input"}).event:(null===(c=null===b||void 0===b?void 0:b.modifiers)||void 0===c?0:c.lazy)?"change":ma(a)?"input":"change"}function La(a,b){return Object.keys(a).reduce(function(c,d){a[d].forEach(function(e){e.context||(a[d].context=b,e.data||(e.data={}),e.data.slot=d)});return c.concat(a[d])},[])}function na(a,b){return a.$scopedSlots["default"]?a.$scopedSlots["default"](b)||
[]:a.$slots["default"]||[]}function oa(a){return r(r({},a.flags),{errors:a.errors,classes:a.classes,failedRules:a.failedRules,reset:function(){return a.reset()},validate:function(){for(var b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];return a.validate.apply(a,b)},ariaInput:{"aria-invalid":a.flags.invalid?"true":"false","aria-required":a.isRequired?"true":"false","aria-errormessage":"vee_"+a.id},ariaMsg:{id:"vee_"+a.id,"aria-live":a.errors.length?"assertive":"off"}})}function pa(a,b){a.initialized||
(a.initialValue=b);var c=!a._ignoreImmediate&&a.immediate||a.value!==b&&a.normalizedEvents.length||a._needsValidation||!a.initialized&&void 0===b?!0:!1;a._needsValidation=!1;a.value=b;a._ignoreImmediate=!0;if(c){var d=function(){if(a.immediate||a.flags.validated)return X(a);a.validateSilent()};a.initialized?d():a.$once("hook:mounted",function(){return d()})}}function qa(a){return(y(a.mode)?a.mode:ra[a.mode])(a)}function X(a){var b=a.validateSilent();a._pendingValidation=b;return b.then(function(c){b===
a._pendingValidation&&(a.applyResult(c),a._pendingValidation=void 0);return c})}function sa(a){a.$veeOnInput||(a.$veeOnInput=function(k){a.syncValue(k);a.setFlags({dirty:!0,pristine:!1})});var b=a.$veeOnInput;a.$veeOnBlur||(a.$veeOnBlur=function(){a.setFlags({touched:!0,untouched:!1})});var c=a.$veeOnBlur,d=a.$veeHandler,e=qa(a);d&&a.$veeDebounce===a.debounce||(d=ba(function(){a.$nextTick(function(){a._pendingReset||X(a);a._pendingReset=!1})},e.debounce||a.debounce),a.$veeHandler=d,a.$veeDebounce=
a.debounce);return{onInput:b,onBlur:c,onValidate:d}}function Ma(a,b){var c=U(b);a._inputEventName=a._inputEventName||la(b,T(b));pa(a,null===c||void 0===c?void 0:c.value);c=sa(a);var d=c.onBlur,e=c.onValidate;W(b,a._inputEventName,c.onInput);W(b,"blur",d);a.normalizedEvents.forEach(function(k){W(b,k,e)});a.initialized=!0}function Na(a,b){for(var c={},d=Object.keys(b),e=d.length,k=function(f){f=d[f];var l=a&&a[f]||f,g=b[f];if(z(g)||("valid"===f||"invalid"===f)&&!b.validated)return"continue";"string"===
typeof l?c[l]=g:Array.isArray(l)&&l.forEach(function(n){c[n]=g})},h=0;h<e;h++)k(h);return c}function Oa(a){var b=a.$_veeObserver.refs;return a.fieldDeps.reduce(function(c,d){if(!b[d])return c;c.values[d]=b[d].value;c.names[d]=b[d].name;return c},{names:{},values:{}})}function Pa(a){if(a.vid)return a.vid;if(a.name)return a.name;if(a.id)return a.id;if(a.fieldName)return a.fieldName;ta++;return"_vee_"+ta}function Qa(){return{refs:{},observe:function(a){this.refs[a.id]=a},unobserve:function(a){delete this.refs[a]}}}
function ua(a,b,c){void 0===c&&(c=!0);var d=a.$_veeObserver.refs;a._veeWatchers||(a._veeWatchers={});if(!d[b]&&c)return a.$once("hook:mounted",function(){ua(a,b,!1)});!y(a._veeWatchers[b])&&d[b]&&(a._veeWatchers[b]=d[b].$watch("value",function(){a.flags.validated&&(a._needsValidation=!0,a.validate())}))}function va(a){a.$_veeObserver&&a.$_veeObserver.unobserve(a.id,"observer")}function wa(a){a.$_veeObserver&&a.$_veeObserver.observe(a,"observer")}function xa(){return r(r({},P()),{valid:!0,invalid:!1})}
function Ra(){for(var a=v(N(this.refs),this.observers),b={},c=xa(),d={},e=a.length,k=0;k<e;k++){var h=a[k];Array.isArray(h.errors)?(b[h.id]=h.errors,d[h.id]=r({id:h.id,name:h.name,failedRules:h.failedRules},h.flags)):(b=r(r({},b),h.errors),d=r(r({},d),h.fields))}Sa.forEach(function(f){var l=f[0];c[l]=a[f[1]](function(g){return g.flags[l]})});return{errors:b,flags:c,fields:d}}p=p&&p.hasOwnProperty("default")?p["default"]:p;var r=function(){r=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<
d;c++){b=arguments[c];for(var e in b)Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e])}return a};return r.apply(this,arguments)},B=function(a){return null!==a&&a&&"object"===typeof a&&!Array.isArray(a)},G={},x=function(){function a(){}a.extend=function(b,c){var d=Ba(c);G[b]=G[b]?O(G[b],c):r({lazy:!1,computesRequired:!1},d)};a.isLazy=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.lazy)};a.isRequireRule=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.computesRequired)};a.getRuleDefinition=
function(b){return G[b]};return a}(),C=r({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),Y=function(a){C=r(r({},C),a)},ra={aggressive:function(){return{on:["input","blur"]}},eager:function(a){return a.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},
S=new p,Ta=function(){function a(b,c){this.container={};this.locale=b;this.merge(c)}a.prototype.resolve=function(b,c,d){return this.format(this.locale,b,c,d)};a.prototype.format=function(b,c,d,e){var k,h,f,l,g,n,q,w;(d=(null===(f=null===(h=null===(k=this.container[b])||void 0===k?void 0:k.fields)||void 0===h?void 0:h[c])||void 0===f?void 0:f[d])||(null===(g=null===(l=this.container[b])||void 0===l?void 0:l.messages)||void 0===g?void 0:g[d]))||(d="{field} is not valid");c=(w=null===(q=null===(n=this.container[b])||
void 0===n?void 0:n.names)||void 0===q?void 0:q[c],null!==w&&void 0!==w?w:c);return y(d)?d(c,e):Q(d,r(r({},e),{_field_:c}))};a.prototype.merge=function(b){O(this.container,b)};a.prototype.hasRule=function(b){var c,d;return!(null===(d=null===(c=this.container[this.locale])||void 0===c?void 0:c.messages)||void 0===d||!d[b])};return a}(),H,ma=function(a){var b,c=(null===(b=a.data)||void 0===b?void 0:b.attrs)||a.elm;return("input"!==a.tag||c&&c.type)&&"textarea"!==a.tag?F("text password search email tel url number".split(" "),
null===c||void 0===c?void 0:c.type):!0},ta=0,ya=p.extend({inject:{$_veeObserver:{from:"$_veeObserver","default":function(){this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver=Qa());return this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,"default":""},name:{type:String,"default":null},mode:{type:[String,Function],"default":function(){return C.mode}},rules:{type:[Object,String],"default":null},immediate:{type:Boolean,"default":!1},bails:{type:Boolean,"default":function(){return C.bails}},
skipIfEmpty:{type:Boolean,"default":function(){return C.skipOptional}},debounce:{type:Number,"default":0},tag:{type:String,"default":"span"},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1},customMessages:{type:Object,"default":function(){return{}}}},watch:{rules:{deep:!0,handler:function(a,b){this._needsValidation=!A(a,b)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:P(),failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var a=
this;return Object.keys(this.normalizedRules).reduce(function(b,c){var d=Ca(a.normalizedRules[c]).map(function(e){return e.__locatorRef});b.push.apply(b,d);d.forEach(function(e){ua(a,e)});return b},[])},normalizedEvents:function(){var a=this;return(qa(this).on||[]).map(function(b){return"input"===b?a._inputEventName:b})},isRequired:function(){var a=r(r({},this._resolvedRules),this.normalizedRules);a=Object.keys(a).some(x.isRequireRule);this.flags.required=!!a;return a},classes:function(){return Na(C.classes,
this.flags)},normalizedRules:function(){return I(this.rules)}},created:function(){var a=this,b=function(){if(a.flags.validated){var c=a._regenerateMap;if(c){var d=[],e={};Object.keys(c).forEach(function(k){var h=c[k]();d.push(h);e[k]=h});a.applyResult({errors:d,failedRules:e,regenerateMap:c})}else a.validate()}};S.$on("change:locale",b);this.$on("hook:beforeDestroy",function(){S.$off("change:locale",b)})},render:function(a){var b=this;this.registerField();var c=oa(this);c=na(this,c);ka(c).forEach(function(d){var e,
k,h,f,l;if(C.useConstraintAttrs){var g,n=null===(g=d.data)||void 0===g?void 0:g.attrs;if(F(["input","select","textarea"],d.tag)&&n)if(g={},"required"in n&&!1!==n.required&&x.getRuleDefinition("required")&&(g.required="checkbox"===n.type?[!0]:!0),ma(d)){n=r;g=r({},g);var q=null===(l=d.data)||void 0===l?void 0:l.attrs;l={};q&&("email"===q.type&&x.getRuleDefinition("email")&&(l.email=["multiple"in q]),q.pattern&&x.getRuleDefinition("regex")&&(l.regex=q.pattern),0<=q.maxlength&&x.getRuleDefinition("max")&&
(l.max=q.maxlength),0<=q.minlength&&x.getRuleDefinition("min")&&(l.min=q.minlength),"number"===q.type&&(Z(q.min)&&x.getRuleDefinition("min_value")&&(l.min_value=Number(q.min)),Z(q.max)&&x.getRuleDefinition("max_value")&&(l.max_value=Number(q.max))));l=I(n(g,l))}else l=I(g);else l={}}else l={};A(b._resolvedRules,l)||(b._needsValidation=!0);F(["input","select","textarea"],d.tag)&&(b.fieldName=(null===(k=null===(e=d.data)||void 0===e?void 0:e.attrs)||void 0===k?void 0:k.name)||(null===(f=null===(h=d.data)||
void 0===h?void 0:h.attrs)||void 0===f?void 0:f.id));b._resolvedRules=l;Ma(b,d)});return this.slim&&1>=c.length?c[0]:a(this.tag,c)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(a){var b=this;Object.keys(a).forEach(function(c){b.flags[c]=a[c]})},syncValue:function(a){this.value=a=Ja(a);this.flags.changed=this.initialValue!==a},reset:function(){var a=this;this.errors=[];this.initialValue=
this.value;var b=P();b.required=this.isRequired;this.setFlags(b);this.failedRules={};this.validateSilent();this._pendingValidation=void 0;this._pendingReset=!0;setTimeout(function(){a._pendingReset=!1},this.debounce)},validate:function(){for(var a=[],b=0;b<arguments.length;b++)a[b]=arguments[b];return t(this,void 0,void 0,function(){return u(this,function(c){0<a.length&&this.syncValue(a[0]);return[2,X(this)]})})},validateSilent:function(){return t(this,void 0,void 0,function(){var a,b;return u(this,
function(c){switch(c.label){case 0:return this.setFlags({pending:!0}),a=r(r({},this._resolvedRules),this.normalizedRules),Object.defineProperty(a,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,ea(this.value,a,r(r({name:this.name||this.fieldName},Oa(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return b=c.sent(),this.setFlags({pending:!1,valid:b.valid,invalid:!b.valid}),[2,b]}})})},setErrors:function(a){this.applyResult({errors:a,
failedRules:{}})},applyResult:function(a){var b=a.errors,c=a.failedRules;a=a.regenerateMap;this.errors=b;this._regenerateMap=a;this.failedRules=r({},c||{});this.setFlags({valid:!b.length,passed:!b.length,invalid:!!b.length,failed:!!b.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var a=Pa(this),b=this.id;!this.isActive||b===a&&this.$_veeObserver.refs[b]||(b!==a&&this.$_veeObserver.refs[b]===this&&this.$_veeObserver.unobserve(b),this.id=a,this.$_veeObserver.observe(this))}}}),
Sa=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],Ua=0,Va=p.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver","default":function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,"default":"span"},vid:{type:String,
"default":function(){return"obs_"+Ua++}},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:xa(),fields:{}}},created:function(){var a=this;this.id=this.vid;wa(this);var b=ba(function(c){var d=c.flags,e=c.fields;a.errors=c.errors;a.flags=d;a.fields=e},16);this.$watch(Ra,b)},activated:function(){wa(this)},deactivated:function(){va(this)},beforeDestroy:function(){va(this)},render:function(a){var b=na(this,r(r({},this.flags),
{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=b.length?b[0]:a(this.tag,{on:this.$listeners},b)},methods:{observe:function(a,b){var c;void 0===b&&(b="provider");"observer"===b?this.observers.push(a):this.refs=r(r({},this.refs),(c={},c[a.id]=a,c))},unobserve:function(a,b){void 0===b&&(b="provider");if("provider"===b)this.refs[a]&&this.$delete(this.refs,a);else{var c=aa(this.observers,function(d){return d.id===
a});-1!==c&&this.observers.splice(c,1)}},validate:function(a){a=(void 0===a?{}:a).silent;var b=void 0===a?!1:a;return t(this,void 0,void 0,function(){var c;return u(this,function(d){switch(d.label){case 0:return[4,Promise.all(v(N(this.refs).filter(function(e){return!e.disabled}).map(function(e){return e[b?"validateSilent":"validate"]().then(function(k){return k.valid})}),this.observers.filter(function(e){return!e.disabled}).map(function(e){return e.validate({silent:b})})))];case 1:return c=d.sent(),
[2,c.every(function(e){return e})]}})})},handleSubmit:function(a){return t(this,void 0,void 0,function(){var b;return u(this,function(c){switch(c.label){case 0:return[4,this.validate()];case 1:return(b=c.sent())&&a?[2,a()]:[2]}})})},reset:function(){return v(N(this.refs),this.observers).forEach(function(a){return a.reset()})},setErrors:function(a){var b=this;Object.keys(a).forEach(function(c){var d=b.refs[c];d&&(c=a[c]||[],c="string"===typeof c?[c]:c,d.setErrors(c))});this.observers.forEach(function(c){c.setErrors(a)})}}});
m.ValidationObserver=Va;m.ValidationProvider=ya;m.configure=function(a){Y(a)};m.extend=function(a,b){if(!y(b)&&!y(b.validate)&&!x.getRuleDefinition(a))throw Error("Extension Error: The validator '"+a+"' must be a function or have a 'validate' method.");"object"===typeof b?x.extend(a,b):x.extend(a,{validate:b})};m.localeChanged=ja;m.localize=function(a,b){var c;H||(H=new Ta("en",{}),Y({defaultMessage:function(d,e){return H.resolve(d,null===e||void 0===e?void 0:e._rule_,e||{})}}));"string"===typeof a?
(H.locale=a,b&&H.merge((c={},c[a]=b,c)),ja()):H.merge(a)};m.normalizeRules=I;m.setInteractionMode=function(a,b){Y({mode:a});if(b){if(!y(b))throw Error("A mode implementation must be a function");ra[a]=b}};m.validate=ea;m.version="3.2.3";m.withValidation=function(a,b){void 0===b&&(b=Aa);var c,d="options"in a?a.options:a,e=ya.options;e={name:(d.name||"AnonymousHoc")+"WithValidation",props:r({},e.props),data:e.data,computed:r({},e.computed),methods:r({},e.methods),beforeDestroy:e.beforeDestroy,inject:e.inject};
var k=(null===(c=null===d||void 0===d?void 0:d.model)||void 0===c?void 0:c.event)||"input";e.render=function(h){var f;this.registerField();var l=oa(this),g=r({},this.$listeners),n=T(this.$vnode);this._inputEventName=this._inputEventName||la(this.$vnode,n);var q=U(this.$vnode);pa(this,null===q||void 0===q?void 0:q.value);q=sa(this);var w=q.onBlur,D=q.onValidate;K(g,k,q.onInput);K(g,"blur",w);this.normalizedEvents.forEach(function(J){K(g,J,D)});q=(V(this.$vnode)||{prop:"value"}).prop;l=r(r(r({},this.$attrs),
(f={},f[q]=null===n||void 0===n?void 0:n.value,f)),b(l));return h(d,{attrs:this.$attrs,props:l,on:g},La(this.$slots,this.$vnode.context))};return e};Object.defineProperty(m,"__esModule",{value:!0})});



!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((r=r||self).VeeValidateRules={})}(this,function(r){"use strict";var i={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},a={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},u={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},s={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},o=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return o(r,{locale:n})}):n?(i[n]||i.en).test(e):Object.keys(i).some(function(r){return i[r].test(e)})},e={validate:o,params:[{name:"locale"}]},l=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return l(r,{locale:n})}):n?(s[n]||s.en).test(e):Object.keys(s).some(function(r){return s[r].test(e)})},t={validate:l,params:[{name:"locale"}]},c=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return c(r,{locale:n})}):n?(u[n]||u.en).test(e):Object.keys(u).some(function(r){return u[r].test(e)})},n={validate:c,params:[{name:"locale"}]},A=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return A(r,{locale:n})}):n?(a[n]||a.en).test(e):Object.keys(a).some(function(r){return a[r].test(e)})},f={validate:A,params:[{name:"locale"}]},m=function(r,e){var t=void 0===e?{}:e,n=t.min,i=t.max;return Array.isArray(r)?r.every(function(r){return!!m(r,{min:n,max:i})}):Number(n)<=r&&Number(i)>=r},v={validate:m,params:[{name:"min"},{name:"max"}]},$={validate:function(r,e){var t=e.target;return String(r)===String(t)},params:[{name:"target",isTarget:!0}]},d=function(r,e){var t=e.length;if(Array.isArray(r))return r.every(function(r){return d(r,{length:t})});var n=String(r);return/^[0-9]*$/.test(n)&&n.length===t},y={validate:d,params:[{name:"length",cast:function(r){return Number(r)}}]},g={validate:function(r,e){var u=e.width,s=e.height,t=[];r=Array.isArray(r)?r:[r];for(var n=0;n<r.length;n++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(r[n].name))return Promise.resolve(!1);t.push(r[n])}return Promise.all(t.map(function(r){return t=r,n=u,i=s,a=window.URL||window.webkitURL,new Promise(function(r){var e=new Image;e.onerror=function(){return r(!1)},e.onload=function(){return r(e.width===n&&e.height===i)},e.src=a.createObjectURL(t)});var t,n,i,a})).then(function(r){return r.every(function(r){return r})})},params:[{name:"width",cast:function(r){return Number(r)}},{name:"height",cast:function(r){return Number(r)}}]},Z={validate:function(r,e){var t=(void 0===e?{}:e).multiple,n=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return t&&!Array.isArray(r)&&(r=String(r).split(",").map(function(r){return r.trim()})),Array.isArray(r)?r.every(function(r){return n.test(String(r))}):n.test(String(r))},params:[{name:"multiple",default:!1}]};function p(r){return null==r}function h(r){return Array.isArray(r)&&0===r.length}function x(r){return"function"==typeof Array.from?Array.from(r):function(r){for(var e=[],t=r.length,n=0;n<t;n++)e.push(r[n]);return e}(r)}function _(r){return h(r)||-1!==[!1,null,void 0].indexOf(r)||!String(r).trim().length}var b=function(e,t){return Array.isArray(e)?e.every(function(r){return b(r,t)}):x(t).some(function(r){return r==e})},w={validate:b},S={validate:function(r,e){return!b(r,e)}},N={validate:function(r,e){var t=new RegExp(".("+e.join("|")+")$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.name)}):t.test(r.name)}},j={validate:function(r){var e=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(r)?r.every(function(r){return e.test(r.name)}):e.test(r.name)}},k={validate:function(r){return Array.isArray(r)?r.every(function(r){return/^-?[0-9]+$/.test(String(r))}):/^-?[0-9]+$/.test(String(r))}},z={validate:function(r,e){return r===e.other},params:[{name:"other"}]},F={validate:function(r,e){return r!==e.other},params:[{name:"other"}]},R={validate:function(r,e){var t=e.length;return!p(r)&&("number"==typeof r&&(r=String(r)),r.length||(r=x(r)),r.length===t)},params:[{name:"length",cast:function(r){return Number(r)}}]},O=function(r,e){var t=e.length;return p(r)?0<=t:Array.isArray(r)?r.every(function(r){return O(r,{length:t})}):String(r).length<=t},q={validate:O,params:[{name:"length",cast:function(r){return Number(r)}}]},C=function(r,e){var t=e.max;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return C(r,{max:t})}):Number(r)<=t)},P={validate:C,params:[{name:"max",cast:function(r){return Number(r)}}]},E={validate:function(r,e){var t=new RegExp(e.join("|").replace("*",".+")+"$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.type)}):t.test(r.type)}},L=function(r,e){var t=e.length;return!p(r)&&(Array.isArray(r)?r.every(function(r){return L(r,{length:t})}):String(r).length>=t)},U={validate:L,params:[{name:"length",cast:function(r){return Number(r)}}]},T=function(r,e){var t=e.min;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return T(r,{min:t})}):Number(r)>=t)},V={validate:T,params:[{name:"min",cast:function(r){return Number(r)}}]},I=/^[٠١٢٣٤٥٦٧٨٩]+$/,M=/^[0-9]+$/,B={validate:function(r){function e(r){var e=String(r);return M.test(e)||I.test(e)}return Array.isArray(r)?r.every(e):e(r)}},D=function(r,e){var t=e.regex;return Array.isArray(r)?r.every(function(r){return D(r,{regex:t})}):t.test(String(r))},G={validate:D,params:[{name:"regex",cast:function(r){return"string"==typeof r?new RegExp(r):r}}]},H={validate:function(r,e){var t=(void 0===e?{allowFalse:!0}:e).allowFalse,n={valid:!1,required:!0};return p(r)||h(r)||!1===r&&!t||(n.valid=!!String(r).trim().length),n},params:[{name:"allowFalse",default:!0}],computesRequired:!0},J={validate:function(r,e){var t,n=e.target,i=e.values;return(t=i&&i.length?(Array.isArray(i)||"string"!=typeof i||(i=[i]),i.some(function(r){return r==String(n).trim()})):!_(n))?{valid:!_(r),required:t}:{valid:!0,required:t}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},K={validate:function(r,e){var t=e.size;if(isNaN(t))return!1;var n=1024*t;if(!Array.isArray(r))return r.size<=n;for(var i=0;i<r.length;i++)if(r[i].size>n)return!1;return!0},params:[{name:"size",cast:function(r){return Number(r)}}]};r.alpha=e,r.alpha_dash=t,r.alpha_num=n,r.alpha_spaces=f,r.between=v,r.confirmed=$,r.digits=y,r.dimensions=g,r.email=Z,r.excluded=S,r.ext=N,r.image=j,r.integer=k,r.is=z,r.is_not=F,r.length=R,r.max=q,r.max_value=P,r.mimes=E,r.min=U,r.min_value=V,r.numeric=B,r.oneOf=w,r.regex=G,r.required=H,r.required_if=J,r.size=K,Object.defineProperty(r,"__esModule",{value:!0})});