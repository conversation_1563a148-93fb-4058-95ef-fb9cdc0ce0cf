/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Roboto Light"), local("Roboto-Light"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmSU5fCRc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Roboto Light"), local("Roboto-Light"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmSU5fABc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Roboto Light"), local("Roboto-Light"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmSU5fCBc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+1F00-1FFF;
}

/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Roboto Light"), local("Roboto-Light"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmSU5fBxc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0370-03FF;
}

/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Roboto Light"), local("Roboto-Light"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmSU5fCxc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Roboto Light"), local("Roboto-Light"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmSU5fChc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Roboto Light"), local("Roboto-Light"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmSU5fBBc4AMP6lQ.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Roboto"), local("Roboto-Regular"), url(https://fonts.gstatic.com/s/roboto/v20/KFOmCnqEu92Fr1Mu72xKKTU1Kvnz.woff2) format("woff2");
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Roboto"), local("Roboto-Regular"), url(https://fonts.gstatic.com/s/roboto/v20/KFOmCnqEu92Fr1Mu5mxKKTU1Kvnz.woff2) format("woff2");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Roboto"), local("Roboto-Regular"), url(https://fonts.gstatic.com/s/roboto/v20/KFOmCnqEu92Fr1Mu7mxKKTU1Kvnz.woff2) format("woff2");
  unicode-range: U+1F00-1FFF;
}

/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Roboto"), local("Roboto-Regular"), url(https://fonts.gstatic.com/s/roboto/v20/KFOmCnqEu92Fr1Mu4WxKKTU1Kvnz.woff2) format("woff2");
  unicode-range: U+0370-03FF;
}

/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Roboto"), local("Roboto-Regular"), url(https://fonts.gstatic.com/s/roboto/v20/KFOmCnqEu92Fr1Mu7WxKKTU1Kvnz.woff2) format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Roboto"), local("Roboto-Regular"), url(https://fonts.gstatic.com/s/roboto/v20/KFOmCnqEu92Fr1Mu7GxKKTU1Kvnz.woff2) format("woff2");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Roboto"), local("Roboto-Regular"), url(https://fonts.gstatic.com/s/roboto/v20/KFOmCnqEu92Fr1Mu4mxKKTU1Kg.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Roboto Medium"), local("Roboto-Medium"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmEU9fCRc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Roboto Medium"), local("Roboto-Medium"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmEU9fABc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Roboto Medium"), local("Roboto-Medium"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmEU9fCBc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+1F00-1FFF;
}

/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Roboto Medium"), local("Roboto-Medium"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmEU9fBxc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0370-03FF;
}

/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Roboto Medium"), local("Roboto-Medium"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmEU9fCxc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Roboto Medium"), local("Roboto-Medium"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmEU9fChc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Roboto Medium"), local("Roboto-Medium"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmEU9fBBc4AMP6lQ.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local("Roboto Bold"), local("Roboto-Bold"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmWUlfCRc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local("Roboto Bold"), local("Roboto-Bold"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmWUlfABc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local("Roboto Bold"), local("Roboto-Bold"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmWUlfCBc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+1F00-1FFF;
}

/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local("Roboto Bold"), local("Roboto-Bold"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmWUlfBxc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0370-03FF;
}

/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local("Roboto Bold"), local("Roboto-Bold"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmWUlfCxc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local("Roboto Bold"), local("Roboto-Bold"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmWUlfChc4AMP6lbBP.woff2) format("woff2");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local("Roboto Bold"), local("Roboto-Bold"), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmWUlfBBc4AMP6lQ.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjo0oSmYmRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

/* cyrillic */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjo0oSma2RlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* greek-ext */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjo0oSmY2RlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+1F00-1FFF;
}

/* greek */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjo0oSmbGRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0370-03FF;
}

/* vietnamese */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjo0oSmYGRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjo0oSmYWRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjo0oSmb2RlV9Su1cai.woff) format("woff");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjojISmYmRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

/* cyrillic */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjojISma2RlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* greek-ext */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjojISmY2RlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+1F00-1FFF;
}

/* greek */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjojISmbGRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0370-03FF;
}

/* vietnamese */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjojISmYGRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjojISmYWRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjojISmb2RlV9Su1cai.woff) format("woff");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* cyrillic-ext */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjoa4OmYmRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

/* cyrillic */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjoa4Oma2RlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

/* greek-ext */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjoa4OmY2RlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+1F00-1FFF;
}

/* greek */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjoa4OmbGRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0370-03FF;
}

/* vietnamese */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjoa4OmYGRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
}

/* latin-ext */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjoa4OmYWRlV9Su1caiTVo.woff) format("woff");
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

/* latin */
@font-face {
  font-family: 'Roboto Slab';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/robotoslab/v12/BngbUXZYTXPIvIBgJJSb6s3BzlRRfKOFbvjoa4Omb2RlV9Su1cai.woff) format("woff");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'yuno-icon';
  src: url("../fonts/yuno-icon.woff2?5qrc6l") format("woff2");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.ylIcon, #app .field .upload .icon .mdi-upload:before {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@font-face {
  font-family: 'Material Icons Outlined';
  font-style: normal;
  font-weight: 400;
  src: url("../fonts/material-Icons.woff2?6qrc5l") format("woff2");
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url("../fonts/material-Icons-filled.woff2?8qrc5l") format("woff2");
}

.material-icons-outlined, #app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .control::before, #app .yunoHeader .yunoMainNav > ul > li.dropdown .dropdownToggle:after, #app .yunoHeader .yunoMainNav > ul > li .submenuLevel2 li.dropdown:after, #app .yunoFooter .whatsappSticky a::before, #app .yunoFooter .linkList.withIcon li.zoomTest a:before, .modal.yunoModal.loginSignupModal .modal-close::after, .modal.yunoModal.lightTheme .modal-close::after {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .yunoFooter .linkList.withIcon li.wifiSpeed a:before, #app .yunoFooter .linkList.withIcon li.helpDesk a:before, #app .yunoFooter .linkList.checkList li:not(.listTitle):before {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
@font-face {
  font-display: swap;
  font-family: 'FontAwesome';
  src: url("../fonts/fontawesome-webfont.eot?v=4.7.0");
  src: url("../fonts/fontawesome-webfont.eot?#iefix&v=4.7.0") format("embedded-opentype"), url("../fonts/fontawesome-webfont.woff2?v=4.7.0") format("woff2"), url("../fonts/fontawesome-webfont.woff?v=4.7.0") format("woff"), url("../fonts/fontawesome-webfont.ttf?v=4.7.0") format("truetype"), url("../fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular") format("svg");
  font-weight: normal;
  font-style: normal;
}

.fa, #app .yunoHeader .yunoLoginDropdown .dropdown-toggle:after, #app .yunoFooter .linkList li.iconsBlock div a:before, #app .yunoFooter .linkList.withIcon li.android a:before, #app .field .datepicker .icon .mdi-chevron-left:after, #app .field .datepicker .icon .mdi-chevron-right:after, #app .field .autocomplete .control.has-icons-right .icon .mdi-close-circle:after, .modal.yunoModal .modal-close:after, .modal-close:after {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fa-lg {
  font-size: 1.33333333em;
  line-height: .75em;
  vertical-align: -15%;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

.fa-ul > li {
  position: relative;
}

.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: .14285714em;
  text-align: center;
}

.fa-li.fa-lg {
  left: -1.85714286em;
}

.fa-border {
  padding: .2em .25em .15em;
  border: solid .08em #eee;
  border-radius: .1em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left, #app .yunoHeader .yunoLoginDropdown .fa-pull-left.dropdown-toggle:after, #app .yunoFooter .linkList li.iconsBlock div a.fa-pull-left:before, #app .yunoFooter .linkList.withIcon li.android a.fa-pull-left:before, #app .field .datepicker .icon .fa-pull-left.mdi-chevron-left:after, #app .field .datepicker .icon .fa-pull-left.mdi-chevron-right:after, #app .field .autocomplete .control.has-icons-right .icon .fa-pull-left.mdi-close-circle:after, .modal.yunoModal .fa-pull-left.modal-close:after, .fa-pull-left.modal-close:after {
  margin-right: .3em;
}

.fa.fa-pull-right, #app .yunoHeader .yunoLoginDropdown .fa-pull-right.dropdown-toggle:after, #app .yunoFooter .linkList li.iconsBlock div a.fa-pull-right:before, #app .yunoFooter .linkList.withIcon li.android a.fa-pull-right:before, #app .field .datepicker .icon .fa-pull-right.mdi-chevron-left:after, #app .field .datepicker .icon .fa-pull-right.mdi-chevron-right:after, #app .field .autocomplete .control.has-icons-right .icon .fa-pull-right.mdi-close-circle:after, .modal.yunoModal .fa-pull-right.modal-close:after, .fa-pull-right.modal-close:after {
  margin-left: .3em;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.fa.pull-left, #app .yunoHeader .yunoLoginDropdown .pull-left.dropdown-toggle:after, #app .yunoFooter .linkList li.iconsBlock div a.pull-left:before, #app .yunoFooter .linkList.withIcon li.android a.pull-left:before, #app .field .datepicker .icon .pull-left.mdi-chevron-left:after, #app .field .datepicker .icon .pull-left.mdi-chevron-right:after, #app .field .autocomplete .control.has-icons-right .icon .pull-left.mdi-close-circle:after, .modal.yunoModal .pull-left.modal-close:after, .pull-left.modal-close:after {
  margin-right: .3em;
}

.fa.pull-right, #app .yunoHeader .yunoLoginDropdown .pull-right.dropdown-toggle:after, #app .yunoFooter .linkList li.iconsBlock div a.pull-right:before, #app .yunoFooter .linkList.withIcon li.android a.pull-right:before, #app .field .datepicker .icon .pull-right.mdi-chevron-left:after, #app .field .datepicker .icon .pull-right.mdi-chevron-right:after, #app .field .autocomplete .control.has-icons-right .icon .pull-right.mdi-close-circle:after, .modal.yunoModal .pull-right.modal-close:after, .pull-right.modal-close:after {
  margin-left: .3em;
}

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
  transform: scale(1, -1);
}

:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  -webkit-filter: none;
          filter: none;
}

.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}

.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: #fff;
}

.fa-glass:before {
  content: "\f000";
}

.fa-music:before {
  content: "\f001";
}

.fa-search:before {
  content: "\f002";
}

.fa-envelope-o:before {
  content: "\f003";
}

.fa-heart:before {
  content: "\f004";
}

.fa-star:before {
  content: "\f005";
}

.fa-star-o:before {
  content: "\f006";
}

.fa-user:before {
  content: "\f007";
}

.fa-film:before {
  content: "\f008";
}

.fa-th-large:before {
  content: "\f009";
}

.fa-th:before {
  content: "\f00a";
}

.fa-th-list:before {
  content: "\f00b";
}

.fa-check:before {
  content: "\f00c";
}

.fa-remove:before, .fa-close:before, .fa-times:before {
  content: "\f00d";
}

.fa-search-plus:before {
  content: "\f00e";
}

.fa-search-minus:before {
  content: "\f010";
}

.fa-power-off:before {
  content: "\f011";
}

.fa-signal:before {
  content: "\f012";
}

.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

.fa-trash-o:before {
  content: "\f014";
}

.fa-home:before {
  content: "\f015";
}

.fa-file-o:before {
  content: "\f016";
}

.fa-clock-o:before {
  content: "\f017";
}

.fa-road:before {
  content: "\f018";
}

.fa-download:before {
  content: "\f019";
}

.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

.fa-inbox:before {
  content: "\f01c";
}

.fa-play-circle-o:before {
  content: "\f01d";
}

.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

.fa-refresh:before {
  content: "\f021";
}

.fa-list-alt:before {
  content: "\f022";
}

.fa-lock:before {
  content: "\f023";
}

.fa-flag:before {
  content: "\f024";
}

.fa-headphones:before {
  content: "\f025";
}

.fa-volume-off:before {
  content: "\f026";
}

.fa-volume-down:before {
  content: "\f027";
}

.fa-volume-up:before {
  content: "\f028";
}

.fa-qrcode:before {
  content: "\f029";
}

.fa-barcode:before {
  content: "\f02a";
}

.fa-tag:before {
  content: "\f02b";
}

.fa-tags:before {
  content: "\f02c";
}

.fa-book:before {
  content: "\f02d";
}

.fa-bookmark:before {
  content: "\f02e";
}

.fa-print:before {
  content: "\f02f";
}

.fa-camera:before {
  content: "\f030";
}

.fa-font:before {
  content: "\f031";
}

.fa-bold:before {
  content: "\f032";
}

.fa-italic:before {
  content: "\f033";
}

.fa-text-height:before {
  content: "\f034";
}

.fa-text-width:before {
  content: "\f035";
}

.fa-align-left:before {
  content: "\f036";
}

.fa-align-center:before {
  content: "\f037";
}

.fa-align-right:before {
  content: "\f038";
}

.fa-align-justify:before {
  content: "\f039";
}

.fa-list:before {
  content: "\f03a";
}

.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

.fa-indent:before {
  content: "\f03c";
}

.fa-video-camera:before {
  content: "\f03d";
}

.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

.fa-pencil:before {
  content: "\f040";
}

.fa-map-marker:before {
  content: "\f041";
}

.fa-adjust:before {
  content: "\f042";
}

.fa-tint:before {
  content: "\f043";
}

.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

.fa-share-square-o:before {
  content: "\f045";
}

.fa-check-square-o:before {
  content: "\f046";
}

.fa-arrows:before {
  content: "\f047";
}

.fa-step-backward:before {
  content: "\f048";
}

.fa-fast-backward:before {
  content: "\f049";
}

.fa-backward:before {
  content: "\f04a";
}

.fa-play:before {
  content: "\f04b";
}

.fa-pause:before {
  content: "\f04c";
}

.fa-stop:before {
  content: "\f04d";
}

.fa-forward:before {
  content: "\f04e";
}

.fa-fast-forward:before {
  content: "\f050";
}

.fa-step-forward:before {
  content: "\f051";
}

.fa-eject:before {
  content: "\f052";
}

.fa-chevron-left:before {
  content: "\f053";
}

.fa-chevron-right:before {
  content: "\f054";
}

.fa-plus-circle:before {
  content: "\f055";
}

.fa-minus-circle:before {
  content: "\f056";
}

.fa-times-circle:before {
  content: "\f057";
}

.fa-check-circle:before {
  content: "\f058";
}

.fa-question-circle:before {
  content: "\f059";
}

.fa-info-circle:before {
  content: "\f05a";
}

.fa-crosshairs:before {
  content: "\f05b";
}

.fa-times-circle-o:before {
  content: "\f05c";
}

.fa-check-circle-o:before {
  content: "\f05d";
}

.fa-ban:before {
  content: "\f05e";
}

.fa-arrow-left:before {
  content: "\f060";
}

.fa-arrow-right:before {
  content: "\f061";
}

.fa-arrow-up:before {
  content: "\f062";
}

.fa-arrow-down:before {
  content: "\f063";
}

.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

.fa-expand:before {
  content: "\f065";
}

.fa-compress:before {
  content: "\f066";
}

.fa-plus:before {
  content: "\f067";
}

.fa-minus:before {
  content: "\f068";
}

.fa-asterisk:before {
  content: "\f069";
}

.fa-exclamation-circle:before {
  content: "\f06a";
}

.fa-gift:before {
  content: "\f06b";
}

.fa-leaf:before {
  content: "\f06c";
}

.fa-fire:before {
  content: "\f06d";
}

.fa-eye:before {
  content: "\f06e";
}

.fa-eye-slash:before {
  content: "\f070";
}

.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

.fa-plane:before {
  content: "\f072";
}

.fa-calendar:before {
  content: "\f073";
}

.fa-random:before {
  content: "\f074";
}

.fa-comment:before {
  content: "\f075";
}

.fa-magnet:before {
  content: "\f076";
}

.fa-chevron-up:before {
  content: "\f077";
}

.fa-chevron-down:before {
  content: "\f078";
}

.fa-retweet:before {
  content: "\f079";
}

.fa-shopping-cart:before {
  content: "\f07a";
}

.fa-folder:before {
  content: "\f07b";
}

.fa-folder-open:before {
  content: "\f07c";
}

.fa-arrows-v:before {
  content: "\f07d";
}

.fa-arrows-h:before {
  content: "\f07e";
}

.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

.fa-twitter-square:before {
  content: "\f081";
}

.fa-facebook-square:before {
  content: "\f082";
}

.fa-camera-retro:before {
  content: "\f083";
}

.fa-key:before {
  content: "\f084";
}

.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

.fa-comments:before {
  content: "\f086";
}

.fa-thumbs-o-up:before {
  content: "\f087";
}

.fa-thumbs-o-down:before {
  content: "\f088";
}

.fa-star-half:before {
  content: "\f089";
}

.fa-heart-o:before {
  content: "\f08a";
}

.fa-sign-out:before {
  content: "\f08b";
}

.fa-linkedin-square:before {
  content: "\f08c";
}

.fa-thumb-tack:before {
  content: "\f08d";
}

.fa-external-link:before {
  content: "\f08e";
}

.fa-sign-in:before {
  content: "\f090";
}

.fa-trophy:before {
  content: "\f091";
}

.fa-github-square:before {
  content: "\f092";
}

.fa-upload:before {
  content: "\f093";
}

.fa-lemon-o:before {
  content: "\f094";
}

.fa-phone:before {
  content: "\f095";
}

.fa-square-o:before {
  content: "\f096";
}

.fa-bookmark-o:before {
  content: "\f097";
}

.fa-phone-square:before {
  content: "\f098";
}

.fa-twitter:before {
  content: "\f099";
}

.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

.fa-github:before {
  content: "\f09b";
}

.fa-unlock:before {
  content: "\f09c";
}

.fa-credit-card:before {
  content: "\f09d";
}

.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

.fa-hdd-o:before {
  content: "\f0a0";
}

.fa-bullhorn:before {
  content: "\f0a1";
}

.fa-bell:before {
  content: "\f0f3";
}

.fa-certificate:before {
  content: "\f0a3";
}

.fa-hand-o-right:before {
  content: "\f0a4";
}

.fa-hand-o-left:before {
  content: "\f0a5";
}

.fa-hand-o-up:before {
  content: "\f0a6";
}

.fa-hand-o-down:before {
  content: "\f0a7";
}

.fa-arrow-circle-left:before {
  content: "\f0a8";
}

.fa-arrow-circle-right:before {
  content: "\f0a9";
}

.fa-arrow-circle-up:before {
  content: "\f0aa";
}

.fa-arrow-circle-down:before {
  content: "\f0ab";
}

.fa-globe:before {
  content: "\f0ac";
}

.fa-wrench:before {
  content: "\f0ad";
}

.fa-tasks:before {
  content: "\f0ae";
}

.fa-filter:before {
  content: "\f0b0";
}

.fa-briefcase:before {
  content: "\f0b1";
}

.fa-arrows-alt:before {
  content: "\f0b2";
}

.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

.fa-cloud:before {
  content: "\f0c2";
}

.fa-flask:before {
  content: "\f0c3";
}

.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

.fa-paperclip:before {
  content: "\f0c6";
}

.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

.fa-square:before {
  content: "\f0c8";
}

.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

.fa-list-ul:before {
  content: "\f0ca";
}

.fa-list-ol:before {
  content: "\f0cb";
}

.fa-strikethrough:before {
  content: "\f0cc";
}

.fa-underline:before {
  content: "\f0cd";
}

.fa-table:before {
  content: "\f0ce";
}

.fa-magic:before {
  content: "\f0d0";
}

.fa-truck:before {
  content: "\f0d1";
}

.fa-pinterest:before {
  content: "\f0d2";
}

.fa-pinterest-square:before {
  content: "\f0d3";
}

.fa-google-plus-square:before {
  content: "\f0d4";
}

.fa-google-plus:before {
  content: "\f0d5";
}

.fa-money:before {
  content: "\f0d6";
}

.fa-caret-down:before {
  content: "\f0d7";
}

.fa-caret-up:before {
  content: "\f0d8";
}

.fa-caret-left:before {
  content: "\f0d9";
}

.fa-caret-right:before {
  content: "\f0da";
}

.fa-columns:before {
  content: "\f0db";
}

.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

.fa-envelope:before {
  content: "\f0e0";
}

.fa-linkedin:before {
  content: "\f0e1";
}

.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

.fa-comment-o:before {
  content: "\f0e5";
}

.fa-comments-o:before {
  content: "\f0e6";
}

.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

.fa-sitemap:before {
  content: "\f0e8";
}

.fa-umbrella:before {
  content: "\f0e9";
}

.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

.fa-lightbulb-o:before {
  content: "\f0eb";
}

.fa-exchange:before {
  content: "\f0ec";
}

.fa-cloud-download:before {
  content: "\f0ed";
}

.fa-cloud-upload:before {
  content: "\f0ee";
}

.fa-user-md:before {
  content: "\f0f0";
}

.fa-stethoscope:before {
  content: "\f0f1";
}

.fa-suitcase:before {
  content: "\f0f2";
}

.fa-bell-o:before {
  content: "\f0a2";
}

.fa-coffee:before {
  content: "\f0f4";
}

.fa-cutlery:before {
  content: "\f0f5";
}

.fa-file-text-o:before {
  content: "\f0f6";
}

.fa-building-o:before {
  content: "\f0f7";
}

.fa-hospital-o:before {
  content: "\f0f8";
}

.fa-ambulance:before {
  content: "\f0f9";
}

.fa-medkit:before {
  content: "\f0fa";
}

.fa-fighter-jet:before {
  content: "\f0fb";
}

.fa-beer:before {
  content: "\f0fc";
}

.fa-h-square:before {
  content: "\f0fd";
}

.fa-plus-square:before {
  content: "\f0fe";
}

.fa-angle-double-left:before {
  content: "\f100";
}

.fa-angle-double-right:before {
  content: "\f101";
}

.fa-angle-double-up:before {
  content: "\f102";
}

.fa-angle-double-down:before {
  content: "\f103";
}

.fa-angle-left:before {
  content: "\f104";
}

.fa-angle-right:before {
  content: "\f105";
}

.fa-angle-up:before {
  content: "\f106";
}

.fa-angle-down:before {
  content: "\f107";
}

.fa-desktop:before {
  content: "\f108";
}

.fa-laptop:before {
  content: "\f109";
}

.fa-tablet:before {
  content: "\f10a";
}

.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

.fa-circle-o:before {
  content: "\f10c";
}

.fa-quote-left:before {
  content: "\f10d";
}

.fa-quote-right:before {
  content: "\f10e";
}

.fa-spinner:before {
  content: "\f110";
}

.fa-circle:before {
  content: "\f111";
}

.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

.fa-github-alt:before {
  content: "\f113";
}

.fa-folder-o:before {
  content: "\f114";
}

.fa-folder-open-o:before {
  content: "\f115";
}

.fa-smile-o:before {
  content: "\f118";
}

.fa-frown-o:before {
  content: "\f119";
}

.fa-meh-o:before {
  content: "\f11a";
}

.fa-gamepad:before {
  content: "\f11b";
}

.fa-keyboard-o:before {
  content: "\f11c";
}

.fa-flag-o:before {
  content: "\f11d";
}

.fa-flag-checkered:before {
  content: "\f11e";
}

.fa-terminal:before {
  content: "\f120";
}

.fa-code:before {
  content: "\f121";
}

.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

.fa-location-arrow:before {
  content: "\f124";
}

.fa-crop:before {
  content: "\f125";
}

.fa-code-fork:before {
  content: "\f126";
}

.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

.fa-question:before {
  content: "\f128";
}

.fa-info:before {
  content: "\f129";
}

.fa-exclamation:before {
  content: "\f12a";
}

.fa-superscript:before {
  content: "\f12b";
}

.fa-subscript:before {
  content: "\f12c";
}

.fa-eraser:before {
  content: "\f12d";
}

.fa-puzzle-piece:before {
  content: "\f12e";
}

.fa-microphone:before {
  content: "\f130";
}

.fa-microphone-slash:before {
  content: "\f131";
}

.fa-shield:before {
  content: "\f132";
}

.fa-calendar-o:before {
  content: "\f133";
}

.fa-fire-extinguisher:before {
  content: "\f134";
}

.fa-rocket:before {
  content: "\f135";
}

.fa-maxcdn:before {
  content: "\f136";
}

.fa-chevron-circle-left:before {
  content: "\f137";
}

.fa-chevron-circle-right:before {
  content: "\f138";
}

.fa-chevron-circle-up:before {
  content: "\f139";
}

.fa-chevron-circle-down:before {
  content: "\f13a";
}

.fa-html5:before {
  content: "\f13b";
}

.fa-css3:before {
  content: "\f13c";
}

.fa-anchor:before {
  content: "\f13d";
}

.fa-unlock-alt:before {
  content: "\f13e";
}

.fa-bullseye:before {
  content: "\f140";
}

.fa-ellipsis-h:before {
  content: "\f141";
}

.fa-ellipsis-v:before {
  content: "\f142";
}

.fa-rss-square:before {
  content: "\f143";
}

.fa-play-circle:before {
  content: "\f144";
}

.fa-ticket:before {
  content: "\f145";
}

.fa-minus-square:before {
  content: "\f146";
}

.fa-minus-square-o:before {
  content: "\f147";
}

.fa-level-up:before {
  content: "\f148";
}

.fa-level-down:before {
  content: "\f149";
}

.fa-check-square:before {
  content: "\f14a";
}

.fa-pencil-square:before {
  content: "\f14b";
}

.fa-external-link-square:before {
  content: "\f14c";
}

.fa-share-square:before {
  content: "\f14d";
}

.fa-compass:before {
  content: "\f14e";
}

.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

.fa-gbp:before {
  content: "\f154";
}

.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

.fa-won:before, .fa-krw:before {
  content: "\f159";
}

.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

.fa-file:before {
  content: "\f15b";
}

.fa-file-text:before {
  content: "\f15c";
}

.fa-sort-alpha-asc:before {
  content: "\f15d";
}

.fa-sort-alpha-desc:before {
  content: "\f15e";
}

.fa-sort-amount-asc:before {
  content: "\f160";
}

.fa-sort-amount-desc:before {
  content: "\f161";
}

.fa-sort-numeric-asc:before {
  content: "\f162";
}

.fa-sort-numeric-desc:before {
  content: "\f163";
}

.fa-thumbs-up:before {
  content: "\f164";
}

.fa-thumbs-down:before {
  content: "\f165";
}

.fa-youtube-square:before {
  content: "\f166";
}

.fa-youtube:before {
  content: "\f167";
}

.fa-xing:before {
  content: "\f168";
}

.fa-xing-square:before {
  content: "\f169";
}

.fa-youtube-play:before {
  content: "\f16a";
}

.fa-dropbox:before {
  content: "\f16b";
}

.fa-stack-overflow:before {
  content: "\f16c";
}

.fa-instagram:before {
  content: "\f16d";
}

.fa-flickr:before {
  content: "\f16e";
}

.fa-adn:before {
  content: "\f170";
}

.fa-bitbucket:before {
  content: "\f171";
}

.fa-bitbucket-square:before {
  content: "\f172";
}

.fa-tumblr:before {
  content: "\f173";
}

.fa-tumblr-square:before {
  content: "\f174";
}

.fa-long-arrow-down:before {
  content: "\f175";
}

.fa-long-arrow-up:before {
  content: "\f176";
}

.fa-long-arrow-left:before {
  content: "\f177";
}

.fa-long-arrow-right:before {
  content: "\f178";
}

.fa-apple:before {
  content: "\f179";
}

.fa-windows:before {
  content: "\f17a";
}

.fa-android:before {
  content: "\f17b";
}

.fa-linux:before {
  content: "\f17c";
}

.fa-dribbble:before {
  content: "\f17d";
}

.fa-skype:before {
  content: "\f17e";
}

.fa-foursquare:before {
  content: "\f180";
}

.fa-trello:before {
  content: "\f181";
}

.fa-female:before {
  content: "\f182";
}

.fa-male:before {
  content: "\f183";
}

.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

.fa-sun-o:before {
  content: "\f185";
}

.fa-moon-o:before {
  content: "\f186";
}

.fa-archive:before {
  content: "\f187";
}

.fa-bug:before {
  content: "\f188";
}

.fa-vk:before {
  content: "\f189";
}

.fa-weibo:before {
  content: "\f18a";
}

.fa-renren:before {
  content: "\f18b";
}

.fa-pagelines:before {
  content: "\f18c";
}

.fa-stack-exchange:before {
  content: "\f18d";
}

.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

.fa-arrow-circle-o-left:before {
  content: "\f190";
}

.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

.fa-dot-circle-o:before {
  content: "\f192";
}

.fa-wheelchair:before {
  content: "\f193";
}

.fa-vimeo-square:before {
  content: "\f194";
}

.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

.fa-plus-square-o:before {
  content: "\f196";
}

.fa-space-shuttle:before {
  content: "\f197";
}

.fa-slack:before {
  content: "\f198";
}

.fa-envelope-square:before {
  content: "\f199";
}

.fa-wordpress:before {
  content: "\f19a";
}

.fa-openid:before {
  content: "\f19b";
}

.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

.fa-yahoo:before {
  content: "\f19e";
}

.fa-google:before {
  content: "\f1a0";
}

.fa-reddit:before {
  content: "\f1a1";
}

.fa-reddit-square:before {
  content: "\f1a2";
}

.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

.fa-stumbleupon:before {
  content: "\f1a4";
}

.fa-delicious:before {
  content: "\f1a5";
}

.fa-digg:before {
  content: "\f1a6";
}

.fa-pied-piper-pp:before {
  content: "\f1a7";
}

.fa-pied-piper-alt:before {
  content: "\f1a8";
}

.fa-drupal:before {
  content: "\f1a9";
}

.fa-joomla:before {
  content: "\f1aa";
}

.fa-language:before {
  content: "\f1ab";
}

.fa-fax:before {
  content: "\f1ac";
}

.fa-building:before {
  content: "\f1ad";
}

.fa-child:before {
  content: "\f1ae";
}

.fa-paw:before {
  content: "\f1b0";
}

.fa-spoon:before {
  content: "\f1b1";
}

.fa-cube:before {
  content: "\f1b2";
}

.fa-cubes:before {
  content: "\f1b3";
}

.fa-behance:before {
  content: "\f1b4";
}

.fa-behance-square:before {
  content: "\f1b5";
}

.fa-steam:before {
  content: "\f1b6";
}

.fa-steam-square:before {
  content: "\f1b7";
}

.fa-recycle:before {
  content: "\f1b8";
}

.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

.fa-tree:before {
  content: "\f1bb";
}

.fa-spotify:before {
  content: "\f1bc";
}

.fa-deviantart:before {
  content: "\f1bd";
}

.fa-soundcloud:before {
  content: "\f1be";
}

.fa-database:before {
  content: "\f1c0";
}

.fa-file-pdf-o:before {
  content: "\f1c1";
}

.fa-file-word-o:before {
  content: "\f1c2";
}

.fa-file-excel-o:before {
  content: "\f1c3";
}

.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

.fa-file-code-o:before {
  content: "\f1c9";
}

.fa-vine:before {
  content: "\f1ca";
}

.fa-codepen:before {
  content: "\f1cb";
}

.fa-jsfiddle:before {
  content: "\f1cc";
}

.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

.fa-circle-o-notch:before {
  content: "\f1ce";
}

.fa-ra:before, .fa-resistance:before, .fa-rebel:before {
  content: "\f1d0";
}

.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

.fa-git-square:before {
  content: "\f1d2";
}

.fa-git:before {
  content: "\f1d3";
}

.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

.fa-tencent-weibo:before {
  content: "\f1d5";
}

.fa-qq:before {
  content: "\f1d6";
}

.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

.fa-history:before {
  content: "\f1da";
}

.fa-circle-thin:before {
  content: "\f1db";
}

.fa-header:before {
  content: "\f1dc";
}

.fa-paragraph:before {
  content: "\f1dd";
}

.fa-sliders:before {
  content: "\f1de";
}

.fa-share-alt:before {
  content: "\f1e0";
}

.fa-share-alt-square:before {
  content: "\f1e1";
}

.fa-bomb:before {
  content: "\f1e2";
}

.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

.fa-tty:before {
  content: "\f1e4";
}

.fa-binoculars:before {
  content: "\f1e5";
}

.fa-plug:before {
  content: "\f1e6";
}

.fa-slideshare:before {
  content: "\f1e7";
}

.fa-twitch:before {
  content: "\f1e8";
}

.fa-yelp:before {
  content: "\f1e9";
}

.fa-newspaper-o:before {
  content: "\f1ea";
}

.fa-wifi:before {
  content: "\f1eb";
}

.fa-calculator:before {
  content: "\f1ec";
}

.fa-paypal:before {
  content: "\f1ed";
}

.fa-google-wallet:before {
  content: "\f1ee";
}

.fa-cc-visa:before {
  content: "\f1f0";
}

.fa-cc-mastercard:before {
  content: "\f1f1";
}

.fa-cc-discover:before {
  content: "\f1f2";
}

.fa-cc-amex:before {
  content: "\f1f3";
}

.fa-cc-paypal:before {
  content: "\f1f4";
}

.fa-cc-stripe:before {
  content: "\f1f5";
}

.fa-bell-slash:before {
  content: "\f1f6";
}

.fa-bell-slash-o:before {
  content: "\f1f7";
}

.fa-trash:before {
  content: "\f1f8";
}

.fa-copyright:before {
  content: "\f1f9";
}

.fa-at:before {
  content: "\f1fa";
}

.fa-eyedropper:before {
  content: "\f1fb";
}

.fa-paint-brush:before {
  content: "\f1fc";
}

.fa-birthday-cake:before {
  content: "\f1fd";
}

.fa-area-chart:before {
  content: "\f1fe";
}

.fa-pie-chart:before {
  content: "\f200";
}

.fa-line-chart:before {
  content: "\f201";
}

.fa-lastfm:before {
  content: "\f202";
}

.fa-lastfm-square:before {
  content: "\f203";
}

.fa-toggle-off:before {
  content: "\f204";
}

.fa-toggle-on:before {
  content: "\f205";
}

.fa-bicycle:before {
  content: "\f206";
}

.fa-bus:before {
  content: "\f207";
}

.fa-ioxhost:before {
  content: "\f208";
}

.fa-angellist:before {
  content: "\f209";
}

.fa-cc:before {
  content: "\f20a";
}

.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

.fa-meanpath:before {
  content: "\f20c";
}

.fa-buysellads:before {
  content: "\f20d";
}

.fa-connectdevelop:before {
  content: "\f20e";
}

.fa-dashcube:before {
  content: "\f210";
}

.fa-forumbee:before {
  content: "\f211";
}

.fa-leanpub:before {
  content: "\f212";
}

.fa-sellsy:before {
  content: "\f213";
}

.fa-shirtsinbulk:before {
  content: "\f214";
}

.fa-simplybuilt:before {
  content: "\f215";
}

.fa-skyatlas:before {
  content: "\f216";
}

.fa-cart-plus:before {
  content: "\f217";
}

.fa-cart-arrow-down:before {
  content: "\f218";
}

.fa-diamond:before {
  content: "\f219";
}

.fa-ship:before {
  content: "\f21a";
}

.fa-user-secret:before {
  content: "\f21b";
}

.fa-motorcycle:before {
  content: "\f21c";
}

.fa-street-view:before {
  content: "\f21d";
}

.fa-heartbeat:before {
  content: "\f21e";
}

.fa-venus:before {
  content: "\f221";
}

.fa-mars:before {
  content: "\f222";
}

.fa-mercury:before {
  content: "\f223";
}

.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

.fa-transgender-alt:before {
  content: "\f225";
}

.fa-venus-double:before {
  content: "\f226";
}

.fa-mars-double:before {
  content: "\f227";
}

.fa-venus-mars:before {
  content: "\f228";
}

.fa-mars-stroke:before {
  content: "\f229";
}

.fa-mars-stroke-v:before {
  content: "\f22a";
}

.fa-mars-stroke-h:before {
  content: "\f22b";
}

.fa-neuter:before {
  content: "\f22c";
}

.fa-genderless:before {
  content: "\f22d";
}

.fa-facebook-official:before {
  content: "\f230";
}

.fa-pinterest-p:before {
  content: "\f231";
}

.fa-whatsapp:before {
  content: "\f232";
}

.fa-server:before {
  content: "\f233";
}

.fa-user-plus:before {
  content: "\f234";
}

.fa-user-times:before {
  content: "\f235";
}

.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

.fa-viacoin:before {
  content: "\f237";
}

.fa-train:before {
  content: "\f238";
}

.fa-subway:before {
  content: "\f239";
}

.fa-medium:before {
  content: "\f23a";
}

.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

.fa-optin-monster:before {
  content: "\f23c";
}

.fa-opencart:before {
  content: "\f23d";
}

.fa-expeditedssl:before {
  content: "\f23e";
}

.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before {
  content: "\f240";
}

.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

.fa-mouse-pointer:before {
  content: "\f245";
}

.fa-i-cursor:before {
  content: "\f246";
}

.fa-object-group:before {
  content: "\f247";
}

.fa-object-ungroup:before {
  content: "\f248";
}

.fa-sticky-note:before {
  content: "\f249";
}

.fa-sticky-note-o:before {
  content: "\f24a";
}

.fa-cc-jcb:before {
  content: "\f24b";
}

.fa-cc-diners-club:before {
  content: "\f24c";
}

.fa-clone:before {
  content: "\f24d";
}

.fa-balance-scale:before {
  content: "\f24e";
}

.fa-hourglass-o:before {
  content: "\f250";
}

.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

.fa-hourglass:before {
  content: "\f254";
}

.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

.fa-hand-scissors-o:before {
  content: "\f257";
}

.fa-hand-lizard-o:before {
  content: "\f258";
}

.fa-hand-spock-o:before {
  content: "\f259";
}

.fa-hand-pointer-o:before {
  content: "\f25a";
}

.fa-hand-peace-o:before {
  content: "\f25b";
}

.fa-trademark:before {
  content: "\f25c";
}

.fa-registered:before {
  content: "\f25d";
}

.fa-creative-commons:before {
  content: "\f25e";
}

.fa-gg:before {
  content: "\f260";
}

.fa-gg-circle:before {
  content: "\f261";
}

.fa-tripadvisor:before {
  content: "\f262";
}

.fa-odnoklassniki:before {
  content: "\f263";
}

.fa-odnoklassniki-square:before {
  content: "\f264";
}

.fa-get-pocket:before {
  content: "\f265";
}

.fa-wikipedia-w:before {
  content: "\f266";
}

.fa-safari:before {
  content: "\f267";
}

.fa-chrome:before {
  content: "\f268";
}

.fa-firefox:before {
  content: "\f269";
}

.fa-opera:before {
  content: "\f26a";
}

.fa-internet-explorer:before {
  content: "\f26b";
}

.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

.fa-contao:before {
  content: "\f26d";
}

.fa-500px:before {
  content: "\f26e";
}

.fa-amazon:before {
  content: "\f270";
}

.fa-calendar-plus-o:before {
  content: "\f271";
}

.fa-calendar-minus-o:before {
  content: "\f272";
}

.fa-calendar-times-o:before {
  content: "\f273";
}

.fa-calendar-check-o:before {
  content: "\f274";
}

.fa-industry:before {
  content: "\f275";
}

.fa-map-pin:before {
  content: "\f276";
}

.fa-map-signs:before {
  content: "\f277";
}

.fa-map-o:before {
  content: "\f278";
}

.fa-map:before {
  content: "\f279";
}

.fa-commenting:before {
  content: "\f27a";
}

.fa-commenting-o:before {
  content: "\f27b";
}

.fa-houzz:before {
  content: "\f27c";
}

.fa-vimeo:before {
  content: "\f27d";
}

.fa-black-tie:before {
  content: "\f27e";
}

.fa-fonticons:before {
  content: "\f280";
}

.fa-reddit-alien:before {
  content: "\f281";
}

.fa-edge:before {
  content: "\f282";
}

.fa-credit-card-alt:before {
  content: "\f283";
}

.fa-codiepie:before {
  content: "\f284";
}

.fa-modx:before {
  content: "\f285";
}

.fa-fort-awesome:before {
  content: "\f286";
}

.fa-usb:before {
  content: "\f287";
}

.fa-product-hunt:before {
  content: "\f288";
}

.fa-mixcloud:before {
  content: "\f289";
}

.fa-scribd:before {
  content: "\f28a";
}

.fa-pause-circle:before {
  content: "\f28b";
}

.fa-pause-circle-o:before {
  content: "\f28c";
}

.fa-stop-circle:before {
  content: "\f28d";
}

.fa-stop-circle-o:before {
  content: "\f28e";
}

.fa-shopping-bag:before {
  content: "\f290";
}

.fa-shopping-basket:before {
  content: "\f291";
}

.fa-hashtag:before {
  content: "\f292";
}

.fa-bluetooth:before {
  content: "\f293";
}

.fa-bluetooth-b:before {
  content: "\f294";
}

.fa-percent:before {
  content: "\f295";
}

.fa-gitlab:before {
  content: "\f296";
}

.fa-wpbeginner:before {
  content: "\f297";
}

.fa-wpforms:before {
  content: "\f298";
}

.fa-envira:before {
  content: "\f299";
}

.fa-universal-access:before {
  content: "\f29a";
}

.fa-wheelchair-alt:before {
  content: "\f29b";
}

.fa-question-circle-o:before {
  content: "\f29c";
}

.fa-blind:before {
  content: "\f29d";
}

.fa-audio-description:before {
  content: "\f29e";
}

.fa-volume-control-phone:before {
  content: "\f2a0";
}

.fa-braille:before {
  content: "\f2a1";
}

.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before {
  content: "\f2a4";
}

.fa-glide:before {
  content: "\f2a5";
}

.fa-glide-g:before {
  content: "\f2a6";
}

.fa-signing:before, .fa-sign-language:before {
  content: "\f2a7";
}

.fa-low-vision:before {
  content: "\f2a8";
}

.fa-viadeo:before {
  content: "\f2a9";
}

.fa-viadeo-square:before {
  content: "\f2aa";
}

.fa-snapchat:before {
  content: "\f2ab";
}

.fa-snapchat-ghost:before {
  content: "\f2ac";
}

.fa-snapchat-square:before {
  content: "\f2ad";
}

.fa-pied-piper:before {
  content: "\f2ae";
}

.fa-first-order:before {
  content: "\f2b0";
}

.fa-yoast:before {
  content: "\f2b1";
}

.fa-themeisle:before {
  content: "\f2b2";
}

.fa-google-plus-circle:before, .fa-google-plus-official:before {
  content: "\f2b3";
}

.fa-fa:before, .fa-font-awesome:before {
  content: "\f2b4";
}

.fa-handshake-o:before {
  content: "\f2b5";
}

.fa-envelope-open:before {
  content: "\f2b6";
}

.fa-envelope-open-o:before {
  content: "\f2b7";
}

.fa-linode:before {
  content: "\f2b8";
}

.fa-address-book:before {
  content: "\f2b9";
}

.fa-address-book-o:before {
  content: "\f2ba";
}

.fa-vcard:before, .fa-address-card:before {
  content: "\f2bb";
}

.fa-vcard-o:before, .fa-address-card-o:before {
  content: "\f2bc";
}

.fa-user-circle:before {
  content: "\f2bd";
}

.fa-user-circle-o:before {
  content: "\f2be";
}

.fa-user-o:before {
  content: "\f2c0";
}

.fa-id-badge:before {
  content: "\f2c1";
}

.fa-drivers-license:before, .fa-id-card:before {
  content: "\f2c2";
}

.fa-drivers-license-o:before, .fa-id-card-o:before {
  content: "\f2c3";
}

.fa-quora:before {
  content: "\f2c4";
}

.fa-free-code-camp:before {
  content: "\f2c5";
}

.fa-telegram:before {
  content: "\f2c6";
}

.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before {
  content: "\f2c7";
}

.fa-thermometer-3:before, .fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

.fa-thermometer-2:before, .fa-thermometer-half:before {
  content: "\f2c9";
}

.fa-thermometer-1:before, .fa-thermometer-quarter:before {
  content: "\f2ca";
}

.fa-thermometer-0:before, .fa-thermometer-empty:before {
  content: "\f2cb";
}

.fa-shower:before {
  content: "\f2cc";
}

.fa-bathtub:before, .fa-s15:before, .fa-bath:before {
  content: "\f2cd";
}

.fa-podcast:before {
  content: "\f2ce";
}

.fa-window-maximize:before {
  content: "\f2d0";
}

.fa-window-minimize:before {
  content: "\f2d1";
}

.fa-window-restore:before {
  content: "\f2d2";
}

.fa-times-rectangle:before, .fa-window-close:before {
  content: "\f2d3";
}

.fa-times-rectangle-o:before, .fa-window-close-o:before {
  content: "\f2d4";
}

.fa-bandcamp:before {
  content: "\f2d5";
}

.fa-grav:before {
  content: "\f2d6";
}

.fa-etsy:before {
  content: "\f2d7";
}

.fa-imdb:before {
  content: "\f2d8";
}

.fa-ravelry:before {
  content: "\f2d9";
}

.fa-eercast:before {
  content: "\f2da";
}

.fa-microchip:before {
  content: "\f2db";
}

.fa-snowflake-o:before {
  content: "\f2dc";
}

.fa-superpowers:before {
  content: "\f2dd";
}

.fa-wpexplorer:before {
  content: "\f2de";
}

.fa-meetup:before {
  content: "\f2e0";
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}

/* Common ----- START */
html body {
  font-family: "Roboto";
  font-weight: 400;
  color: #000;
}

html body .to-top-container {
  display: none;
}

html body .zsiq_flt_rel {
  display: inline-block !important;
}

html body.logged-in .zsiq_floatmain, html body.page-template-invite .zsiq_floatmain {
  display: none  !important;
}

html body.logged-in .zsiq_flt_rel, html body.page-template-invite .zsiq_flt_rel {
  display: none !important;
}

.fusion-privacy-bar {
  display: none;
}

html .switch:hover input[type=checkbox]:checked + .check, html .switch input[type=checkbox]:checked + .check {
  background: #002F5A;
}

html .switch:hover input[type=checkbox]:checked + .check:hover, html .switch input[type=checkbox]:checked + .check:hover {
  background: #002F5A;
}

.dialog .modal-card-title {
  -ms-flex-negative: 1;
      flex-shrink: 1;
  line-height: normal;
}

@-webkit-keyframes slideDown {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
}

@keyframes slideDown {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
}

@-webkit-keyframes slideUp {
  0% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}

@keyframes slideUp {
  0% {
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}

.notices {
  position: fixed;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2em;
  overflow: hidden;
  z-index: 1000;
  pointer-events: none;
}

.notices.is-top {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.notices .toast {
  -ms-flex-preferred-size: auto;
      flex-basis: auto;
}

.notices .toast.is-yuno-info {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-animation-duration: .15s;
          animation-duration: .15s;
  margin: .5em 0;
  text-align: center;
  -webkit-box-shadow: rgba(0, 0, 0, 0.12) 0 1px 4px;
          box-shadow: rgba(0, 0, 0, 0.12) 0 1px 4px;
  border-radius: 2em;
  padding: .75em 1.5em;
  pointer-events: auto;
  opacity: .92;
  background-color: #3298dc;
  color: #FFF;
}

.notices .toast.is-top, .notices .toast.is-bottom {
  -ms-flex-item-align: center;
      -ms-grid-row-align: center;
      align-self: center;
}

.notices .toast.is-danger {
  background-color: red !important;
}

body.yunoLoaderEnabled {
  overflow: hidden;
}

body button.button, body a.button {
  height: auto;
}

.b-table.scrollable .table-wrapper, #app .yunoTabs .b-table.scrollable .table-wrapper {
  overflow-x: auto;
}

#app .notificationBar {
  -webkit-animation-duration: 0.5s;
          animation-duration: 0.5s;
  background-color: #FFDEA6;
  font-size: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: normal;
  position: relative;
}

#app .notificationBar:hover {
  background-color: #FFFBFF;
}

#app .notificationBar a {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 48px);
          flex: 0 0 calc(100% - 48px);
  padding: 15px 0 15px 24px;
  line-height: normal;
  font-weight: 500;
  text-align: center;
  color: #201A19;
}

#app .notificationBar a:hover {
  color: #201A19;
  text-decoration: none;
}

#app .notificationBar .material-icons-outlined, #app .notificationBar .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .control::before, #app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .notificationBar .control::before, #app .notificationBar .yunoHeader .yunoMainNav > ul > li.dropdown .dropdownToggle:after, #app .yunoHeader .yunoMainNav > ul > li.dropdown .notificationBar .dropdownToggle:after, #app .notificationBar .yunoHeader .yunoMainNav > ul > li .submenuLevel2 li.dropdown:after, #app .yunoHeader .yunoMainNav > ul > li .submenuLevel2 .notificationBar li.dropdown:after, #app .notificationBar .yunoFooter .whatsappSticky a::before, #app .yunoFooter .whatsappSticky .notificationBar a::before, #app .notificationBar .yunoFooter .linkList.withIcon li.zoomTest a:before, #app .yunoFooter .linkList.withIcon li.zoomTest .notificationBar a:before, #app .notificationBar .modal.yunoModal.loginSignupModal .modal-close::after, .modal.yunoModal.loginSignupModal #app .notificationBar .modal-close::after, #app .notificationBar .modal.yunoModal.lightTheme .modal-close::after, .modal.yunoModal.lightTheme #app .notificationBar .modal-close::after {
  cursor: pointer;
}

#app .notificationBar.notVisibleInDOM {
  display: none;
}

#app .notificationHide {
  -webkit-animation-name: slideDown;
          animation-name: slideDown;
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
}

#app .notificationShow {
  -webkit-animation-name: slideUp;
          animation-name: slideUp;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}

#app .appPrompt {
  position: relative;
  z-index: 10;
  padding: 10px 0;
  background: #e5e5e5;
}

#app .appPrompt .button.yunoPrimaryCTA {
  background-color: rgba(0, 0, 0, 0.3);
  color: black;
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.3);
  text-transform: uppercase;
}

#app .appPrompt .colGrid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .appPrompt .closePrompt {
  padding-right: 15px;
  padding-left: 5px;
}

#app .appPrompt .closePrompt .fa, #app .appPrompt .closePrompt .yunoHeader .yunoLoginDropdown .dropdown-toggle:after, #app .yunoHeader .yunoLoginDropdown .appPrompt .closePrompt .dropdown-toggle:after, #app .appPrompt .closePrompt .yunoFooter .linkList li.iconsBlock div a:before, #app .yunoFooter .linkList li.iconsBlock div .appPrompt .closePrompt a:before, #app .appPrompt .closePrompt .yunoFooter .linkList.withIcon li.android a:before, #app .yunoFooter .linkList.withIcon li.android .appPrompt .closePrompt a:before, #app .appPrompt .closePrompt .field .datepicker .icon .mdi-chevron-left:after, #app .field .datepicker .icon .appPrompt .closePrompt .mdi-chevron-left:after, #app .appPrompt .closePrompt .field .datepicker .icon .mdi-chevron-right:after, #app .field .datepicker .icon .appPrompt .closePrompt .mdi-chevron-right:after, #app .appPrompt .closePrompt .field .autocomplete .control.has-icons-right .icon .mdi-close-circle:after, #app .field .autocomplete .control.has-icons-right .icon .appPrompt .closePrompt .mdi-close-circle:after, #app .appPrompt .closePrompt .modal-close:after {
  font-size: 20px;
  cursor: pointer;
}

#app .appPrompt .appIcon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .appPrompt .appMedia {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .appPrompt .appInfo {
  margin-left: 15px;
}

#app .appPrompt .appInfo p {
  margin: 0;
}

#app .appPrompt .appInfo .infoTitle {
  font-size: 14px;
  margin-bottom: 5px;
}

#app .appPrompt .appInfo .infoCaption {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.5);
}

#app .appPrompt .iconWrap {
  width: 50px;
  height: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 10px;
  background: #FFF;
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: rgba(0, 0, 0, 0.117647) 0 0 40px;
          box-shadow: rgba(0, 0, 0, 0.117647) 0 0 40px;
}

#app .appPrompt .iconWrap img {
  width: 100%;
  height: auto;
}

#app .unauthorizedLogin a {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  background: #4285F4;
  border-radius: 4px;
  padding: 5px;
  color: #FFF;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: auto;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}

#app .unauthorizedLogin a .yuno-login-with-google-on-pages {
  display: block;
  font-weight: 500;
  padding: 0 16px;
}

#app .unauthorizedLogin a:hover {
  color: #FFF;
  text-decoration: none;
  background: #002F5A;
}

@-webkit-keyframes spinAround {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}

@keyframes spinAround {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}

@media (min-width: 1024px) {
  #app .container {
    max-width: 960px;
  }
}

@media (min-width: 1216px) {
  #app .container {
    max-width: 1140px;
  }
}

@media (min-width: 1408px) {
  #app .container {
    max-width: 1140px;
  }
}

#app .yunoLoader {
  display: none;
}

#app .yunoLoader.isActive {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoLoader.withOverlay {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  overflow: hidden;
  position: fixed;
  z-index: 7777;
}

#app .yunoLoader .yunoSpinner {
  width: 100px;
  height: 100px;
  position: static;
}

#app .loaderWrapper {
  height: 50px;
}

#app .loaderWrapper.big {
  height: 100px;
}

#app .smallLoader,
#app .smallLoader:before,
#app .smallLoader:after {
  background: #002F5A;
  -webkit-animation: load1 1s infinite ease-in-out;
  animation: load1 1s infinite ease-in-out;
  width: 1em;
  height: 4em;
}

#app .smallLoader {
  color: #002F5A;
  text-indent: -9999em;
  margin: 88px auto;
  position: relative;
  font-size: 8px;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

#app .smallLoader.withField {
  margin: 0 0 0 7px;
  font-size: 4px;
}

#app .smallLoader:before,
#app .smallLoader:after {
  position: absolute;
  top: 0;
  content: '';
}

#app .smallLoader:before {
  left: -1.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

#app .smallLoader:after {
  left: 1.5em;
}

@-webkit-keyframes load1 {
  0%,
  80%,
  100% {
    -webkit-box-shadow: 0 0;
            box-shadow: 0 0;
    height: 4em;
  }
  40% {
    -webkit-box-shadow: 0 -2em;
            box-shadow: 0 -2em;
    height: 5em;
  }
}

@keyframes load1 {
  0%,
  80%,
  100% {
    -webkit-box-shadow: 0 0;
            box-shadow: 0 0;
    height: 4em;
  }
  40% {
    -webkit-box-shadow: 0 -2em;
            box-shadow: 0 -2em;
    height: 5em;
  }
}

#app .imgFluid {
  width: 100%;
  height: auto;
}

#app[v-cloak] * {
  display: none;
}

#app figure {
  margin: 0;
}

#app ul, #app li {
  margin: 0;
  padding: 0;
  list-style: none;
}

#app .container.noOverflow {
  overflow: visible;
}

#app a {
  color: #a62027;
}

#app a:hover {
  color: #410004;
}

#app a.button {
  -webkit-appearance: none;
}

#app .primaryColor {
  color: #002F5A;
}

#app .fsLarger {
  font-size: 18px;
}

#app .marginBtm16 {
  margin-bottom: 16px;
}

#app .marginBtm30 {
  margin-bottom: 30px;
}

#app .padBtm30 {
  padding-bottom: 30px;
}

#app .padBtm60 {
  padding-bottom: 60px;
}

#app .commonTitle {
  color: #000;
  font-size: 32px;
  margin: 30px 0 0;
}

#app .button {
  border-radius: 3px;
  padding: 7px 15px;
  font-size: 14px;
  line-height: normal;
}

#app .button.big {
  padding: 10px 16px;
}

#app .button.gapTopLargest {
  margin-top: 30px;
}

#app .button.withIcon > span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .button.withIcon > span .material-icons-outlined, #app .button.withIcon > span .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .control::before, #app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .button.withIcon > span .control::before, #app .button.withIcon > span .yunoHeader .yunoMainNav > ul > li.dropdown .dropdownToggle:after, #app .yunoHeader .yunoMainNav > ul > li.dropdown .button.withIcon > span .dropdownToggle:after, #app .button.withIcon > span .yunoHeader .yunoMainNav > ul > li .submenuLevel2 li.dropdown:after, #app .yunoHeader .yunoMainNav > ul > li .submenuLevel2 .button.withIcon > span li.dropdown:after, #app .button.withIcon > span .yunoFooter .whatsappSticky a::before, #app .yunoFooter .whatsappSticky .button.withIcon > span a::before, #app .button.withIcon > span .yunoFooter .linkList.withIcon li.zoomTest a:before, #app .yunoFooter .linkList.withIcon li.zoomTest .button.withIcon > span a:before, #app .button.withIcon > span .modal.yunoModal.loginSignupModal .modal-close::after, .modal.yunoModal.loginSignupModal #app .button.withIcon > span .modal-close::after, #app .button.withIcon > span .modal.yunoModal.lightTheme .modal-close::after, .modal.yunoModal.lightTheme #app .button.withIcon > span .modal-close::after, #app .button.withIcon > span .material-icons, #app .button.withIcon > span .yunoFooter .linkList.withIcon li.wifiSpeed a:before, #app .yunoFooter .linkList.withIcon li.wifiSpeed .button.withIcon > span a:before, #app .button.withIcon > span .yunoFooter .linkList.withIcon li.helpDesk a:before, #app .yunoFooter .linkList.withIcon li.helpDesk .button.withIcon > span a:before, #app .button.withIcon > span .yunoFooter .linkList.checkList li:not(.listTitle):before, #app .yunoFooter .linkList.checkList .button.withIcon > span li:not(.listTitle):before {
  margin-right: 5px;
}

#app .button .fa, #app .button .yunoHeader .yunoLoginDropdown .dropdown-toggle:after, #app .yunoHeader .yunoLoginDropdown .button .dropdown-toggle:after, #app .button .yunoFooter .linkList li.iconsBlock div a:before, #app .yunoFooter .linkList li.iconsBlock div .button a:before, #app .button .yunoFooter .linkList.withIcon li.android a:before, #app .yunoFooter .linkList.withIcon li.android .button a:before, #app .button .field .datepicker .icon .mdi-chevron-left:after, #app .field .datepicker .icon .button .mdi-chevron-left:after, #app .button .field .datepicker .icon .mdi-chevron-right:after, #app .field .datepicker .icon .button .mdi-chevron-right:after, #app .button .field .autocomplete .control.has-icons-right .icon .mdi-close-circle:after, #app .field .autocomplete .control.has-icons-right .icon .button .mdi-close-circle:after, #app .button .modal-close:after {
  padding-left: 5px;
}

#app .button:focus {
  outline: none;
}

#app .button.is-loading {
  position: relative;
}

#app .button.is-loading > span {
  color: transparent;
}

#app .button.is-loading:after {
  -webkit-animation: spinAround .5s infinite linear;
          animation: spinAround .5s infinite linear;
  border: 2px solid #dbdbdb;
  border-radius: 290486px;
  border-right-color: transparent;
  border-top-color: transparent;
  content: "";
  display: block;
  height: 1em;
  width: 1em;
  position: absolute;
  left: calc(50% - .5em);
  top: calc(50% - .5em);
}

#app .button.yunoPrimaryCTA, #app .button.yunoSecondaryCTA {
  background: #000;
  color: #FFF;
  display: inline-block;
  border: 1px solid transparent;
  font-weight: 500;
}

#app .button.yunoPrimaryCTA.noCTA, #app .button.yunoSecondaryCTA.noCTA {
  padding-left: 0;
  padding-right: 0;
  background: none;
  border: 0;
  color: #A81E22;
  font-size: 12px;
  font-weight: 500;
  text-decoration: underline;
}

#app .button.yunoPrimaryCTA.noCTA:hover, #app .button.yunoSecondaryCTA.noCTA:hover {
  background: none;
}

#app .button.yunoPrimaryCTA.fluid, #app .button.yunoSecondaryCTA.fluid {
  width: 100%;
  height: 40px;
  line-height: 24px;
}

#app .button.yunoPrimaryCTA.fat, #app .button.yunoSecondaryCTA.fat {
  height: 40px;
  line-height: normal;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .button.yunoPrimaryCTA.withIcon > span, #app .button.yunoSecondaryCTA.withIcon > span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .button.yunoPrimaryCTA.withIcon > span .material-icons-outlined, #app .button.yunoPrimaryCTA.withIcon > span .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .control::before, #app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .button.yunoPrimaryCTA.withIcon > span .control::before, #app .button.yunoPrimaryCTA.withIcon > span .yunoHeader .yunoMainNav > ul > li.dropdown .dropdownToggle:after, #app .yunoHeader .yunoMainNav > ul > li.dropdown .button.yunoPrimaryCTA.withIcon > span .dropdownToggle:after, #app .button.yunoPrimaryCTA.withIcon > span .yunoHeader .yunoMainNav > ul > li .submenuLevel2 li.dropdown:after, #app .yunoHeader .yunoMainNav > ul > li .submenuLevel2 .button.yunoPrimaryCTA.withIcon > span li.dropdown:after, #app .button.yunoPrimaryCTA.withIcon > span .yunoFooter .whatsappSticky a::before, #app .yunoFooter .whatsappSticky .button.yunoPrimaryCTA.withIcon > span a::before, #app .button.yunoPrimaryCTA.withIcon > span .yunoFooter .linkList.withIcon li.zoomTest a:before, #app .yunoFooter .linkList.withIcon li.zoomTest .button.yunoPrimaryCTA.withIcon > span a:before, #app .button.yunoPrimaryCTA.withIcon > span .modal.yunoModal.loginSignupModal .modal-close::after, .modal.yunoModal.loginSignupModal #app .button.yunoPrimaryCTA.withIcon > span .modal-close::after, #app .button.yunoPrimaryCTA.withIcon > span .modal.yunoModal.lightTheme .modal-close::after, .modal.yunoModal.lightTheme #app .button.yunoPrimaryCTA.withIcon > span .modal-close::after, #app .button.yunoPrimaryCTA.withIcon > span .material-icons, #app .button.yunoPrimaryCTA.withIcon > span .yunoFooter .linkList.withIcon li.wifiSpeed a:before, #app .yunoFooter .linkList.withIcon li.wifiSpeed .button.yunoPrimaryCTA.withIcon > span a:before, #app .button.yunoPrimaryCTA.withIcon > span .yunoFooter .linkList.withIcon li.helpDesk a:before, #app .yunoFooter .linkList.withIcon li.helpDesk .button.yunoPrimaryCTA.withIcon > span a:before, #app .button.yunoPrimaryCTA.withIcon > span .yunoFooter .linkList.checkList li:not(.listTitle):before, #app .yunoFooter .linkList.checkList .button.yunoPrimaryCTA.withIcon > span li:not(.listTitle):before, #app .button.yunoSecondaryCTA.withIcon > span .material-icons-outlined, #app .button.yunoSecondaryCTA.withIcon > span .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .control::before, #app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .button.yunoSecondaryCTA.withIcon > span .control::before, #app .button.yunoSecondaryCTA.withIcon > span .yunoHeader .yunoMainNav > ul > li.dropdown .dropdownToggle:after, #app .yunoHeader .yunoMainNav > ul > li.dropdown .button.yunoSecondaryCTA.withIcon > span .dropdownToggle:after, #app .button.yunoSecondaryCTA.withIcon > span .yunoHeader .yunoMainNav > ul > li .submenuLevel2 li.dropdown:after, #app .yunoHeader .yunoMainNav > ul > li .submenuLevel2 .button.yunoSecondaryCTA.withIcon > span li.dropdown:after, #app .button.yunoSecondaryCTA.withIcon > span .yunoFooter .whatsappSticky a::before, #app .yunoFooter .whatsappSticky .button.yunoSecondaryCTA.withIcon > span a::before, #app .button.yunoSecondaryCTA.withIcon > span .yunoFooter .linkList.withIcon li.zoomTest a:before, #app .yunoFooter .linkList.withIcon li.zoomTest .button.yunoSecondaryCTA.withIcon > span a:before, #app .button.yunoSecondaryCTA.withIcon > span .modal.yunoModal.loginSignupModal .modal-close::after, .modal.yunoModal.loginSignupModal #app .button.yunoSecondaryCTA.withIcon > span .modal-close::after, #app .button.yunoSecondaryCTA.withIcon > span .modal.yunoModal.lightTheme .modal-close::after, .modal.yunoModal.lightTheme #app .button.yunoSecondaryCTA.withIcon > span .modal-close::after, #app .button.yunoSecondaryCTA.withIcon > span .material-icons, #app .button.yunoSecondaryCTA.withIcon > span .yunoFooter .linkList.withIcon li.wifiSpeed a:before, #app .yunoFooter .linkList.withIcon li.wifiSpeed .button.yunoSecondaryCTA.withIcon > span a:before, #app .button.yunoSecondaryCTA.withIcon > span .yunoFooter .linkList.withIcon li.helpDesk a:before, #app .yunoFooter .linkList.withIcon li.helpDesk .button.yunoSecondaryCTA.withIcon > span a:before, #app .button.yunoSecondaryCTA.withIcon > span .yunoFooter .linkList.checkList li:not(.listTitle):before, #app .yunoFooter .linkList.checkList .button.yunoSecondaryCTA.withIcon > span li:not(.listTitle):before {
  margin-right: 5px;
}

#app .button.yunoPrimaryCTA.isDisabled, #app .button.yunoSecondaryCTA.isDisabled {
  background-color: rgba(0, 0, 0, 0.5);
  cursor: not-allowed;
}

#app .button.yunoPrimaryCTA.is-loading, #app .button.yunoSecondaryCTA.is-loading {
  background: transparent;
  border-color: #002F5A;
}

#app .button.yunoPrimaryCTA.is-loading:after, #app .button.yunoSecondaryCTA.is-loading:after {
  border-color: #002F5A;
  border-right-color: transparent;
  border-top-color: transparent;
}

#app .button.yunoPrimaryCTA.wired, #app .button.yunoSecondaryCTA.wired {
  background: #FFF;
  color: #201A19;
  border: 1px solid #D0C4C2;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
  font-weight: 500;
}

#app .button.yunoPrimaryCTA.wired:hover, #app .button.yunoSecondaryCTA.wired:hover {
  background: #FFFBFF;
  color: #201A19;
  border-color: #201A19;
}

#app .button.yunoPrimaryCTA.wired.is-loading:after, #app .button.yunoSecondaryCTA.wired.is-loading:after {
  border-color: #201A19;
  border-right-color: transparent;
  border-top-color: transparent;
}

#app .button.yunoPrimaryCTA:hover, #app .button.yunoSecondaryCTA:hover {
  text-decoration: none;
}

#app .button.yunoPrimaryCTA.medium, #app .button.yunoSecondaryCTA.medium {
  padding: 12px 15px;
}

#app .button.noBorder {
  border: 0;
  padding-left: 0;
  padding-right: 0;
}

#app .button.small {
  padding: 8px 16px;
}

#app .button.iconOnly {
  border-radius: 50%;
  padding: 0;
  width: 35px;
  height: 35px;
  text-align: center;
}

#app .button.iconOnly.anchor {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .button.iconOnly .fa, #app .button.iconOnly .yunoHeader .yunoLoginDropdown .dropdown-toggle:after, #app .yunoHeader .yunoLoginDropdown .button.iconOnly .dropdown-toggle:after, #app .button.iconOnly .yunoFooter .linkList li.iconsBlock div a:before, #app .yunoFooter .linkList li.iconsBlock div .button.iconOnly a:before, #app .button.iconOnly .yunoFooter .linkList.withIcon li.android a:before, #app .yunoFooter .linkList.withIcon li.android .button.iconOnly a:before, #app .button.iconOnly .field .datepicker .icon .mdi-chevron-left:after, #app .field .datepicker .icon .button.iconOnly .mdi-chevron-left:after, #app .button.iconOnly .field .datepicker .icon .mdi-chevron-right:after, #app .field .datepicker .icon .button.iconOnly .mdi-chevron-right:after, #app .button.iconOnly .field .autocomplete .control.has-icons-right .icon .mdi-close-circle:after, #app .field .autocomplete .control.has-icons-right .icon .button.iconOnly .mdi-close-circle:after, #app .button.iconOnly .modal-close:after {
  position: relative;
  left: -2px;
}

#app .button.yunoSecondaryCTA {
  background: #A81E22;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}

#app .button.yunoSecondaryCTA.is-loading {
  background: transparent;
  border-color: #a62027;
}

#app .button.yunoSecondaryCTA.is-loading:after {
  border-color: #a62027;
  border-right-color: transparent;
  border-top-color: transparent;
}

#app .button.yunoSecondaryCTA:hover {
  background-color: #410004;
}

#app .button.yunoSecondaryCTA.wired {
  color: #a62027;
  border: 1px solid #a62027;
}

#app .button.yunoSecondaryCTA.wired:hover {
  background: #a62027;
  color: #FFF;
  border-color: #a62027;
}

#app .button.yunoSecondaryCTA.wired.is-loading:after {
  border-color: #a62027;
  border-right-color: transparent;
  border-top-color: transparent;
}

#app .button.yunoSecondaryCTA.wired.is-loading:hover {
  background: transparent;
}

#app .b-radio.radio {
  outline: none;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

#app .b-radio.radio:not(.button) {
  margin-right: .5em;
}

#app .b-radio.radio .control-label {
  padding-left: calc(.75em - 1px);
}

#app .b-radio.radio input[type=radio] {
  position: absolute;
  left: 0;
  opacity: 0;
  outline: none;
  z-index: -1;
}

#app .b-radio.radio input[type=radio] + .check {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  position: relative;
  cursor: pointer;
  width: 1.25em;
  height: 1.25em;
  -webkit-transition: background .15s ease-out;
  transition: background .15s ease-out;
  border-radius: 50%;
  border: 2px solid #a62027;
}

#app .b-radio.radio input[type=radio] + .check:before {
  content: "";
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  left: 50%;
  margin-left: -.625em;
  bottom: 50%;
  margin-bottom: -.625em;
  width: 1.25em;
  height: 1.25em;
  -webkit-transition: -webkit-transform .15s ease-out;
  transition: -webkit-transform .15s ease-out;
  transition: transform .15s ease-out;
  transition: transform .15s ease-out, -webkit-transform .15s ease-out;
  border-radius: 50%;
  -webkit-transform: scale(0);
          transform: scale(0);
  background-color: #a62027;
}

#app .b-radio.radio input[type=radio]:checked + .check {
  border-color: #a62027;
}

#app .b-radio.radio input[type=radio]:checked + .check:before {
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
}

#app .colorGrey .b-radio.radio input[type=radio] + .check {
  border-color: #000;
}

#app .colorGrey .b-radio.radio input[type=radio] + .check:before {
  background-color: #000;
}

#app .colorGrey .b-radio.radio input[type=radio]:checked + .check {
  border-color: #000;
}

#app .radioTiles {
  background: #FFF;
  border: 1px solid #CCCCCC;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
  margin-top: 15px;
}

#app .radioTiles .control {
  border-bottom: 1px solid #CCC;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

#app .radioTiles .control:last-child {
  border-bottom: 0;
}

#app .radioTiles .b-radio {
  font-size: 12px;
  padding: 8px 10px;
  margin: 0;
  cursor: pointer;
  border-radius: 0;
  width: 100%;
}

#app .radioTiles .b-radio.is-primary {
  background: #002F5A;
  color: #FFF;
}

@media (min-width: 768px) {
  #app .radioTiles {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
  }
  #app .radioTiles .control {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    border-right: 1px solid #CCC;
    border-bottom: 0;
  }
  #app .radioTiles .control:last-child {
    border-right: 0;
  }
  #app .radioTiles .b-radio {
    width: auto;
  }
}

#app .checkbox {
  cursor: pointer;
  display: inline-block;
  line-height: 1.25;
  position: relative;
}

#app .b-checkbox.checkbox .control-label {
  padding-left: calc(.75em - 1px);
}

#app .b-checkbox.checkbox.button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .b-checkbox.checkbox[disabled] {
  opacity: .5;
}

#app .b-checkbox.checkbox {
  outline: none;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .b-checkbox.checkbox:not(.button) {
  margin-right: .5em;
}

#app .b-checkbox.checkbox:not(.button) + .checkbox:last-child {
  margin-right: 0;
}

#app .b-checkbox.checkbox input[type=checkbox] {
  position: absolute;
  left: 0;
  opacity: 0;
  outline: none;
  z-index: -1;
}

#app .b-checkbox.checkbox input[type=checkbox] + .check {
  width: 1.25em;
  height: 1.25em;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  border-radius: 4px;
  border: 2px solid #CCC;
  -webkit-transition: background .15s ease-out;
  transition: background .15s ease-out;
  background: #FFF;
}

#app .b-checkbox.checkbox input[type=checkbox]:checked + .check {
  background: #002F5A url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3Cpath d='M.04.627L.146.52.43.804.323.91zm.177.177L.854.167.96.273.323.91z' fill='%23fff'/%3E%3C/svg%3E") no-repeat 50%;
  border-color: #002F5A;
}

#app #yunoMain {
  min-height: 600px;
}

#app #yunoMain.instructorBody {
  background: #f5f5f5;
  padding-bottom: 30px;
}

#app .yunoSpinner {
  display: block;
  position: absolute;
  left: 50%;
  top: 50%;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #002F5A;
  -webkit-animation: spin 2s linear infinite;
  /* Chrome, Opera 15+, Safari 5+ */
  animation: spin 2s linear infinite;
  /* Chrome, Firefox 16+, IE 10+, Opera */
}

#app .yunoSpinner:before {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #a62027;
  -webkit-animation: spin 3s linear infinite;
  /* Chrome, Opera 15+, Safari 5+ */
  animation: spin 3s linear infinite;
  /* Chrome, Firefox 16+, IE 10+, Opera */
}

#app .yunoSpinner:after {
  content: "";
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #f9c922;
  -webkit-animation: spin 1.5s linear infinite;
  /* Chrome, Opera 15+, Safari 5+ */
  animation: spin 1.5s linear infinite;
  /* Chrome, Firefox 16+, IE 10+, Opera */
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    /* Chrome, Opera 15+, Safari 3.1+ */
    /* IE 9 */
    transform: rotate(0deg);
    /* Firefox 16+, IE 10+, Opera */
  }
  100% {
    -webkit-transform: rotate(360deg);
    /* Chrome, Opera 15+, Safari 3.1+ */
    /* IE 9 */
    transform: rotate(360deg);
    /* Firefox 16+, IE 10+, Opera */
  }
}

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    /* Chrome, Opera 15+, Safari 3.1+ */
    /* IE 9 */
    transform: rotate(0deg);
    /* Firefox 16+, IE 10+, Opera */
  }
  100% {
    -webkit-transform: rotate(360deg);
    /* Chrome, Opera 15+, Safari 3.1+ */
    /* IE 9 */
    transform: rotate(360deg);
    /* Firefox 16+, IE 10+, Opera */
  }
}

.fade-out-top, #app .isSticky.hideHeader .notificationBar, #app .yunoHeader.scrollEnabled {
  -webkit-animation: fade-out-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: fade-out-top 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@-webkit-keyframes fade-out-top {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    -webkit-transform: translateY(-50px);
    transform: translateY(-50px);
    opacity: 0;
  }
}

@keyframes fade-out-top {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    -webkit-transform: translateY(-50px);
    transform: translateY(-50px);
    opacity: 0;
  }
}

/* Common ------ END */
/* Yuno Header ----- START */
#app .isSticky {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 10;
}

#app .isSticky.notificationShow {
  -webkit-transform: none;
          transform: none;
}

@media (min-width: 768px) {
  #app .isSticky.notificationShow .yunoSubmenu {
    z-index: 9;
    top: 126px;
  }
}

#app .yunoHeader {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  background: #FFF;
  -webkit-box-shadow: rgba(0, 0, 0, 0.117647) 0 1px 3px;
          box-shadow: rgba(0, 0, 0, 0.117647) 0 1px 3px;
  z-index: 10;
}

#app .yunoHeader.scrollEnabled {
  position: static;
}

#app .yunoHeader.noNav.logoCenter .navbar {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .yunoHeader.noNav .navbar {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#app .yunoHeader.noNav .logo {
  margin-left: 15px;
}

@media (min-width: 768px) {
  #app .yunoHeader.noNav .logo {
    margin-left: 0;
  }
}

#app .yunoHeader > .container {
  padding: 0;
}

#app .yunoHeader .logo {
  margin-left: 54px;
}

#app .yunoHeader .logo img {
  width: 106px;
  height: auto;
}

#app .yunoHeader .navbar {
  padding: 0;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .yunoHeader .navbar .hasSearchBar .searchBarWrapper {
  padding: 10px;
}

@media (min-width: 992px) {
  #app .yunoHeader .navbar .hasSearchBar .searchBarWrapper {
    padding: 0;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
    display: block;
  }
}

#app .yunoHeader .navbar .hasSearchBar .searchBarWrapper form {
  margin: 0;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .searchField {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .control {
  border: 1px solid rgba(0, 0, 0, 0.12);
  padding-right: 34px;
  padding-left: 38px;
  border-radius: 4px;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .control::before {
  content: "\e8b6";
  position: absolute;
  left: 8px;
  top: 8px;
  z-index: 4;
  color: rgba(0, 0, 0, 0.2);
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .control input[type="text"] {
  height: 40px;
  border: 0;
  padding-right: 0;
  padding-left: 0;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .control .icon.is-clickable {
  height: 40px;
  right: 0;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .ctaWrapper {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 0;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 56px;
          flex: 0 0 56px;
  margin: 0;
  display: none;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .doSearch {
  background: rgba(0, 0, 0, 0.02);
  color: rgba(0, 0, 0, 0.38);
  border: 0;
  border-left: 1px solid rgba(0, 0, 0, 0.12);
  padding: 0;
  width: 100%;
  height: 38px;
  z-index: 3;
  position: relative;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content {
  padding: 0;
  max-height: 400px;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content div.dropdown-item:hover {
  background: none;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item {
  padding: 10px 15px;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item .suggestion {
  text-wrap: wrap;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item .suggestion .courseTitle {
  font-weight: 500;
  color: #201A19;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item .suggestion .courseDetail {
  color: #534342;
  font-size: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item .suggestion .courseDetail .caption {
  margin-right: 10px;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item .suggestion .courseDetail .caption:after {
  content: "|";
  padding-left: 10px;
  color: #0000001F;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item .suggestion.courseBlock figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item .suggestion.courseBlock figure .imageWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100px;
          flex: 0 0 100px;
  text-align: center;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item .suggestion.courseBlock figure .imageWrapper img {
  max-height: 56px;
  font-size: 0;
  border-radius: 4px;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item .suggestion.courseBlock figure figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item.is-hovered {
  color: white;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item.is-hovered .suggestion .courseTitle {
  color: white;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item.is-hovered .suggestion .courseDetail {
  color: white;
}

#app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .dropdown-content .dropdown-item.is-hovered .suggestion .courseDetail .caption:after {
  color: white;
}

#app .yunoHeader .navbar-toggler {
  position: absolute;
  left: 0;
  top: 12px;
}

#app .yunoHeader .navbar-toggler:focus {
  outline: none;
}

#app .yunoHeader .navbar-toggler-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .yunoHeader .yunoMainNav {
  border: 1px solid;
  border-color: rgba(0, 47, 90, 0.1);
}

#app .yunoHeader .yunoMainNav > ul > li {
  font-size: 14px;
  margin: 0;
  position: relative;
  width: 100%;
  border-bottom: 1px solid;
  font-weight: 500;
  border-color: rgba(0, 47, 90, 0.1);
}

#app .yunoHeader .yunoMainNav > ul > li > a {
  color: rgba(0, 0, 0, 0.6);
  display: block;
  padding: 10px 16px;
}

#app .yunoHeader .yunoMainNav > ul > li.hasCTA {
  padding: 10px 15px;
}

#app .yunoHeader .yunoMainNav > ul > li.hasCTA .button {
  display: inline-block;
  color: #FFF;
  font-size: 14px;
}

#app .yunoHeader .yunoMainNav > ul > li.hasSearchBar {
  padding-left: 15px;
  padding-right: 15px;
  padding-top: 15px;
  padding-bottom: 15px;
}

@media (min-width: 768px) {
  #app .yunoHeader .yunoMainNav > ul > li.hasSearchBar {
    padding-left: 0;
    padding-right: 0;
  }
}

#app .yunoHeader .yunoMainNav > ul > li.hasSearchBar .searchBarWrapper {
  padding: 0;
}

#app .yunoHeader .yunoMainNav > ul > li.active, #app .yunoHeader .yunoMainNav > ul > li.isCTA {
  border-bottom: 0;
  background-color: rgba(0, 0, 0, 0.02);
}

#app .yunoHeader .yunoMainNav > ul > li.active a, #app .yunoHeader .yunoMainNav > ul > li.isCTA a {
  color: rgba(0, 0, 0, 0.87);
}

#app .yunoHeader .yunoMainNav > ul > li.active:after, #app .yunoHeader .yunoMainNav > ul > li.isCTA:after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.87);
  position: absolute;
  left: 0;
  top: 0;
}

#app .yunoHeader .yunoMainNav > ul > li.isCTA {
  background: #a62027;
  color: #FFF;
  border-radius: 4px;
  margin-left: 15px;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
}

#app .yunoHeader .yunoMainNav > ul > li.isCTA:after {
  display: none;
}

#app .yunoHeader .yunoMainNav > ul > li.isCTA a {
  color: #FFF;
  padding: 10px 30px;
}

#app .yunoHeader .yunoMainNav > ul > li.isCTA:hover {
  background: #7b181d;
}

#app .yunoHeader .yunoMainNav > ul > li.isCTA:hover a {
  color: #FFF;
}

#app .yunoHeader .yunoMainNav > ul > li.dropdown {
  display: block;
}

#app .yunoHeader .yunoMainNav > ul > li.dropdown .dropdownToggle:after {
  border: 0;
  content: "\e5cf";
  vertical-align: 0;
  font-size: 18px;
  position: relative;
  top: 4px;
}

#app .yunoHeader .yunoMainNav > ul > li.dropdown.show .dropdown-toggle:after {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

#app .yunoHeader .yunoMainNav > ul > li .dropdown-menu {
  border-color: #E6E6E6;
  border-radius: 4px;
  padding: 0;
}

#app .yunoHeader .yunoMainNav > ul > li .dropdown-menu.level2 {
  display: block;
}

#app .yunoHeader .yunoMainNav > ul > li .dropdown-menu .level3 {
  top: 0;
  left: auto;
  right: 100%;
  margin: -1px 1px 0 0;
}

#app .yunoHeader .yunoMainNav > ul > li .dropdown-menu .dropdown-item {
  font-size: 14px;
  color: #000;
  padding: 0;
}

#app .yunoHeader .yunoMainNav > ul > li .dropdown-menu .dropdown-item:last-child {
  border-bottom: 0;
}

#app .yunoHeader .yunoMainNav > ul > li .dropdown-menu .dropdown-item:hover a {
  color: rgba(0, 0, 0, 0.87);
}

#app .yunoHeader .yunoMainNav > ul > li .dropdown-menu .dropdown-item:hover .level3 .dropdown-item a {
  color: rgba(0, 0, 0, 0.6);
}

#app .yunoHeader .yunoMainNav > ul > li .dropdown-menu .dropdown-item:hover .level3 .dropdown-item:hover a {
  color: rgba(0, 0, 0, 0.87);
}

#app .yunoHeader .yunoMainNav > ul > li .dropdown-menu .dropdown-item:active, #app .yunoHeader .yunoMainNav > ul > li .dropdown-menu .dropdown-item:focus {
  outline: none;
  background: none;
}

#app .yunoHeader .yunoMainNav > ul > li .submenuLevel2 li.dropdown {
  position: relative;
}

#app .yunoHeader .yunoMainNav > ul > li .submenuLevel2 li.dropdown:after {
  border: 0;
  content: "\e5cc";
  vertical-align: 0;
  font-size: 18px;
  position: absolute;
  right: 12px;
  top: 8px;
}

#app .yunoHeader .yunoMainNav > ul > li .submenuLevel2 li a {
  display: block;
  padding: 5px 30px 5px 16px;
  color: rgba(0, 0, 0, 0.87);
}

#app .yunoHeader .yunoMainNav > ul > li:hover a {
  text-decoration: none;
  color: rgba(0, 0, 0, 0.87);
}

#app .yunoHeader .yunoMainNav > ul > li:last-child {
  border-bottom: 0;
}

#app .yunoHeader .yunoCallUs {
  display: none;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 14px;
}

#app .yunoHeader .yunoCallUs.noSpacer:before {
  display: none;
}

#app .yunoHeader .yunoCallUs a {
  background-color: #A81E22;
  color: white;
  font-weight: 500;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 40px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 15px;
  border-radius: 4px;
}

#app .yunoHeader .yunoCallUs a:hover {
  text-decoration: none;
}

#app .yunoHeader .yunoCallUs.preLogin {
  margin-left: 15px;
}

#app .yunoHeader .yunoCallUs .material-icons, #app .yunoHeader .yunoCallUs .yunoFooter .linkList.withIcon li.wifiSpeed a:before, #app .yunoFooter .linkList.withIcon li.wifiSpeed .yunoHeader .yunoCallUs a:before, #app .yunoHeader .yunoCallUs .yunoFooter .linkList.withIcon li.helpDesk a:before, #app .yunoFooter .linkList.withIcon li.helpDesk .yunoHeader .yunoCallUs a:before, #app .yunoHeader .yunoCallUs .yunoFooter .linkList.checkList li:not(.listTitle):before, #app .yunoFooter .linkList.checkList .yunoHeader .yunoCallUs li:not(.listTitle):before {
  font-size: 24px;
}

#app .yunoHeader .yunoCallUs .caption, #app .yunoHeader .yunoCallUs .value {
  display: none;
}

@media (min-width: 768px) {
  #app .yunoHeader .yunoCallUs {
    position: relative;
    display: none;
  }
  #app .yunoHeader .yunoCallUs .material-icons, #app .yunoHeader .yunoCallUs .yunoFooter .linkList.withIcon li.wifiSpeed a:before, #app .yunoFooter .linkList.withIcon li.wifiSpeed .yunoHeader .yunoCallUs a:before, #app .yunoHeader .yunoCallUs .yunoFooter .linkList.withIcon li.helpDesk a:before, #app .yunoFooter .linkList.withIcon li.helpDesk .yunoHeader .yunoCallUs a:before, #app .yunoHeader .yunoCallUs .yunoFooter .linkList.checkList li:not(.listTitle):before, #app .yunoFooter .linkList.checkList .yunoHeader .yunoCallUs li:not(.listTitle):before {
    font-size: 14px;
    position: relative;
    top: 2px;
  }
  #app .yunoHeader .yunoCallUs .caption, #app .yunoHeader .yunoCallUs .value {
    display: inline-block;
  }
}

@media (min-width: 992px) {
  #app .yunoHeader .yunoCallUs {
    position: relative;
  }
  #app .yunoHeader .yunoCallUs .fa, #app .yunoHeader .yunoCallUs .yunoLoginDropdown .dropdown-toggle:after, #app .yunoHeader .yunoLoginDropdown .yunoCallUs .dropdown-toggle:after, #app .yunoHeader .yunoCallUs .yunoFooter .linkList li.iconsBlock div a:before, #app .yunoFooter .linkList li.iconsBlock div .yunoHeader .yunoCallUs a:before, #app .yunoHeader .yunoCallUs .yunoFooter .linkList.withIcon li.android a:before, #app .yunoFooter .linkList.withIcon li.android .yunoHeader .yunoCallUs a:before, #app .yunoHeader .yunoCallUs .field .datepicker .icon .mdi-chevron-left:after, #app .field .datepicker .icon .yunoHeader .yunoCallUs .mdi-chevron-left:after, #app .yunoHeader .yunoCallUs .field .datepicker .icon .mdi-chevron-right:after, #app .field .datepicker .icon .yunoHeader .yunoCallUs .mdi-chevron-right:after, #app .yunoHeader .yunoCallUs .field .autocomplete .control.has-icons-right .icon .mdi-close-circle:after, #app .field .autocomplete .control.has-icons-right .icon .yunoHeader .yunoCallUs .mdi-close-circle:after, #app .yunoHeader .yunoCallUs .modal-close:after {
    font-size: 14px;
  }
  #app .yunoHeader .yunoCallUs .caption, #app .yunoHeader .yunoCallUs .value {
    display: inline-block;
  }
}

#app .yunoHeader .yunoLogin {
  padding: 0 15px 0 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: absolute;
  top: 13px;
  right: 0;
}

#app .yunoHeader .yunoLogin > a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: auto;
}

#app .yunoHeader .yunoLogin > a.marginRight15 {
  margin-right: 15px;
}

@media (max-width: 320px) {
  #app .yunoHeader .yunoLogin > a.marginRight15 {
    margin-right: 5px;
  }
}

#app .yunoHeader .yunoLogin > a .yuno-login-with-google-on-pages {
  display: block;
  font-weight: 500;
  padding: 5px 10px;
  background-color: transparent;
  border-radius: 4px;
  color: #FFF;
  border: 1px solid transparent;
  -webkit-transition: all 0.25s ease;
  transition: all 0.25s ease;
  font-size: 14px;
  color: #a81e22;
}

#app .yunoHeader .yunoLogin > a .yuno-login-with-google-on-pages.grey.wired {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.6);
  position: relative;
}

#app .yunoHeader .yunoLogin > a .yuno-login-with-google-on-pages.grey.wired::after {
  content: "|";
  position: absolute;
  right: -10px;
  top: 5px;
  color: rgba(0, 0, 0, 0.12);
}

#app .yunoHeader .yunoLogin > a:hover {
  text-decoration: none;
}

#app .yunoHeader .yunoLogin > a:hover .yuno-login-with-google-on-pages {
  background-color: transparent;
}

@media (min-width: 768px) {
  #app .yunoHeader .yunoLogin {
    padding: 15px 0 15px 15px;
    position: static;
  }
}

#app .yunoHeader .yunoLoginDropdown {
  position: absolute;
  right: 15px;
  top: 10px;
}

#app .yunoHeader .yunoLoginDropdown > a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #000;
}

#app .yunoHeader .yunoLoginDropdown > a:hover {
  text-decoration: none;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-toggle:after {
  border: 0;
  content: "\f0d7";
  vertical-align: 0;
  font-size: 18px;
}

#app .yunoHeader .yunoLoginDropdown.show .dropdown-toggle:after {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

#app .yunoHeader .yunoLoginDropdown .userProfile {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .yunoHeader .yunoLoginDropdown .profilePic {
  width: 32px;
  height: 32px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  overflow: hidden;
}

#app .yunoHeader .yunoLoginDropdown .profilePic img {
  width: 32px;
  height: 32px;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu {
  background-color: white;
  border-color: #E6E6E6;
  border-radius: 4px;
  padding: 0;
  left: auto;
  right: 0;
  -webkit-box-shadow: rgba(0, 0, 0, 0.117647) 0 0 10px;
          box-shadow: rgba(0, 0, 0, 0.117647) 0 0 10px;
  width: 300px;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .menuHeader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 15px;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .menuHeader img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  font-size: 0;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .menuHeader figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 80px);
          flex: 0 0 calc(100% - 80px);
  margin-left: 15px;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .menuHeader figcaption .userName {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  line-height: 24px;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .menuHeader figcaption .userEmail {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.6);
  line-height: 20px;
  word-break: break-all;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks {
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 14px;
  font-weight: 500;
  padding: 10px 15px;
  color: rgba(0, 0, 0, 0.87);
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li .material-icons-outlined, #app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li .navbar .hasSearchBar .searchFieldWrapper .autocomplete .control::before, #app .yunoHeader .navbar .hasSearchBar .searchFieldWrapper .autocomplete .yunoLoginDropdown .dropdown-menu .userlinks li .control::before, #app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks .yunoMainNav > ul > li.dropdown .dropdownToggle:after, #app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks .yunoMainNav > ul > li .submenuLevel2 li.dropdown:after, #app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li .yunoFooter .whatsappSticky a::before, #app .yunoFooter .whatsappSticky .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li a::before, #app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks .yunoFooter .linkList.withIcon li.zoomTest a:before, #app .yunoFooter .linkList.withIcon .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li.zoomTest a:before, #app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li .modal.yunoModal.loginSignupModal .modal-close::after, .modal.yunoModal.loginSignupModal #app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li .modal-close::after, #app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li .modal.yunoModal.lightTheme .modal-close::after, .modal.yunoModal.lightTheme #app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li .modal-close::after {
  margin-right: 15px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 24px;
  font-weight: normal;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .userlinks li.linksFooter {
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .groupItem {
  border-bottom: 1px solid #E6E6E6;
}

#app .yunoHeader .yunoLoginDropdown .dropdown-menu .groupItem .dropdown-item {
  border-bottom: 0;
}

#app .yunoHeader .additionalItems {
  padding: 15px;
}

#app .yunoHeader .additionalItems .item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

#app .yunoHeader .additionalItems .item .field {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(50% - 15px);
          flex: 0 0 calc(50% - 15px);
  margin: 0 15px 0 0;
}

#app .yunoHeader .additionalItems .item a {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 5px;
}

#app .yunoHeader .additionalItems .item a .caption {
  font-size: 12px;
}

@media (min-width: 992px) {
  #app .yunoHeader {
    min-height: 72px;
  }
  #app .yunoHeader > .container {
    padding: 0 15px;
  }
  #app .yunoHeader .logo {
    margin: 0;
  }
  #app .yunoHeader .yunoMainNav {
    border: 0;
    margin-left: 15px;
  }
  #app .yunoHeader .yunoMainNav.hasAdmin {
    overflow-x: auto;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 186px);
            flex: 0 0 calc(100% - 186px);
  }
  #app .yunoHeader .yunoMainNav.hasAdmin > ul {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
  #app .yunoHeader .yunoMainNav.hasAdmin > ul > li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 146px;
            flex: 0 0 146px;
    text-align: center;
  }
  #app .yunoHeader .yunoMainNav.hasSearch > ul {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
  #app .yunoHeader .yunoMainNav > ul {
    -webkit-box-pack: end;
        -ms-flex-pack: end;
            justify-content: flex-end;
  }
  #app .yunoHeader .yunoMainNav > ul > li {
    margin: 0;
    width: auto;
    border: 0;
  }
  #app .yunoHeader .yunoMainNav > ul > li.hasSearchBar {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 46%;
            flex: 0 0 46%;
    margin: 0 15px;
  }
  #app .yunoHeader .yunoMainNav > ul > li > a {
    padding: 24px 24px;
  }
  #app .yunoHeader .yunoMainNav > ul > li.active:after {
    top: auto;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
  }
  #app .yunoHeader .yunoMainNav > ul > li.dropdown {
    position: relative;
    top: -1px;
  }
  #app .yunoHeader .yunoMainNav > ul > li .dropdown-menu {
    -webkit-box-shadow: rgba(0, 0, 0, 0.117647) 0 0 10px;
            box-shadow: rgba(0, 0, 0, 0.117647) 0 0 10px;
  }
  #app .yunoHeader .yunoMainNav > ul > li .dropdown-menu .dropdown-item {
    padding: 0;
  }
  #app .yunoHeader .yunoLoginDropdown {
    position: static;
    margin-left: 15px;
  }
}

@media (min-width: 1367px) {
  #app .yunoHeader .yunoMainNav > ul > li.hasSearchBar {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 60%;
            flex: 0 0 60%;
  }
}

#app .yunoSubmenu {
  position: absolute;
  top: 61px;
  left: 0;
  width: 100%;
  z-index: 11;
  padding: 30px 0;
  background-color: #FAFAFA;
  border-bottom: 1px solid #e6e6e6;
}

@media (min-width: 768px) {
  #app .yunoSubmenu {
    z-index: 9;
    top: 72px;
  }
}

#app .yunoSubmenu .closeSubmenu {
  display: block;
  position: absolute;
  left: 19px;
  top: -21px;
}

@media (min-width: 768px) {
  #app .yunoSubmenu .closeSubmenu {
    display: none;
  }
}

#app .yunoSubmenu .submenuList {
  margin: 0 -15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  height: 240px;
  overflow-y: auto;
}

@media (min-width: 768px) {
  #app .yunoSubmenu .submenuList {
    height: auto;
    overflow-y: auto;
  }
}

#app .yunoSubmenu .submenuList li {
  padding: 0 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

@media (min-width: 768px) {
  #app .yunoSubmenu .submenuList li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3%;
            flex: 0 0 33.3%;
  }
}

#app .yunoSubmenu .submenuList li a {
  display: block;
  padding: 15px;
}

#app .yunoSubmenu .submenuList li a:hover {
  text-decoration: none;
  background-color: #FFF;
}

#app .yunoSubmenu .submenuList li a:hover .navLabel {
  color: #A81E22;
}

#app .yunoSubmenu .submenuList li a figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoSubmenu .submenuList li a figure .imgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100px;
          flex: 0 0 100px;
  margin-right: 15px;
}

#app .yunoSubmenu .submenuList li a figure .imgWrapper img {
  width: 100%;
  height: auto;
}

#app .yunoSubmenu .submenuList li a figure .figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% 115px);
          flex: 0 0 calc(100% 115px);
}

#app .yunoSubmenu .submenuList li .navLabel {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.87);
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .yunoSubmenu .submenuList li .navDes {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 16px;
  font-weight: 400;
  margin: 0;
}

@media (min-width: 768px) {
  #app .yunoSubmenu .submenuList.col2 li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
}

/* Yuno Header ------ END */
#site-content {
  height: 600px;
}

/* Yuno Footer ----- START */
#app .yunoFooter {
  background-color: rgba(0, 0, 0, 0.02);
}

#app .yunoFooter .link {
  font-size: 12px;
  line-height: normal;
  text-decoration: underline;
  margin-top: 30px;
  display: inline-block;
}

#app .yunoFooter .whatsappSticky {
  position: fixed;
  z-index: 6;
  bottom: 0;
  right: 0;
  background-color: white;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
  border-radius: 100px;
  margin: 0 15px 15px;
}

#app .yunoFooter .whatsappSticky a {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.87);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 8px 15px;
  line-height: normal;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .yunoFooter .whatsappSticky a::before {
  content: "\ea9c";
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #25D366;
  color: white;
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-right: 5px;
}

#app .yunoFooter .whatsappSticky a:hover {
  text-decoration: none;
  color: #A81E22;
}

#app .yunoFooter .spacer {
  border-top: 1px solid #E6E6E6;
  margin: 16px 0;
  padding: 16px 0;
}

#app .yunoFooter.noNav > .container {
  padding-top: 0;
}

#app .yunoFooter.noNav .footerBottom {
  border-top: 0;
}

#app .yunoFooter.noBG {
  background: none;
  border: 0;
  color: rgba(0, 0, 0, 0.4);
}

#app .yunoFooter.noBG .footerBottom {
  margin: 0;
  padding: 0;
}

#app .yunoFooter .col-12 {
  padding-bottom: 30px;
}

#app .yunoFooter .col-12:last-child {
  padding-bottom: 0;
}

#app .yunoFooter > .container {
  padding-top: 30px;
  padding-bottom: 30px;
}

#app .yunoFooter .linkList li {
  font-size: 14px;
  color: #000;
  margin-bottom: 15px;
}

#app .yunoFooter .linkList li:last-child {
  margin-bottom: 0;
}

#app .yunoFooter .linkList li.listTitle {
  margin-bottom: 20px;
  text-transform: capitalize;
}

#app .yunoFooter .linkList li.listTitle h3 {
  font-size: 16px;
  font-weight: 400;
}

#app .yunoFooter .linkList li.listTitle.noGap {
  margin: 0;
}

#app .yunoFooter .linkList li.android a {
  display: inline-block;
}

#app .yunoFooter .linkList li.android img {
  width: 150px;
  height: auto;
}

#app .yunoFooter .linkList li a {
  color: rgba(0, 0, 0, 0.6);
  font-size: 12px;
}

#app .yunoFooter .linkList li a:hover {
  text-decoration: none;
  color: #002F5A;
}

#app .yunoFooter .linkList li .summary {
  display: block;
  color: rgba(0, 0, 0, 0.5);
}

#app .yunoFooter .linkList li .enableDate {
  margin-top: 5px;
  display: block;
  color: rgba(0, 0, 0, 0.5);
}

#app .yunoFooter .linkList li.iconsBlock {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoFooter .linkList li.iconsBlock div {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 24px;
          flex: 0 0 24px;
  margin-right: 10px;
}

#app .yunoFooter .linkList li.iconsBlock div:last-child {
  margin-right: 0;
}

#app .yunoFooter .linkList li.iconsBlock div a {
  display: block;
  height: 24px;
  text-indent: -99999px;
  background: #FFF;
  border-radius: 50%;
  position: relative;
  background-color: rgba(0, 0, 0, 0.38);
}

#app .yunoFooter .linkList li.iconsBlock div a:before {
  font-size: 14px;
  position: absolute;
  left: 0;
  top: 0;
  text-indent: 0;
  width: 24px;
  height: 24px;
  text-align: center;
  line-height: 24px;
}

#app .yunoFooter .linkList li.iconsBlock div.facebook a::before {
  content: "\f09a";
  color: #FFF;
}

#app .yunoFooter .linkList li.iconsBlock div.facebook a:hover {
  background-color: #016DE5;
}

#app .yunoFooter .linkList li.iconsBlock div.twitter a::before {
  content: "\f099";
  color: #FFF;
}

#app .yunoFooter .linkList li.iconsBlock div.twitter a:hover {
  background-color: #1D9BF0;
}

#app .yunoFooter .linkList li.iconsBlock div.linkedin a::before {
  content: "\f0e1";
  color: #FFF;
}

#app .yunoFooter .linkList li.iconsBlock div.linkedin a:hover {
  background-color: #0277B5;
}

#app .yunoFooter .linkList li.iconsBlock div.instagram a::before {
  content: "\f16d";
  color: #FFF;
}

#app .yunoFooter .linkList li.iconsBlock div.instagram a:hover {
  background: linear-gradient(45deg, #405de6, #5851db, #833ab4, #c13584, #e1306c, #fd1d1d);
}

#app .yunoFooter .linkList li.iconsBlock div.youtube a::before {
  content: "\f16a";
  color: #FFF;
}

#app .yunoFooter .linkList li.iconsBlock div.youtube a:hover {
  background-color: #FF0000;
}

#app .yunoFooter .linkList.withBorder {
  border-bottom: 1px solid #E6E6E6;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

#app .yunoFooter .linkList.withIcon li a {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: rgba(0, 0, 0, 0.87);
  font-size: 14px;
}

#app .yunoFooter .linkList.withIcon li a:before {
  content: "";
  width: 32px;
  height: 32px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  color: rgba(0, 0, 0, 0.6);
}

#app .yunoFooter .linkList.withIcon li.wifiSpeed a:before {
  content: "\e640";
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .yunoFooter .linkList.withIcon li.zoomTest a:before {
  content: "\e04b";
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .yunoFooter .linkList.withIcon li.helpDesk a:before {
  content: "\f0e2";
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .yunoFooter .linkList.withIcon li.android a:before {
  content: "\f17b";
  background-color: #93be23;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: #FFF;
  font-size: 22px;
}

#app .yunoFooter .linkList.checkList {
  margin-bottom: 16px;
}

#app .yunoFooter .linkList.checkList li:not(.listTitle) {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: rgba(0, 0, 0, 0.6);
  font-size: 12px;
}

#app .yunoFooter .linkList.checkList li:not(.listTitle):before {
  content: "\e86c";
  padding-right: 10px;
  font-size: 16px;
  color: #669D4F;
}

#app .yunoFooter .footerBottom {
  border-top: 1px solid #E6E6E6;
  margin-top: 30px;
  padding: 16px 0 0;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

#app .yunoFooter .footerBottom .copy {
  padding-bottom: 16px;
  font-size: 14px;
}

#app .yunoFooter .footerBottom .logo img {
  width: 106px;
  height: auto;
}

@media (min-width: 768px) {
  #app .yunoFooter .footerBottom {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

@media (min-width: 992px) {
  #app .yunoFooter > .container {
    padding-top: 60px;
  }
  #app .yunoFooter .footerBottom {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

/* Yuno Footer ------ END */
/* Yuno Card ----- START */
#app .yunoCardSectionTitle {
  font-size: 32px;
  margin: 0;
  padding: 30px 0 0;
}

#app .yunoCardWide {
  background: #FFF;
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.1);
  margin: 30px 0 0;
  padding: 15px;
  border-radius: 4px;
}

#app .yunoCardWide.withGap {
  margin-left: 15px;
  margin-right: 15px;
}

@media (min-width: 768px) {
  #app .yunoCardWide {
    padding: 30px;
  }
}

#app .yunoCardWide .yunoCardTitle {
  font-size: 24px;
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#app .yunoCardWide .yunoCardTitle a {
  color: #000;
}

#app .yunoCardWide .yunoCardTitle .cardAction {
  font-size: 18px;
  font-weight: normal;
  color: #002F5A;
}

#app .yunoCardWide .yunoCardTitle .cardAction:hover {
  color: #a62027;
}

#app .yunoCardWide .metaList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .yunoCardWide .metaList li {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
}

#app .yunoCardWide .metaList li .linkItem {
  display: inline-block;
  padding-right: 5px;
  text-transform: capitalize;
}

#app .yunoCardWide .metaList li .linkItem:after {
  content: ",";
}

#app .yunoCardWide .metaList li .linkItem:last-child:after {
  display: none;
}

@media (min-width: 768px) {
  #app .yunoCardWide .metaList li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-right: 10px;
  }
  #app .yunoCardWide .metaList li:after {
    content: "|";
    padding-left: 10px;
  }
  #app .yunoCardWide .metaList li:last-child:after {
    display: none;
  }
}

#app .yunoCardWide .yunoCardBody.withFlexbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-top: 15px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .yunoCardWide .yunoCardBody.withFlexbox .imgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  margin-bottom: 15px;
}

#app .yunoCardWide .yunoCardBody.withFlexbox .imgWrapper img {
  width: 100%;
  height: auto;
}

#app .yunoCardWide .yunoCardBody.withFlexbox .descriptionWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

@media (min-width: 768px) {
  #app .yunoCardWide .yunoCardBody.withFlexbox .imgWrapper {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 30%;
            flex: 0 0 30%;
    margin-bottom: 0;
  }
  #app .yunoCardWide .yunoCardBody.withFlexbox .descriptionWrapper {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 70%;
            flex: 0 0 70%;
    padding-left: 15px;
  }
  #app .yunoCardWide .yunoCardBody.withFlexbox .descriptionWrapper.noMedia {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
            flex: 0 0 100%;
  }
}

#app .yunoCardWide .yunoCardBody .description {
  font-size: 16px;
  margin: 0;
}

#app .yunoCardWide .yunoCardBody p:last-child {
  margin-bottom: 0;
}

#app .yunoCardWide .yunoCardFooter {
  padding-top: 30px;
}

/* Yuno Card ------ END */
#app .groupElement {
  margin-bottom: 15px;
}

#app .groupElement .field {
  margin-bottom: 5px;
}

#app .groupElement .error {
  margin: 0;
  padding-top: 0;
  font-size: 14px;
  color: red;
}

#app .groupElement .fieldLabel {
  margin: 0;
  padding: 0;
  font-size: 16px;
}

#app .groupElement .helper {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.5);
  display: block;
  margin-bottom: 15px;
}

#app .groupElement.checkList .b-checkbox.invalid input[type=checkbox] + .check {
  border-color: red;
}

#app .groupElement.checkList .control-label {
  font-size: 14px;
}

#app .field {
  margin-bottom: 15px;
}

#app .field.fieldLoading {
  min-height: 60px;
}

#app .field:last-child {
  margin-bottom: 0;
}

#app .field .error {
  margin: 0;
  padding-top: 10px;
  font-size: 14px;
  color: red;
}

#app .field .label {
  font-size: 14px;
  margin: 0 0 10px;
  color: rgba(0, 0, 0, 0.6);
}

#app .field .control input[type="text"] {
  width: 100%;
  border: 1px solid #CCCCCC;
  padding: 0 15px;
  height: 36px;
  border-radius: 4px;
  font-size: 14px;
  color: #000;
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .field .control input:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  border-color: #cccccc;
}

#app .field .control textarea {
  width: 100%;
  border: 1px solid #CCCCCC;
  padding: 15px;
  height: 100px;
  border-radius: 4px;
  font-size: 14px;
  color: #000;
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .field .control textarea:focus {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .field .control.height250 textarea {
  height: 250px;
}

#app .field .control .counter {
  display: block;
  text-align: right;
  color: rgba(0, 0, 0, 0.5);
}

#app .field .control.invalid input[type="text"] {
  border: 1px solid red;
}

#app .field .control.invalid .select select {
  border-color: red;
}

#app .field .control.invalid .textarea {
  border-color: red;
}

#app .field .dropdown-content {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
  background: none;
}

#app .field .dropdown-content .dropdown-item {
  color: rgba(0, 0, 0, 0.5);
  padding: 10px 15px;
  border-bottom: 1px solid #FFF;
}

#app .field .dropdown-content .dropdown-item.disabled {
  background-color: rgba(0, 0, 0, 0.2);
  opacity: 0.5;
  cursor: not-allowed;
  border-bottom: 1px solid #FFF;
}

#app .field .dropdown-content .dropdown-item.is-hovered {
  background-color: rgba(0, 0, 0, 0.8);
  color: #FFF;
}

#app .field .dropdown-content .dropdown-item .userItem {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
}

#app .field .dropdown-content .dropdown-item .userItem .userImg {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 24px;
          flex: 0 0 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.2);
  margin-right: 10px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 18px;
}

#app .field .dropdown-content .dropdown-item .userItem .userImg img {
  width: 22px;
  height: 22px;
}

#app .field .dropdown-content .dropdown-item .userItem .userName {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 55px);
          flex: 0 0 calc(100% - 55px);
  text-overflow: ellipsis;
  overflow: hidden;
}

#app .field .dropdown-content .dropdown-item:active, #app .field .dropdown-content .dropdown-item:focus {
  border: 0;
  background: none;
}

#app .field.is-primary .file-cta {
  background: #FFF;
  border: 1px solid #002F5A;
  color: #000;
}

#app .field .upload .icon {
  margin-right: 10px;
}

#app .field .upload .icon .mdi-upload:before {
  content: "upload";
}

#app .field .select:not(.is-multiple):not(.is-loading):after {
  border-color: #002F5A;
}

#app .field .taginput .taginput-container {
  border-color: #CCCCCC;
  padding: 0;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .field .taginput .taginput-container.is-focused {
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .field .taginput .taginput-container.is-focusable {
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .field .taginput .taginput-container:focus, #app .field .taginput .taginput-container:active {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
}

#app .field .taginput .taginput-container .autocomplete {
  width: 100%;
}

#app .field .taginput .taginput-container .tag {
  margin: 10px 15px 0;
  overflow: hidden;
  -ms-flex-item-align: start;
      align-self: flex-start;
  max-width: calc(100% - 30px);
}

#app .field .taginput .taginput-container .tag > span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#app .field .taginput .taginput-container input {
  border: 0;
  margin: 0;
  padding: 0 15px;
}

#app .field .taginput.inlineTags .taginput-container {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

#app .field .taginput.inlineTags .taginput-container .tag {
  margin-bottom: 10px;
  margin-right: 0;
}

#app .field .select select {
  border-color: #CCC;
}

#app .field .select select:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

#app .field .datepicker .dropdown-item:focus, #app .field .datepicker .dropdown-item:active {
  background: none;
}

#app .field .datepicker .icon {
  color: #002F5A !important;
}

#app .field .datepicker .icon .mdi-chevron-left:after, #app .field .datepicker .icon .mdi-chevron-right:after {
  content: "\f053";
}

#app .field .datepicker .icon .mdi-chevron-right:after {
  content: "\f054";
}

#app .field .datepicker .datepicker-cell.is-selectable {
  color: #000;
}

#app .field .datepicker .datepicker-cell.is-selectable.is-selected {
  background-color: #002F5A;
  color: #FFF;
}

#app .field .datepicker .datepicker-cell.is-today {
  border-color: #002F5A;
}

#app .field .timepicker .dropdown-item:active {
  background: none;
}

#app .field .autocomplete .control.has-icons-right input {
  padding-right: 36px;
}

#app .field .autocomplete .control.has-icons-right .icon .mdi-close-circle:after {
  content: "\f00d";
}

.modal {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: none;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  overflow: hidden;
  position: fixed;
  z-index: 40;
}

.modal.yunoModal .yunoLoader {
  height: 200px;
  position: relative;
}

.modal.yunoModal .modal-close {
  position: absolute;
  right: 10px;
  top: 10px;
  background-color: #000;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.modal.yunoModal .modal-close:after, .modal.yunoModal .modal-close:before {
  background-color: transparent;
}

.modal.yunoModal .modal-close:before {
  display: none;
}

.modal.yunoModal .modal-close:after {
  content: "\f00d";
  color: #FFF;
  font-size: 18px;
  position: static;
  width: auto;
  height: auto;
  -webkit-transform: none;
          transform: none;
}

.modal.yunoModal .formWrapper, .modal.yunoModal .wrapper {
  padding: 24px;
  border-radius: 4px;
  background: #FCFCFC;
  border: 1px solid #eee;
}

.modal.yunoModal .formWrapper.noBG, .modal.yunoModal .wrapper.noBG {
  background: none;
  padding: 0;
  border-radius: 0;
  border: 0;
}

.modal.yunoModal .wrapper p:last-child {
  margin-bottom: 0;
}

.modal.yunoModal .modalHeader .modalTitle {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 30px;
}

.modal.yunoModal .modalFooter {
  margin-top: 30px;
}

.modal.yunoModal .modalFooter .button {
  min-width: 120px;
}

.modal.yunoModal.inviteModal .loaderWrapper {
  height: 50px;
}

.modal.yunoModal.inviteModal .loaderWrapper .smallLoader {
  margin-top: 15px !important;
  margin-bottom: 15px !important;
}

.modal.yunoModal.inviteModal .classFields > li {
  font-size: 16px;
  line-height: normal;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 15px;
}

.modal.yunoModal.inviteModal .classFields > li .listSubtitle {
  margin-bottom: 10px;
  font-size: 14px;
}

.modal.yunoModal.inviteModal .classFields > li .caption {
  font-weight: 500;
  color: #000;
  display: block;
  margin-bottom: 5px;
}

.modal.yunoModal.inviteModal .classFields > li:last-child {
  margin-bottom: 0;
}

.modal.yunoModal.inviteModal .classFields > li .selectedLearners {
  margin-top: 10px;
  max-height: 245px;
  overflow-y: auto;
}

.modal.yunoModal.inviteModal .classFields > li .selectedLearners li {
  padding: 5px 10px;
  font-weight: 400;
}

.modal.yunoModal.inviteModal .classFields > li .selectedLearners li .caption {
  font-weight: 400;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
}

.modal.yunoModal.inviteModal .classFields > li .clipboard {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.modal.yunoModal.inviteModal .classFields > li .clipboard .control {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 85%;
          flex: 0 0 85%;
}

.modal.yunoModal.inviteModal .classFields > li .clipboard .control input {
  width: 100%;
  background-color: #fff;
  border-color: #dbdbdb;
  border-radius: 4px;
  color: #363636;
  border: 1px solid #dbdbdb;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 1rem;
  height: 2.25em;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  line-height: 1.5;
  padding-bottom: calc(.375em - 1px);
  padding-left: calc(.625em - 1px);
  padding-right: calc(.625em - 1px);
  padding-top: calc(.375em - 1px);
  position: relative;
  vertical-align: top;
}

.modal.yunoModal.inviteModal .classFields > li .clipboard .trigger {
  margin-left: 15px;
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.modal.yunoModal.loginSignupModal .modal-content, .modal.yunoModal.lightTheme .modal-content {
  padding: 0;
  border-radius: 4px;
}

.modal.yunoModal.loginSignupModal .modalHeader .modalTitle, .modal.yunoModal.lightTheme .modalHeader .modalTitle {
  color: rgba(0, 0, 0, 0.87);
  font-size: 20px;
  padding: 10px 40px 10px 20px;
  line-height: 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 0;
}

.modal.yunoModal.loginSignupModal .modalBody, .modal.yunoModal.lightTheme .modalBody {
  padding: 20px;
}

.modal.yunoModal.loginSignupModal .modalBody .modalCaption, .modal.yunoModal.lightTheme .modalBody .modalCaption {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.87);
  text-align: left;
  padding-bottom: 15px;
  font-weight: 500;
}

.modal.yunoModal.loginSignupModal .modal-close, .modal.yunoModal.lightTheme .modal-close {
  background-color: #FFF;
  color: rgba(0, 0, 0, 0.6);
  right: 20px;
  top: 7px;
}

.modal.yunoModal.loginSignupModal .modal-close::after, .modal.yunoModal.lightTheme .modal-close::after {
  content: "\e5cd";
  color: rgba(0, 0, 0, 0.6);
}

.modal.yunoModal.loginSignupModal .observer, .modal.yunoModal.lightTheme .observer {
  padding: 0 15px;
}

.modal.yunoModal.loginSignupModal .observer .field .label, .modal.yunoModal.lightTheme .observer .field .label {
  font-size: 14px !important;
  color: rgba(0, 0, 0, 0.6);
}

.modal.yunoModal.loginSignupModal .observer .field .error, .modal.yunoModal.lightTheme .observer .field .error {
  display: none;
}

.modal.yunoModal.loginSignupModal .observer .field .control input[type="text"], .modal.yunoModal.lightTheme .observer .field .control input[type="text"] {
  height: 40px !important;
}

.modal.yunoModal.loginSignupModal .observer .field .control.invalid + .error, .modal.yunoModal.lightTheme .observer .field .control.invalid + .error {
  display: block;
}

.modal.yunoModal.loginSignupModal .ctaWrapper, .modal.yunoModal.lightTheme .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.modal.yunoModal.loginSignupModal .ctaWrapper.alignLeft, .modal.yunoModal.lightTheme .ctaWrapper.alignLeft {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.modal.yunoModal.loginSignupModal .googleLogin, .modal.yunoModal.lightTheme .googleLogin {
  border: 1px solid #1976D2;
  border-radius: 4px;
  width: 100%;
  padding: 9px 15px 8px;
  background-color: white;
  font-size: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: #1976D2;
}

.modal.yunoModal.loginSignupModal .googleLogin.width70, .modal.yunoModal.lightTheme .googleLogin.width70 {
  width: 80%;
}

.modal.yunoModal.loginSignupModal .googleLogin img, .modal.yunoModal.lightTheme .googleLogin img {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.modal.yunoModal.loginSignupModal .footerCaption, .modal.yunoModal.lightTheme .footerCaption {
  color: rgba(0, 0, 0, 0.6);
  text-align: center;
  font-size: 12px;
  margin: 20px 0 0;
}

.modal.yunoModal.loginSignupModal .helperCaption, .modal.yunoModal.lightTheme .helperCaption {
  margin: 15px 0 0;
  font-size: 12px;
  text-align: center;
  color: rgba(0, 0, 0, 0.6);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding-bottom: 15px;
  padding-top: 15px;
}

#app .modal.yunoModal.lightTheme .categoryWithImage {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px 0 0;
}

#app .modal.yunoModal.lightTheme .categoryWithImage .fieldWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  padding: 0 15px 15px 0;
}

#app .modal.yunoModal.lightTheme .categoryWithImage .field {
  background-size: cover;
}

#app .modal.yunoModal.lightTheme .categoryWithImage .inner {
  border-radius: 4px;
  border: 1px solid #E6E6E6;
  background: #FFF;
  -webkit-box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.1);
  padding: 8px;
}

#app .modal.yunoModal.lightTheme .categoryWithImage .inner:hover {
  border-color: #A81E22;
}

#app .modal.yunoModal.lightTheme .categoryWithImage .radio {
  width: 100%;
  height: 84px;
  margin: 0;
  font-size: 0;
  background: transparent;
  border: 0;
}

#app .modal.yunoModal.lightTheme .categoryWithImage .catLabel {
  text-align: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.87);
  font-weight: 500;
}

.modal.is-active {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.modal-background {
  background-color: rgba(10, 10, 10, 0.86);
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.modal-card,
.modal-content {
  margin: 0 20px;
  max-height: calc(100vh - 160px);
  overflow: auto;
  position: relative;
  width: 100%;
  color: #000;
  padding: 30px;
  border-radius: 10px;
}

@media print, screen and (min-width: 769px) {
  .modal-card,
  .modal-content {
    margin: 0 auto;
    max-height: calc(100vh - 40px);
    width: 640px;
  }
}

.modal-close {
  background: none;
  height: 40px;
  position: fixed;
  right: 20px;
  top: 20px;
  width: 40px;
  border: 0;
}

.modal-close:focus {
  outline: none;
}

.modal-close:after {
  content: "\f00d";
  color: #FFF;
  font-size: 18px;
}

.modal-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  max-height: calc(100vh - 40px);
  overflow: hidden;
  overflow-y: visible;
  -ms-overflow-y: visible;
}

.modal-card-foot,
.modal-card-head {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: #FFF;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  padding: 30px;
  position: relative;
}

.modal-card-head {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.modal-card-title {
  color: #000;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  font-size: 20px;
  line-height: 1;
  margin: 0;
  font-weight: 500;
}

.modal-card-foot {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.modal-card-foot .button {
  border-radius: 3px;
  padding: 8px 16px;
}

.modal-card-foot .button .fa, .modal-card-foot .button #app .yunoHeader .yunoLoginDropdown .dropdown-toggle:after, #app .yunoHeader .yunoLoginDropdown .modal-card-foot .button .dropdown-toggle:after, .modal-card-foot .button #app .yunoFooter .linkList li.iconsBlock div a:before, #app .yunoFooter .linkList li.iconsBlock div .modal-card-foot .button a:before, .modal-card-foot .button #app .yunoFooter .linkList.withIcon li.android a:before, #app .yunoFooter .linkList.withIcon li.android .modal-card-foot .button a:before, .modal-card-foot .button #app .field .datepicker .icon .mdi-chevron-left:after, #app .field .datepicker .icon .modal-card-foot .button .mdi-chevron-left:after, .modal-card-foot .button #app .field .datepicker .icon .mdi-chevron-right:after, #app .field .datepicker .icon .modal-card-foot .button .mdi-chevron-right:after, .modal-card-foot .button #app .field .autocomplete .control.has-icons-right .icon .mdi-close-circle:after, #app .field .autocomplete .control.has-icons-right .icon .modal-card-foot .button .mdi-close-circle:after, .modal-card-foot .button .modal-close:after {
  padding-left: 5px;
}

.modal-card-foot .button:focus {
  outline: none;
}

.modal-card-foot .button.is-primary {
  background: #002F5A;
  color: #FFF;
  display: inline-block;
  border: 1px solid transparent;
}

.modal-card-foot .button:not(:last-child) {
  margin-right: .5em;
}

.modal-card-body {
  -webkit-overflow-scrolling: touch;
  background-color: #FFF;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  -ms-flex-negative: 1;
      flex-shrink: 1;
  overflow: auto;
  padding: 0 30px;
}

.modal-card-body .media {
  background: #FCFCFC;
  border-radius: 4px;
  padding: 24px;
  border: 1px solid #EEE;
}

.modal-card-body p {
  margin: 0;
}

html .dialog .modal-card {
  max-width: 460px;
  width: auto;
}

html .dialog .modal-card .modal-card-head {
  font-size: 1.25rem;
  font-weight: 600;
  background: #FFF;
  border: 0;
}

html .dialog .modal-card .modal-card-body .field {
  margin-top: 16px;
}

html .dialog .modal-card .modal-card-body.is-titleless {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

html .dialog .modal-card .modal-card-foot {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  background: #FFF;
  border: 0;
}

html .dialog .modal-card .modal-card-foot .button {
  display: inline;
  min-width: 5em;
  background: #FFF;
  color: #002F5A;
  border: 1px solid #002F5A;
  height: auto;
}

html .dialog .modal-card .modal-card-foot .button.is-danger {
  border: 1px solid #a62027;
  background: #a62027;
  color: #FFF;
}

html .dialog .modal-card .modal-card-foot .button.is-primary {
  border: 1px solid #002F5A;
  background: #002F5A;
  color: #FFF;
}
/*# sourceMappingURL=common.css.map */