Vue.component('yuno-checked-items-card', {
    props: ["data", "options"],
    template: `
        <section class="checkedItemsCards">
            <div class="container">
                <h2 class="largestTitle">{{ data.title }}</h2>
                <b-tabs class="yunoTabsV2" v-model="data.data.activeTab" @input="drawerTabChange" :animated="true">
                    <b-tab-item 
                        v-for="(tab, i) in tabs"
                        :visible="tab.isActive"
                        :key="i"
                        :label="tab.label">
                        <div class="grid" :class="[tab.data.length < 3 ? 'alignCenter' : '']">
                            <article 
                                v-for="(card, j) in tab.data"
                                :key="j"
                                class="item"
                            >
                                <div class="wrapper">
                                    <h3 class="largerTitle">{{ card.title }}
                                        <small v-if="card.subtitle !== ''">
                                            {{ card.subtitle }}
                                        </small>
                                    </h3>
                                    <h4 class="caption1" v-if="card.duration">{{ card.duration }}</h4>
                                    <ul class="checkedList">
                                        <li
                                            v-for="(item, h) in card.items"
                                            :key="h"
                                        >
                                            <h4>
                                                {{ item.label }}
                                                <small v-if="item.caption !== ''">
                                                    {{ item.caption }}    
                                                </small>
                                            </h4>
                                        </li>
                                    </ul>
                                    <p class="price" v-if="card.price">
                                        {{ card.price.amount }} <span>{{ card.price.tax }}</span>
                                    </p>
                                </div>
                            </article>
                        </div>
                    </b-tab-item>
                </b-tabs>
                <p class="note" v-if="data.note !== undefined">
                    <a :href="data.note.url" target="_blank">{{ data.note.label }} <span class="material-icons-outlined">open_in_new</span></a>
                </p>
            </div>
        </section>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        tabs() {
            return this.$props.data.data.tabs
        }
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        drawerTabChange(index) {
            
        },
    }
});