#app .modal.yunoModal.lightTheme.chooseAccountType .fontColorDark {
  color: #201A19;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .fontColorDarkVariant {
  color: #534342;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .fontColorDarkVariant2 {
  color: rgba(0, 0, 0, 0.38);
}

#app .modal.yunoModal.lightTheme.chooseAccountType .h1 {
  font-size: 28px;
  line-height: 40px;
  font-weight: 400;
  margin-bottom: 0;
  text-align: center;
}

@media (min-width: 768px) {
  #app .modal.yunoModal.lightTheme.chooseAccountType .h1 {
    font-size: 40px;
    line-height: 52px;
    font-weight: 400;
    margin-bottom: 0;
  }
}

#app .modal.yunoModal.lightTheme.chooseAccountType .h2 {
  font-size: 20px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .body1, #app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType ul li {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .caption1 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
  text-align: center;
}

@media (min-width: 768px) {
  #app .modal.yunoModal.lightTheme.chooseAccountType .modalBody {
    padding-top: 40px;
    padding-bottom: 40px;
  }
}

#app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 30px 0 0;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

@media (min-width: 768px) {
  #app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes {
    margin: 60px 0 0;
    gap: 14%;
  }
}

#app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (min-width: 768px) {
  #app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 40%;
            flex: 0 0 40%;
  }
}

#app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType h3 {
  margin-bottom: 24px;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 15px;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType ul li .material-icons {
  margin-right: 10px;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType ul li .material-icons.isGreen {
  color: #02CF3B;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType ul li .material-icons.isRed {
  color: #BA1A1A;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType:first-child {
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType:first-child {
    position: relative;
    margin-bottom: 0;
  }
  #app .modal.yunoModal.lightTheme.chooseAccountType .accountTypes .accountType:first-child::after {
    content: "";
    position: absolute;
    top: 0;
    right: -17%;
    background-color: rgba(0, 0, 0, 0.08);
    width: 1px;
    height: 100%;
  }
}

#app .modal.yunoModal.lightTheme.chooseAccountType .ctaWrapper {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  margin-top: 15px;
}

#app .modal.yunoModal.lightTheme.chooseAccountType .ctaWrapper .button {
  width: 100%;
}
/*# sourceMappingURL=chooseAccoutType.css.map */