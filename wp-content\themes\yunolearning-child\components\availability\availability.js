const YUNOAvailability = (function($) {
    const availability = function() {
        
        Vue.component('yuno-availability', {
            props: ["data", "options"],
            template: `
                <div class="availabilityWrapper">
                    <template v-if="isFormLoading">
                        <div class="smallLoader"></div>
                    </template>
                    <template v-if="isFormReady">
                        <validation-observer tag="div" ref="availabilityObserver" v-slot="{ handleSubmit }">
                            <form id="formNewAddress" @submit.prevent="handleSubmit(availabilityInit)">
                                <ul class="hoursWrapper">
                                    <li class="item" v-for="(item, index) in data.data.weeks" :key="index">
                                        <div class="itemWrapper">
                                            <div class="day">{{item.day}}s</div>
                                            <div class="slots">
                                                <b-field>
                                                    <b-switch v-model="item.isDayOff"
                                                        :true-value="false"
                                                        @input="onOpen($event ? false : true, item)"
                                                        :false-value="true">
                                                        <template v-if="item.isDayOff">
                                                            Unavailable
                                                        </template>
                                                        <template v-else>
                                                            Available
                                                        </template>
                                                    </b-switch>
                                                </b-field>
                                            </div>
                                        </div>
                                        <div class="hours" v-if="!item.isDayOff"> 
                                            <template v-for="(slot, slotIndex) in item.availablity">
                                                <div class="hourWrapper" :key="slotIndex" v-if="slot.isActive">
                                                    <validation-provider 
                                                        :customMessages="{ required: message.required }" 
                                                        tag="div"
                                                        class="chooseHour" 
                                                        :rules="{required:true, isOverlapping: slot.isOverlapping}" 
                                                        v-slot="{ errors, classes }"
                                                    >
                                                        <div :class="{ hasError: errors && errors.length > 0 }">
                                                            <b-dropdown
                                                                :class="classes"
                                                                :key="'start-' + slotIndex"
                                                                v-model="slot.startsAt"
                                                                aria-role="list"
                                                                class="filterMenu"
                                                            >
                                                                <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
                                                                    <span><template v-if="!item.is24Hours">Start</template> {{slot.startsAt}}</span>
                                                                    <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                                                </button>
                                                                <template v-for="(time, timeIndex) in timeSlots.data">
                                                                    <b-dropdown-item 
                                                                        @click="onFilterItemSelect(slot, time, 'start', item)"
                                                                        :value="time.label"
                                                                        :key="timeIndex"
                                                                        aria-role="listitem">
                                                                        <span>{{time.label}}</span>
                                                                    </b-dropdown-item>
                                                                </template>
                                                            </b-dropdown>
                                                            <p class="error" v-if="false">{{errors[0]}}</p>
                                                        </div>
                                                    </validation-provider>
                                                    <validation-provider 
                                                        :customMessages="{ required: message.required, is_not: message.isNot,  greaterThen: message.greaterThen}" 
                                                        tag="div" 
                                                        v-if="slot.isEnds"
                                                        class="chooseHour"
                                                        :rules="{required:true, is_not: slot.startsAt, isOverlapping: slot.isOverlapping, isEndTime: slot.isEndTime}" 
                                                        v-slot="{ errors, classes }">
                                                        <div :class="{ hasError: errors && errors.length > 0 }">
                                                            <b-dropdown
                                                                :class="classes"
                                                                v-if="slot.isEnds"
                                                                :key="'end-' + slotIndex"
                                                                v-model="slot.endsAt"
                                                                aria-role="list"
                                                                class="filterMenu"
                                                            >
                                                                <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
                                                                    <span>End {{slot.endsAt}}</span>
                                                                    <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                                                </button>
                                                                <template v-for="(time, timeIndex) in timeSlots.data">
                                                                    <b-dropdown-item 
                                                                        @click="onFilterItemSelect(slot, time, 'end', item)"
                                                                        :value="time.label"
                                                                        :key="timeIndex"
                                                                        aria-role="listitem">
                                                                        <span>{{time.label}}</span>
                                                                    </b-dropdown-item>
                                                                </template>
                                                            </b-dropdown>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </div>
                                                    </validation-provider>
                                                    <b-button 
                                                        @click="removeSlot(item, slot)" 
                                                        class="yunoPrimaryCTA iconOnly removeSlot noBG">
                                                        <span class="material-icons-outlined">close</span>
                                                    </b-button>
                                                    <b-tooltip label="Copy time to all"
                                                        type="is-dark"
                                                        position="is-top">
                                                        <b-button 
                                                            @click="copySlot(item, slot)" 
                                                            v-if="slotIndex === 0"
                                                            class="yunoPrimaryCTA iconOnly copySlot noBG">
                                                            <span class="material-icons-outlined">content_copy</span>
                                                        </b-button>    
                                                    </b-tooltip>
                                                </div>
                                                <template v-if="slot.isEnds && slotIndex === item.availablity.length - 1">
                                                    <div class="addSlotWrapper">
                                                        <b-button 
                                                            v-if="slotIndex === item.availablity.length - 1"
                                                            @click="addSlot(item)" 
                                                            class="yunoPrimaryCTA addSlot noBG">
                                                            Add Hours
                                                        </b-button>
                                                    </div>
                                                </template>
                                            </template>
                                        </div>
                                    </li>
                                </ul>
                                <div class="ctaWrapper">
                                    <b-button
                                        native-type="submit"
                                        :loading="isLoading ? true : false"
                                        :disabled="isLoading ? true : false"
                                        class="yunoSecondaryCTA">
                                        Save
                                    </b-button>    
                                    <b-button
                                        v-if="typeof options.isPreview === 'undefined' || options.isPreview"
                                        @click="initPreview()"
                                        :disabled="data.data.hasRecord && preview.isMatched ? false : true" 
                                        class="yunoSecondaryCTA wired">
                                        Preview
                                    </b-button>    
                                </div>
                            </form>
                        </validation-observer>
                        <yuno-availability-preview v-if="preview.modal" :data="data.data" :options="preview"></yuno-availability-preview>
                    </template>
                </div>
            `,
            data() {
                return {
                    isLoading: false,
                    message: {
                        required: "Required",
                        isNot: "Value should not be same as start time",
                        error: ""
                    },
                    slot: {
                        id: "0",
                        startsAt: "",
                        endsAt: "",
                        start: "",
                        end: "",
                        isActive: true,
                        slotID: "",
                        isEnds: true,
                        isExist: false,
                        isOverlapping: false,
                        isEndTime: false
                    },
                    weeks: [],
                    preview: {
                        modal: false,
                        title: "Availability",
                        oldData: "",
                        isMatched: true
                    }
                }
            },
            computed: {
                ...Vuex.mapState([
                    'user',
                    'userInfo',
                    'userProfile',
                    'userRole',
                    'timeSlots',
                    'instructorAvailability',
                    'instructorAvailabilityGrid',
                    'settings',
                    'resources'
                ]),
                isFormLoading: {
                    get() {
                        const module =  this.$props.data.loading 
                                        || this.timeSlots.loading 
                        return module
                    }
                },
                isFormReady: {
                    get() {
                        let module =  this.$props.data.success 
                                        && this.timeSlots.success;

                        if (module) {
                            this.initFormReady()
                        }
                        
                        return module
                    }
                },
            },
            async created() {
                
            },
            mounted() {
                
            },
            methods: {
                timeToMinutes(timeStr) {
                    let [hours, minutes] = timeStr.split(':').map(Number)
                    if (hours === 24) hours = 0
                    return hours * 60 + minutes
                },
                manageEndTime(slot) {
                    const startMinutes = this.timeToMinutes(slot.start);
                    const endMinutes = this.timeToMinutes(slot.end);
                    // If end time is not later than start time, mark as invalid (set isEndTime true)
                    if (endMinutes <= startMinutes) {
                        slot.isEndTime = true;
                    } else {
                        slot.isEndTime = false;
                    };
                },
                manageOverlappingSlot(slot, availablity) {
                    // Convert current slot times
                    const currentStart = this.timeToMinutes(slot.start);
                    const currentEnd = this.timeToMinutes(slot.end);
                    // Reset overlapping flag
                    slot.isOverlapping = false;
                    // Loop over all existing slots in availablity array
                    availablity.availablity.forEach(existingSlot => {
                        // Skip self-check if IDs are the same
                        if(existingSlot.slotID === slot.slotID) return;
                        const existStart = this.timeToMinutes(existingSlot.start);
                        const existEnd = this.timeToMinutes(existingSlot.end);
                        // Basic overlapping: [A,B] overlaps if A < D && C < B
                        if(currentStart < existEnd && existStart < currentEnd) {
                            slot.isOverlapping = true;
                        }
                    });
                },
                copySlot(item, slot) {
                    const isSignup = this.$props.options.isSignup;
                    let tab = "";

                    if (isSignup) {
                        tab = this.instructorAvailabilityGrid.data;
                    } else {
                        tab = this.resources.data;
                    }

                    

                    const weeks = tab.weeks;

                    weeks.forEach(day => {
                        if (!day.isDayOff) {
                            const copiedAvailablity = JSON.parse(JSON.stringify(item.availablity));
                            const currentAvailablity = day.availablity;

                            currentAvailablity.forEach(element => {
                                element.isActive = false;
                            });

                            copiedAvailablity.forEach(element => {
                                element.id = "0"; 
                            });

                            day.availablity = copiedAvailablity;
                        }
                    });
                },
                initFormReady() {
                    if (this.preview.oldData === "") {
                        this.preview.oldData = JSON.stringify(this.$props.data.data);    
                    }

                    this.enablePreview(JSON.stringify(this.$props.data.data));
                },
                initPreview() {
                    this.preview.modal = true;
                },
                availabilityDone(options) {
                    this.isLoading = false;

                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                        this.$buefy.toast.open({
                            duration: 5000,
                            message: `${options.response.data.message}`,
                            position: 'is-bottom'
                        });
                        
                        if (typeof this.options.isPreview === 'undefined' || this.options.isPreview) {
                            this.weeks = [];
                            this.preview.oldData = "";
                            Event.$emit('initAvailability', this.$props.options.tab, this.$props.options.tabIndex);    
                        };

                    } else {
                        this.$buefy.dialog.alert({
                            title: "Availability",
                            message: `${options.response.data.message}`,
                            confirmText: 'Ok'
                        })
                    }
                },
                availabilityInit() {
                    this.isLoading = true;

                    const payload = JSON.parse(JSON.stringify(this.$props.data.data)),
                        weeks = payload.weeks,
                        existingWeeks = this.weeks,
                        instance = this;

                    for (let i = 0; i < weeks.length; i++) {
                        const day = weeks[i];
                        
                        if (existingWeeks.length !== 0) {
                            for (let j = 0; j < existingWeeks.length; j++) {
                                const existing = existingWeeks[j];
                                
                                if (day.day === existing.day) {
                                    const slots = existing.availablity;

                                    for (let k = 0; k < slots.length; k++) {
                                        const slot = slots[k];
                                        day.availablity.push(slot)    
                                    }
                                }
                            }
                        };
                    }

                    let url = "";

                    if (payload.hasRecord) {
                        url = YUNOCommon.config.createUpdateAvailabilityAPI("update")
                    } else {
                        url = YUNOCommon.config.createUpdateAvailabilityAPI("create")
                    }

                    const options = {
                        apiURL: url,
                        module: "gotData",
                        store: "instructorAvailability",
                        payload: JSON.stringify(payload),
                        headers: {
                            'accept': 'application/json',
                            'content-type': 'application/json'
                        },
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.availabilityDone(options)
                        }
                    };

                    if (payload.hasRecord) {
                        this.$store.dispatch('putData', options);
                    } else {
                        this.$store.dispatch('postData', options);
                    };
                },
                onFilterItemSelect(slot, data, type, item) {
                    slot[type] = data.slug;

                    if (data.slug !== "24") {
                        slot.isEnds = true;
                        item.is24Hours = false;
                    } else {
                        item.availablity = [];
                        item.availablity.push(slot)
                        item.is24Hours = true;
                        slot.isEnds = false;
                    };

                    this.enablePreview(JSON.stringify(this.$props.data.data));
                    this.manageOverlappingSlot(slot, item);
                    this.manageEndTime(slot);
                },
                removeSlot(item, slot) {
                    const availablity = item.availablity;

                    if (slot.isExist) {
                        let dayObj = {
                            day: item.day,
                            availablity: []
                        }
                        
                        slot.isActive = false;
                        dayObj.availablity.push(slot)

                        this.weeks.push(dayObj)
                    };

                    YUNOCommon.removeObjInArr(availablity, "slotID", slot.slotID);

                    for (let i = 0; i < availablity.length; i++) {
                        const element = availablity[i];
                        element.slotID = i;
                    };

                    if (availablity.length === 0) {
                        item.isDayOff = true;
                    }

                    this.enablePreview(JSON.stringify(this.$props.data.data));
                },
                addSlot(item) {
                    let totalItem = item.availablity.length === 1 ? 0 : item.availablity.length - 1,
                        availablity = item.availablity; 
                    const slotObj = JSON.parse(JSON.stringify(this.slot));

                    
                    slotObj.slotID = totalItem + 1;
                    item.availablity.push(slotObj);
                    this.enablePreview(JSON.stringify(this.$props.data.data));
                },
                enablePreview(data) {
                    if (data !== this.preview.oldData) {
                        this.preview.isMatched = false;
                    } else {
                        this.preview.isMatched = true;
                    }
                },
                onOpen(value, day) {
                    const slotObj = JSON.parse(JSON.stringify(this.slot));

                    slotObj.slotID = 0;

                    if (value) {
                        if (day.availablity.length === 0) {
                            day.availablity.push(slotObj);
                            this.enablePreview(JSON.stringify(this.$props.data.data));
                        }
                    } else {
                        day.availablity = [];    
                        this.enablePreview(JSON.stringify(this.$props.data.data));
                    }
                }
            }
        });
    };

    return {
        availability: availability
    };
})(jQuery);



