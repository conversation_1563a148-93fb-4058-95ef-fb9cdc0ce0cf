Vue.component('yuno-banner', {
    props: ["data", "options"],
    template: `
        <section class="banner">
            <div class="container">
                <div class="row">
                    <div class="col-md-8 offset-md-2 wrapper">
                        <div class="lftCol">
                            <h2>
                                {{ data.title }}
                            </h2>
                            <small>
                                {{ data.subtitle }}
                            </small>
                        </div>
                        <div class="ritCol">
                            <b-button tag="a"
                                :href="data.cta.url"
                                target="_blank"
                                class="yunoWhiteCTA">
                                {{ data.cta.label }}
                            </b-button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});