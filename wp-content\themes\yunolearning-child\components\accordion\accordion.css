.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined, #app .yunoAccordion.faq .faqCard .card-header .card-header-icon .icon .mdi:before {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

#app .dark87, #app .yunoAccordion .sectionTitle, #app .yunoAccordion.faq .faqCard .card-header .card-header-title {
  color: rgba(0, 0, 0, 0.87);
}

#app .dark60, #app .yunoAccordion.faq .faqCard .card-header .card-header-icon, #app .yunoAccordion.faq .faqCard .card-content {
  color: rgba(0, 0, 0, 0.6);
}

#app .yunoAccordion {
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .yunoAccordion {
    padding: 60px 0 30px;
  }
}

#app .yunoAccordion .sectionTitle {
  font-size: 32px;
  line-height: 40px;
  text-align: center;
  margin-bottom: 15px;
}

#app .yunoAccordion.faq .wrapper {
  background-color: rgba(0, 0, 0, 0.02);
  padding: 30px;
}

#app .yunoAccordion.faq .collapse:not(.show) {
  display: block;
}

#app .yunoAccordion.faq .faqCard {
  background: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  max-width: 100%;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
  padding: 10px 0;
}

#app .yunoAccordion.faq .faqCard:last-child {
  margin-bottom: 0;
}

#app .yunoAccordion.faq .faqCard.collapse .collapse-trigger {
  display: inline;
  cursor: pointer;
}

#app .yunoAccordion.faq .faqCard.collapse .collapse-trigger .collapse-content {
  display: inherit;
}

#app .yunoAccordion.faq .faqCard .card-header {
  background-color: transparent;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-shadow: none;
          box-shadow: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border: 0;
  padding: 0;
}

#app .yunoAccordion.faq .faqCard .card-header .card-header-title {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0;
  font-size: 16px;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  font-weight: 500;
  line-height: 24px;
  margin: 0 0 5px;
}

#app .yunoAccordion.faq .faqCard .card-header .card-header-icon {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0;
}

#app .yunoAccordion.faq .faqCard .card-header .card-header-icon .icon .mdi:before {
  content: "\e145";
}

#app .yunoAccordion.faq .faqCard .card-header .card-header-icon .icon .mdi.mdi-menu-down:before {
  content: "\e15b";
}

#app .yunoAccordion.faq .faqCard .card-header.active .card-header-title {
  color: #A81E22;
}

#app .yunoAccordion.faq .faqCard .card-header.active .card-header-icon {
  color: #A81E22;
}

#app .yunoAccordion.faq .faqCard .card-content, #app .yunoAccordion.faq .faqCard .card-footer {
  background-color: transparent;
}

#app .yunoAccordion.faq .faqCard .card-content {
  padding: 0;
  margin: 0;
  border-radius: 0;
  font-size: 14px;
  background: none;
  line-height: 20px;
}

#app .yunoAccordion.faq .faqCard .card-content .alphaList {
  margin: 0 0 0 16px;
}

#app .yunoAccordion.faq .faqCard .card-content .alphaList li {
  list-style: lower-alpha outside;
  margin-bottom: 5px;
}

#app .yunoAccordion.faq .faqCard .card-content .alphaList li:last-child {
  margin-bottom: 0;
}

#app .yunoAccordion.faq .faqCard .slide-enter-active, #app .yunoAccordion.faq .faqCard .slide-leave-active {
  -webkit-transition: .15s ease-out;
  transition: .15s ease-out;
}

#app .yunoAccordion.faq .faqCard .slide-enter-to, #app .yunoAccordion.faq .faqCard .slide-leave {
  max-height: 100px;
  overflow: hidden;
}

#app .yunoAccordion.faq .faqCard .slide-enter, #app .yunoAccordion.faq .faqCard .slide-leave-to {
  overflow: hidden;
  max-height: 0;
}
/*# sourceMappingURL=accordion.css.map */