var VueObserveVisibility=function(e){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function n(e){return function(e){if(Array.isArray(e)){for(var t=0,i=new Array(e.length);t<e.length;t++)i[t]=e[t];return i}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var r=function(){function e(t,i,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t,this.observer=null,this.frozen=!1,this.createObserver(i,n)}var t,r,o;return t=e,(r=[{key:"createObserver",value:function(e,t){var i=this;if(this.observer&&this.destroyObserver(),!this.frozen){var r;if(this.options="function"==typeof(r=e)?{callback:r}:r,this.callback=function(e,t){i.options.callback(e,t),e&&i.options.once&&(i.frozen=!0,i.destroyObserver())},this.callback&&this.options.throttle){var o=(this.options.throttleOptions||{}).leading;this.callback=function(e,t){var i,r,o,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=function(s){for(var a=arguments.length,c=new Array(a>1?a-1:0),u=1;u<a;u++)c[u-1]=arguments[u];if(o=c,!i||s!==r){var f=l.leading;"function"==typeof f&&(f=f(s,r)),i&&s===r||!f||e.apply(void 0,[s].concat(n(o))),r=s,clearTimeout(i),i=setTimeout(function(){e.apply(void 0,[s].concat(n(o))),i=0},t)}};return s._clear=function(){clearTimeout(i),i=null},s}(this.callback,this.options.throttle,{leading:function(e){return"both"===o||"visible"===o&&e||"hidden"===o&&!e}})}this.oldResult=void 0,this.observer=new IntersectionObserver(function(e){var t=e[0];if(e.length>1){var n=e.find(function(e){return e.isIntersecting});n&&(t=n)}if(i.callback){var r=t.isIntersecting&&t.intersectionRatio>=i.threshold;if(r===i.oldResult)return;i.oldResult=r,i.callback(r,t)}},this.options.intersection),t.context.$nextTick(function(){i.observer&&i.observer.observe(i.el)})}}},{key:"destroyObserver",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.callback&&this.callback._clear&&(this.callback._clear(),this.callback=null)}},{key:"threshold",get:function(){return this.options.intersection&&this.options.intersection.threshold||0}}])&&i(t.prototype,r),o&&i(t,o),e}();function o(e,t,i){var n=t.value;if(n)if("undefined"==typeof IntersectionObserver)console.warn("[vue-observe-visibility] IntersectionObserver API is not available in your browser. Please install this polyfill: https://github.com/w3c/IntersectionObserver/tree/master/polyfill");else{var o=new r(e,n,i);e._vue_visibilityState=o}}function l(e){var t=e._vue_visibilityState;t&&(t.destroyObserver(),delete e._vue_visibilityState)}var s={bind:o,update:function(e,i,n){var r=i.value;if(!function e(i,n){if(i===n)return!0;if("object"===t(i)){for(var r in i)if(!e(i[r],n[r]))return!1;return!0}return!1}(r,i.oldValue)){var s=e._vue_visibilityState;r?s?s.createObserver(r,n):o(e,{value:r},n):l(e)}},unbind:l};function a(e){e.directive("observe-visibility",s)}var c={version:"0.4.6",install:a},u=null;return"undefined"!=typeof window?u=window.Vue:"undefined"!=typeof global&&(u=global.Vue),u&&u.use(c),e.ObserveVisibility=s,e.default=c,e.install=a,e}({});
