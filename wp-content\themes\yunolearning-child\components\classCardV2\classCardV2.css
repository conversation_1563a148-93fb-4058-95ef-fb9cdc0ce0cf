#app .cardSection .card-content {
  border: 1px solid #E6E6E6;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  padding: 15px 25px;
  cursor: pointer;
}

#app .cardSection .card-content:hover {
  border-color: #A81E22;
}

#app .cardSection .card-content .cardContentWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 5px;
}

#app .cardSection .card-content .cardContentWrapper .classStatus {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  font-size: 10px;
  font-weight: 500;
  line-height: 14px;
  letter-spacing: 1.5px;
  text-align: left;
  text-underline-position: from-font;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
  color: #ff0000;
}

#app .cardSection .card-content .cardContentWrapper .classStatus .dot {
  border: 1px solid;
  border-radius: 50%;
  height: 10px;
  width: 10px;
  background: #ff0000;
}

#app .cardSection .card-content .cardContentWrapper .learnerAttendence {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 7px;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.25px;
}

#app .cardSection .card-content .cardContentWrapper .learnerAttendence .bgLightPink {
  background: #FFDAD7;
  color: #ef4444;
}

#app .cardSection .card-content .cardContentWrapper .classType {
  font-size: 10px;
  font-weight: 500;
  line-height: 14px;
  letter-spacing: 1.5px;
  text-align: center;
  width: 80px;
  padding: 3px;
  border-radius: 4px;
}

#app .cardSection .card-content .cardContentWrapper .classType.bgLightBlue {
  background: #cce5ff;
}

#app .cardSection .card-content .cardContentWrapper .classType.lightPeach {
  background-color: #ffe5cc;
}

#app .cardSection .card-content .cardContentWrapper .classType.bgLightGreen {
  background: #d4edda;
  color: #534342;
}

#app .cardSection .card-content .cardContentWrapper .classTitle {
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.15px;
  color: #201A19;
}

#app .cardSection .card-content .cardContentWrapper .underline {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
  -webkit-text-decoration-style: solid;
          text-decoration-style: solid;
}

#app .cardSection .card-content .cardContentWrapper .classDetails {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 12px;
}

#app .cardSection .card-content .cardContentWrapper .classDetails span {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.25px;
}

#app .cardSection .card-content .cardContentWrapper .classDetails .recordingDuration {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 4px;
}

#app .cardSection .card-content .cardContentWrapper .classDetails .recordingDuration .playIcon {
  border: 2px solid #5f6368;
  border-radius: 50%;
  color: #5f6368;
  width: 18px;
  height: 18px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .cardSection .card-content .cardContentWrapper .userProfile {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 5px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .cardSection .card-content .cardContentWrapper .userProfile span img {
  border-radius: 50%;
  width: 34px;
  height: 35px;
}

#app .cardSection .card-content .cardContentWrapper .userProfile .userDescription {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 1px;
}

#app .cardSection .card-content .cardContentWrapper .progress-wrapper .progress {
  width: 137px;
  height: 7px;
}

#app .cardSection .card-content .cardContentWrapper .learnerList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .cardSection .card-content .cardContentWrapper .learnerList li {
  margin-left: -12px;
}

#app .cardSection .card-content .cardContentWrapper .learnerList li img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

#app .cardSection .card-content .buttonWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 20px;
  padding-top: 10px;
}

#app .cardSection .card-content .buttonWrapper a {
  width: 100%;
}

#app .cardSection .card-content .buttonWrapper a.secondaryCTA {
  padding: 10px 20px;
  border-radius: 4px;
  border: 1px solid #d0c4c2;
  background-color: #fff;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  color: #201a19;
}

#app .cardSection .card-content .buttonWrapper a.secondaryCTA:hover {
  text-decoration: none !important;
  border-color: #a81e22;
}

#app .cardSection .card-content .buttonWrapper .academyLabel {
  text-align: center;
}

#app .cardSection .card-content .starIcon {
  color: #f9b600;
  font-size: 16px;
}

#app .cardSection .scheduleModal .yunoModal .modal-content {
  height: 490px !important;
  border: 1px solid #E6E6E6 !important;
}

#app .cardSection .scheduleModal .yunoModal .modal-background {
  background-color: transparent !important;
}

#app .cardSection .scheduleModal .yunoModal .modalTitle {
  padding: 40px 20px 0 20px;
}

#app .cardSection .scheduleModal .yunoModal .modalBody {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

@media (max-width: 768px) {
  #app .cardSection .scheduleModal .yunoModal .modalBody {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 20px;
  }
}

#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper {
  width: 70%;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 6px;
  border-bottom: 1px solid #E6E6E6;
  width: 300px;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .flexDiv {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-left: 10px;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList li {
  margin-left: -12px;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerList li img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .classInfo .learnerEnrolled {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  gap: 6px;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .courseInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 6px;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .classInfoWrapper .courseInfo .academyFavIcon img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .addLearners {
  width: 30%;
}

#app .cardSection .scheduleModal .yunoModal .modalBody .addLearners .totalLearners {
  border-bottom: 2px solid #E6E6E6;
}

#app .cardSection .scheduleModal .yunoModal .modalFooter {
  padding-bottom: 20px;
}

#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper {
  gap: 10px;
}

#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper .primaryCTA {
  padding: 7px 10px;
  border-radius: 4px;
  border: 1px solid #a81e22;
  background-color: #a81e22;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}

#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper .primaryCTA:hover {
  text-decoration: none;
  background-color: #410004;
}

#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper .secondaryCTA {
  padding: 7px 10px;
  border-radius: 4px;
  border: 1px solid #d0c4c2;
  background-color: #fff;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  min-width: 80px !important;
  color: #201a19;
}

#app .cardSection .scheduleModal .yunoModal .modalFooter .ctaWrapper .secondaryCTA:hover {
  text-decoration: none !important;
  border-color: #a81e22;
}

#app .cardSection .yunoSnackbar .snackbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  padding: 15px;
  background: white;
  width: 400px;
  overflow-y: auto;
}

#app .cardSection .yunoSnackbar .vue-star-rating[data-v-fde73a0c] {
  -webkit-box-pack: center !important;
      -ms-flex-pack: center !important;
          justify-content: center !important;
}

#app .cardSection .yunoSnackbar .yunoInput .textarea:not([rows]) {
  max-height: 6em !important;
  min-height: 4em !important;
}

#app .yunoSnackbar .snackbar .closeSnackbar .material-icons-outlined {
  font-size: 18px;
}

#app .yunoSnackbar .titleLarge {
  font-size: 24px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 5px;
}

#app .yunoSnackbar .titleSmall {
  font-size: 16px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .yunoSnackbar .subtitleSmall {
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 15px;
}

#app .yunoSnackbar .noticeSmall {
  margin-bottom: 10px;
}

#app .yunoSnackbar .noticeTitle {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 1.5px;
}

#app .yunoSnackbar .mappedInstructor {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
}

#app .yunoSnackbar .mappedInstructor .imgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50px;
          flex: 0 0 50px;
  margin-right: 10px;
}

#app .yunoSnackbar .mappedInstructor .imgWrapper img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 0;
  background-color: #FFF;
}

#app .yunoSnackbar .mappedInstructor figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 60px);
          flex: 0 0 calc(100% - 60px);
}

#app .yunoSnackbar .mappedInstructor .insName {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 5px;
}

#app .yunoSnackbar .mappedInstructor .studentCount {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
}

#app .yunoSnackbar .mappedInstructor.gapBtm15 {
  margin-bottom: 15px;
}

#app .yunoSnackbar .ctaWrapper {
  margin-top: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .yunoSnackbar .ctaWrapper .button {
  margin-right: 10px;
}

#app .yunoSnackbar .formWrapper {
  width: 100%;
  position: relative;
  background: #fafafa;
  padding: 10px;
}

#app .yunoSnackbar .formWrapper .innerWrapper {
  background-color: #fafafa;
  padding: 10px;
  margin-bottom: 15px;
}

#app .yunoSnackbar .formWrapper .innerWrapper .groupElement {
  margin: 0;
}

#app .yunoSnackbar .formWrapper .vue-star-rating {
  padding-left: 15px;
}

#app .yunoSnackbar .formWrapper .field.noGap {
  margin: 0;
}

#app .yunoSnackbar .formWrapper .alert {
  height: 300px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .yunoSnackbar .formWrapper .alert .material-icons-outlined {
  font-size: 40px;
  margin-bottom: 10px;
}

#app .yunoSnackbar .formWrapper .ctaWrapper {
  -webkit-box-pack: right;
      -ms-flex-pack: right;
          justify-content: right;
}

#app .yunoSnackbar .formWrapper .ctaWrapper .button {
  margin-right: 0;
}

#app .yunoSnackbar .formWrapper .ctaWrapper.loading {
  position: absolute;
  right: 5px;
  bottom: 54px;
}

#app .yunoSnackbar .formWrapper .ctaWrapper.loading .button {
  border: 0;
}

#app .yunoSnackbar .formWrapper .checkList .fieldLabel {
  font-size: 14px;
  margin: 0 0 10px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 600;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList .field {
  margin-right: 10px;
}

#app .yunoSnackbar .formWrapper .checkList .checkboxList .b-checkbox {
  border-radius: 20px;
  padding: 5px 10px;
  height: auto;
  font-size: 12px;
}

#app .yunoSnackbar .formWrapper.noBG {
  background: none;
  padding: 0;
}

#app .yunoSnackbar .formWrapper.gapTop15 {
  padding-top: 15px;
}

#app .yunoSnackbar .starLabel {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: 12px;
  margin-top: 8px;
}

#app .yunoSnackbar .starLabel li {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 67px;
          flex: 0 0 67px;
  text-align: center;
}

#app .yunoSnackbar .starLabel li.active {
  visibility: visible;
}

#app .yunoSnackbar .starLabel li.notActive {
  visibility: hidden;
}

#app .yunoSnackbar .checkboxList .field {
  display: inline-block !important;
  margin-bottom: 2px !important;
  margin-right: 6px !important;
}

.b-sidebar.extraWide .sidebar-content {
  width: 550px !important;
}

.b-sidebar.card-slidebar .sidebar-content {
  width: 390px;
}

.b-sidebar.card-slidebar .sidebar-content.is-light {
  background-color: white !important;
}

.b-sidebar.card-slidebar .card-content {
  padding-top: 70px;
}

.b-sidebar.card-slidebar .card-content .headline5 {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.card-slidebar .card-content .subtitle1 {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.card-slidebar .card-content .subtitle1.noBold {
  font-weight: 400;
}

.b-sidebar.card-slidebar .card-content .underline {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
  -webkit-text-decoration-style: solid;
          text-decoration-style: solid;
}

.b-sidebar.card-slidebar .card-content .subtitle3 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

.b-sidebar.card-slidebar .card-content .subtitle2 {
  font-size: 14px;
  line-height: 18px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.card-slidebar .card-content .subtitle2.noBold {
  font-weight: 400;
}

.b-sidebar.card-slidebar .card-content .subtitle3 {
  font-size: 16px;
  line-height: 18px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.card-slidebar .card-content .subtitle3.noBold {
  font-weight: 400;
}

.b-sidebar.card-slidebar .card-content .overline {
  font-size: 12px;
  line-height: 14px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 7px;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .videoWrapper {
  position: relative;
  padding-bottom: 45.7%;
  overflow: hidden;
  max-width: 100%;
  margin: 0 auto 30px;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .videoWrapper object {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .videoWrapper embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .videoWrapper.loading {
  padding-bottom: 0;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .classStatus {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  font-size: 10px;
  font-weight: 500;
  line-height: 14px;
  letter-spacing: 1.5px;
  text-align: left;
  text-underline-position: from-font;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
  color: #ff0000;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .classStatus .dot {
  border: 1px solid;
  border-radius: 50%;
  height: 10px;
  width: 10px;
  background: #ff0000;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .progress-wrapper .progress {
  width: 137px;
  height: 7px;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .learnerAttendence {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 7px;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.25px;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .learnerAttendence .progress-wrapper .progress {
  width: 137px;
  height: 7px;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .learnerAttendence .bgLightPink {
  background: #FFDAD7;
  color: #ef4444;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .classType {
  font-size: 10px;
  font-weight: 500;
  line-height: 14px;
  letter-spacing: 1.5px;
  text-align: center;
  width: 80px;
  padding: 3px;
  border-radius: 4px;
  background: #cce5ff;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .classTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
  color: #201A19;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .classDetails {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .classDetails span {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0.25px;
}

.b-sidebar.card-slidebar .card-content .cardContentWrapper .closeSidebar {
  margin-left: -8px;
  cursor: pointer;
}

.b-sidebar.card-slidebar .card-content .button.yunoPrimaryCTA {
  padding: 10px 20px;
  border-radius: 4px;
  border: 1px solid #a81e22;
  background-color: #A81E22;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  color: #FFF;
}

.b-sidebar.card-slidebar .card-content .button.yunoPrimaryCTA:hover {
  text-decoration: none;
  background-color: #410004;
}

.b-sidebar.card-slidebar .card-content .buttonWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 20px;
  padding-top: 10px;
  width: 150px;
}

.b-sidebar.card-slidebar .card-content .buttonWrapper a {
  width: 100%;
}

.b-sidebar.card-slidebar .card-content .buttonWrapper a.secondaryCTA {
  padding: 10px 20px;
  border-radius: 4px;
  border: 1px solid #d0c4c2;
  background-color: #fff;
  line-height: normal;
  font-size: 16px;
  font-weight: 500;
  color: #201a19;
}

.b-sidebar.card-slidebar .card-content .buttonWrapper a.secondaryCTA:hover {
  text-decoration: none !important;
  border-color: #a81e22;
}

.b-sidebar.card-slidebar .card-content .classReview {
  gap: 10px;
}

.b-sidebar.card-slidebar .card-content .classReview img {
  border-radius: 50%;
  width: 70px;
}

.b-sidebar.card-slidebar .card-content .classReview .learnerReviewWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 4px;
}

.b-sidebar.card-slidebar .card-content .userProfile {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.b-sidebar.card-slidebar .card-content .userProfile img {
  width: 40px;
  border-radius: 50%;
}

.b-sidebar.card-slidebar .card-content .userProfile img.userImage {
  width: 82px !important;
}

.b-sidebar.card-slidebar .card-content .userProfile .userDescription {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 3px;
}

.b-sidebar.card-slidebar .card-content .learnerList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
}

.b-sidebar.card-slidebar .card-content .learnerList li {
  margin-left: -12px;
}

.b-sidebar.card-slidebar .card-content .learnerList li img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.b-sidebar.card-slidebar .card-content .dropdownWrapper .select select option {
  color: #534342;
}

.b-sidebar.card-slidebar .card-content .starIcon {
  color: #f9b600;
  font-size: 16px;
}

.b-sidebar.card-slidebar .card-content .instructorLearners ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.b-sidebar.card-slidebar .card-content .instructorLearners ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  gap: 1rem;
  border: 1px solid #E6E6E6;
  padding: 10px 10px;
}

.b-sidebar.card-slidebar .card-content .instructorLearners ul li .learnerProfile {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0.5rem;
}

.b-sidebar.card-slidebar .card-content .instructorLearners ul li .learnerProfile img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.b-sidebar.card-slidebar .card-content .instructorLearners ul li .learnerAttendance {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0.5rem;
}

.b-sidebar.card-slidebar .card-content .instructorLearners ul li .learnerAttendance .progress {
  width: 100px;
  height: 7px;
}

.b-sidebar.card-slidebar .card-content .instructorLearners ul li .learnerRating {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 0.3rem;
}

.b-sidebar.card-slidebar .separator {
  margin: 0 8px;
  color: #b0b0b0;
}

.yunoSnackbar .classCheckbox {
  display: inline-block;
}

.yunoSnackbar .classCheckbox .button {
  padding: 0 8px !important;
  border-radius: 20px !important;
  font-size: 11px !important;
}

.separator {
  margin: 0 8px;
  color: #b0b0b0;
}

@media (max-width: 768px) {
  #app .cardSection .card-content .buttonWrapper .academyLabel {
    text-align: start;
  }
}

@media (max-width: 767px) {
  #app .cardSection .card-content {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
            flex-direction: column !important;
  }
  #app .cardSection .card-content .cardContentWrapper .learnerAttendence {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
  }
  #app .cardSection .card-content .cardContentWrapper .classDetails {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 5px !important;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
  }
  #app .cardSection .card-content .buttonWrapper a {
    width: 100%;
  }
  #app .cardSection .card-content .buttonWrapper a.secondaryCTA {
    padding: 10px 20px;
    border-radius: 4px;
    border: 1px solid #d0c4c2;
    background-color: #fff;
    line-height: normal;
    font-size: 16px;
    font-weight: 500;
    color: #201a19;
  }
  #app .cardSection .card-content .buttonWrapper a.secondaryCTA:hover {
    text-decoration: none !important;
    border-color: #a81e22;
  }
  .b-sidebar.card-slidebar .sidebar-content {
    width: 100%;
  }
  .b-sidebar.card-slidebar .card-content .cardContentWrapper .learnerAttendence {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
  }
  .b-sidebar.card-slidebar .card-content .buttonWrapper a {
    width: 100%;
  }
}
/*# sourceMappingURL=classCardV2.css.map */