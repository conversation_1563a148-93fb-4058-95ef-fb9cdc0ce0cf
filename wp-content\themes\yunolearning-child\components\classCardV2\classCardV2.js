Vue.component("yuno-classCard-v2", {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  template: `
    <div :id="data.id" class="cardSection p-bottom-larger-times-1">
      <div class="card-content" @click="openDrawer(data)" >
        <template v-if="!data.isPlaceholder">
          <div class="cardContentWrapper">
            <div class="classStatus" v-if="data.temporal_status">
              <span class="dot"></span>
              <span class="overline">{{ data.temporal_status }}</span>
            </div>
            <span class="onSurfaceVariant caption1">{{ data.course.title }}</span>
            <div>
              <span :class="['classType', 'onSurfaceVariant', data.type == 'WEBINAR' ? 'lightPeach' : 'bgLightBlue']">
                {{ data.type }}
              </span>
              <span 
                class="classType onSurfaceVariant bgLightGreen" 
                v-if="data?.status == 'past' && data?.recording?.url == ''"
              >
                RECORDING NOT AVAILABLE
              </span>
              <span 
                v-if="data?.status == 'past' && data?.recording?.url != ''" 
                class="classType onSurfaceVariant bgLightGreen"
              >
                RECORDING AVAILABLE
              </span>
            </div>
            <span class="classTitle">{{ data.class_title.title }}</span>
            <div class="classDetails">
              <span class="onSurfaceVariant">
                {{ formatDuration(data.scheduled.start.time, data.scheduled.end.time) }}
              </span>
              <span class="onSurfaceVariant">BATCH: {{ data.batch.title }}</span>
              <span class="onSurfaceVariant">ID: {{ data.batch.id }}</span>
              <div class="d-flex">
                <div class="recordingDuration" v-if="data?.status == 'past'">
                  <div class="playIcon">
                    <span class="material-icons">play_arrow</span>
                  </div>
                  <span>{{ data?.recording?.duration }} minutes</span>
                </div>
              </div>
            </div>
            <div class="learnerAttendence" v-if="userRole.data == 'Learner' && data?.status == 'past'">
              <template v-if="data?.attendance.status">
                <div class="d-flex">
                  <span class="onSurfaceVariant pr-1">
                    Attendance: {{ data?.attendance.duration }} minutes
                  </span>
                  <span class="onSurfaceVariant">
                    ({{ (data?.attendance?.duration_percentage || 0) }}%)
                  </span>
                </div>
                <b-progress
                  type="is-info"
                  :value="data?.attendance?.duration_percentage || 0"
                  style="flex-grow: 1;"
                >
                </b-progress>
              </template>
              <template v-else>
                <span class="classType bgLightPink">ABSENT</span>
              </template>
            </div>
            <div class="userProfile">
              <template v-if="userRole.data == 'Learner'">
                <span>
                  <img :src="data?.instructor.user.image_url" alt="instructor-image" />
                </span>
                <div class="userDescription">
                  <span class="onSurfaceVariant caption1 noBold">
                    {{ data?.instructor.user.role[0] }}
                  </span>
                  <span class="onSurface subtitle2 underline">
                    {{ data?.instructor.user.full_name }}
                  </span>
                </div>
              </template>
              <template v-else>
                <template v-if="data?.status == 'past'">
                  <ul class="learnerList">
                    <template v-if="data?.enrollments?.learners.length > 0">
                      <li class="pl-3" v-for="(enrollment, index) in limitedEnrollments" :key="index">
                        <figure class="learnerMapped">
                          <img :src="enrollment.image_url" alt="Learner Image" />
                        </figure>
                      </li>
                    </template>
                    <template v-else>
                      <span class="material-icons">account_circle</span>
                    </template>
                  </ul>
                  <div class="d-flex" style="align-items: center; gap: 6px">
                    <span class="onSurfaceVariant subtitle2 noBold">
                      {{ data?.attendance_of_each?.length || 0 }} of {{ data?.enrollments?.length || 0 }} attended
                    </span>
                    <span class="onSurfaceVariant subtitle2 noBold">({{ progressValue }}%)</span>
                    <b-progress
                      type="is-info"
                      :value="progressValue"
                      style="flex-grow: 1;"
                    ></b-progress>
                  </div>
                </template>
                <template v-else>
                 <template v-if="data?.enrollments?.learners?.length > 0">
                    <ul class="learnerList pl-3">
                      <li  v-for="(enrollment, index) in limitedEnrollments" :key="index">
                        <figure class="learnerMapped">
                          <img :src="enrollment.image_url" alt="Learner Image" />
                        </figure>
                      </li>
                    </ul>
                  </template>
                  <template v-else>
                    <span class="material-icons">account_circle</span>
                  </template>
                  <div>
                   <span class="onSurfaceVariant subtitle2 noBold">
                      {{ data?.enrollments?.learners?.length > 0 ? data.enrollments.learners.length + ' Students enrolled' : 'No students enrolled yet' }}
                    </span>
                  </div>
                </template>
              </template>
            </div>
          </div>
          <div class="buttonWrapper" >
            <div class="academyLabel">
              <span v-if="data?.academy?.name" class="favIcon onSurfaceVariant caption1 ">
                {{ data.academy.name }}
              </span>
            </div>
            <div class="cta" v-if="data?.temporal_status">
              <b-button
                tag="a"
                class=" yunoPrimaryCTA"
                :disabled="!data.virtual_classroom.meeting_url || data.virtual_classroom.meeting_url.trim() === ''"
                :href="data.virtual_classroom.meeting_url"
              >
                Launch Class
              </b-button>
            </div>
						<div class="cta" v-if=" userRole.data == 'Instructor' && data?.temporal_status">
              <b-button
                tag="a"
                class="secondaryCTA"
                @click.stop="openScheduleModal(data)"
              >
                Schedule Class
              </b-button>
					  </div>
          </div>
        </template>
        <template v-else>
          <div>
            <span class="onSurfaceVariant">No Class</span>
          </div>
        </template>
      </div>
      <b-sidebar
        type="is-light"
        :fullheight="fullheight"
        :fullwidth="fullwidth"
        :overlay="overlay"
        :right="right"
        v-model="open"
        class="card-slidebar"
        :class="{ extraWide: data?.status == 'past' }"
      >
        <yuno-classCard-drawer
          v-if="drawerData && drawerData.type"
          :data="drawerData"
          @closeSidebar="closeSidebar"
        >
        </yuno-classCard-drawer>
      </b-sidebar>
    </div>
  `,

  computed: {
    ...Vuex.mapState(["userInfo", "userRole", "drawer"]),
    isAbsent() {
      return (
        this.data?.actual?.duration?.length > 0 ||
        (this.data?.enrollments?.learners.length === 0 &&
          this.data?.attendance_of_each.length === 0)
      );
    },
    attendancePercentage() {
      return this.data?.attendance_of_each?.duration_percentage || 0;
    },
    limitedEnrollments() {
      return this.data?.enrollments?.learners.slice(0, 5) || [];
    },
    progressValue() {
      const attendanceCount = this.data?.attendance_of_each?.length || 0;
      const enrollmentCount = this.data?.enrollments?.learners.length || 1; // Avoid division by zero
      return (attendanceCount * 100) / enrollmentCount;
    },
  },

  data() {
    return {
      open: false,
      drawerData: null,
      snackbarData: null,
      fullheight: true,
      fullwidth: true,
      overlay: true,
      right: true,
    };
  },

  mounted() {},

  methods: {
    openDrawer(data) {
      this.open = true;
      this.drawerData = data;
    },

    openScheduleModal(data) {
      this.drawer.data = [];
      this.drawer.data = data;
      this.drawer.isActive = true;
    },

    closeSidebar() {
      this.open = false;
    },
    redirectToPage(targetUrl) {
      window.location.href = targetUrl;
    },

    formatDuration(start, end) {
      const startTime = new Date(start);
      const endTime = new Date(end);
      return `${startTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })} - ${endTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    },
    updateClassCardRating(newRating) {
      if (this.snackbarData) {
        this.snackbarData.rating = newRating; // Update the rating in the class card data
      }
    },
    openSnackbar(data) {
      this.snackbarData = data;
      this.$refs.snackbar.syncRating(this.data.rating); // Sync the current rating
      this.setRating();
    },

    setRating() {
      let rating = this.data.rating;
      if (rating === 5) {
        this.$refs.snackbar.setRating(rating);
      } else {
        this.$refs.snackbar.syncRating(rating);
        this.$refs.snackbar.setRating(rating);
        this.$refs.snackbar.openSnackbar();
        this.$refs.snackbar.fetchIssues();
      }
    },
  },
});
