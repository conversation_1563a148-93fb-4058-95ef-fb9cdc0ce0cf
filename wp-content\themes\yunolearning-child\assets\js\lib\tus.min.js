!function(e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).tus=e()}(function(){return function n(o,i,a){function u(t,e){if(!i[t]){if(!o[t]){var r="function"==typeof require&&require;if(!e&&r)return r(t,!0);if(s)return s(t,!0);throw(r=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",r}r=i[t]={exports:{}},o[t][0].call(r.exports,function(e){return u(o[t][1][e]||e)},r,r.exports,n,o,i,a)}return i[t].exports}for(var s="function"==typeof require&&require,e=0;e<a.length;e++)u(a[e]);return u}({1:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var o=n(e("./isReactNative")),i=n(e("./uriToBlob")),a=n(e("./sources/FileSource")),u=n(e("./sources/StreamSource"));function n(e){return e&&e.__esModule?e:{default:e}}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}r.default=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,r,n;return t=e,(r=[{key:"openFile",value:function(e,t){return(0,o.default)()&&e&&void 0!==e.uri?(0,i.default)(e.uri).then(function(e){return new a.default(e)}).catch(function(e){throw new Error("tus: cannot fetch `file.uri` as Blob, make sure the uri is correct and accessible. ".concat(e))}):"function"==typeof e.slice&&void 0!==e.size?Promise.resolve(new a.default(e)):"function"==typeof e.read?(t=+t,isFinite(t)?Promise.resolve(new u.default(e,t)):Promise.reject(new Error("cannot create source for stream without a finite value for the `chunkSize` option"))):Promise.reject(new Error("source object may only be an instance of File, Blob, or Reader in this environment"))}}])&&s(t.prototype,r),n&&s(t,n),e}()},{"./isReactNative":5,"./sources/FileSource":6,"./sources/StreamSource":7,"./uriToBlob":10}],2:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t){if((0,o.default)())return Promise.resolve(function(e,t){var r=e.exif?function(e){var t=0;if(0===e.length)return t;for(var r=0;r<e.length;r++){var n=e.charCodeAt(r);t=(t<<5)-t+n,t&=t}return t}(JSON.stringify(e.exif)):"noexif";return["tus-rn",e.name||"noname",e.size||"nosize",r,t.endpoint].join("/")}(e,t));return Promise.resolve(["tus-br",e.name,e.type,e.size,e.lastModified,t.endpoint].join("-"))};var n,o=(n=e("./isReactNative"))&&n.__esModule?n:{default:n}},{"./isReactNative":5}],3:[function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,r.default=function(){function e(){n(this,e)}return i(e,[{key:"createRequest",value:function(e,t){return new a(e,t)}},{key:"getName",value:function(){return"XHRHttpStack"}}]),e}();var a=function(){function r(e,t){n(this,r),this._xhr=new XMLHttpRequest,this._xhr.open(e,t,!0),this._method=e,this._url=t,this._headers={}}return i(r,[{key:"getMethod",value:function(){return this._method}},{key:"getURL",value:function(){return this._url}},{key:"setHeader",value:function(e,t){this._xhr.setRequestHeader(e,t),this._headers[e]=t}},{key:"getHeader",value:function(e){return this._headers[e]}},{key:"setProgressHandler",value:function(t){"upload"in this._xhr&&(this._xhr.upload.onprogress=function(e){e.lengthComputable&&t(e.loaded)})}},{key:"send",value:function(){var r=this,n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null;return new Promise(function(e,t){r._xhr.onload=function(){e(new u(r._xhr))},r._xhr.onerror=function(e){t(e)},r._xhr.send(n)})}},{key:"abort",value:function(){return this._xhr.abort(),Promise.resolve()}},{key:"getUnderlyingObject",value:function(){return this._xhr}}]),r}(),u=function(){function t(e){n(this,t),this._xhr=e}return i(t,[{key:"getStatus",value:function(){return this._xhr.status}},{key:"getHeader",value:function(e){return this._xhr.getResponseHeader(e)}},{key:"getBody",value:function(){return this._xhr.responseText}},{key:"getUnderlyingObject",value:function(){return this._xhr}}]),t}()},{}],4:[function(e,t,r){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"enableDebugLog",{enumerable:!0,get:function(){return a.enableDebugLog}}),Object.defineProperty(r,"canStoreURLs",{enumerable:!0,get:function(){return u.canStoreURLs}}),Object.defineProperty(r,"HttpStack",{enumerable:!0,get:function(){return s.default}}),r.isSupported=r.defaultOptions=r.Upload=void 0;var i=c(e("../upload")),n=c(e("../noopUrlStorage")),a=e("../logger"),u=e("./urlStorage"),s=c(e("./httpStack")),l=c(e("./fileReader")),e=c(e("./fingerprint"));function c(e){return e&&e.__esModule?e:{default:e}}function f(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function p(e,t){return(p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function d(r){var n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=h(r);return e=n?(e=h(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),t=this,!(e=e)||"object"!==o(e)&&"function"!=typeof e?function(e){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(t):e}}function h(e){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function v(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function y(n){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?v(Object(o),!0).forEach(function(e){var t,r;t=n,e=o[r=e],r in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(o)):v(Object(o)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(o,e))})}return n}var g=y(y({},i.default.defaultOptions),{},{httpStack:new s.default,fileReader:new l.default,urlStorage:new(u.canStoreURLs?u.WebStorageUrlStorage:n.default),fingerprint:e.default});r.defaultOptions=g,r.Upload=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&p(e,t)}(o,i["default"]);var e,t,r,n=d(o);function o(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,o),t=y(y({},g),t),n.call(this,e,t)}return e=o,r=[{key:"terminate",value:function(e,t,r){return t=y(y({},g),t),i.default.terminate(e,t,r)}}],(t=null)&&f(e.prototype,t),r&&f(e,r),o}();n=window,e=n.XMLHttpRequest,n=n.Blob,n=e&&n&&"function"==typeof n.prototype.slice;r.isSupported=n},{"../logger":13,"../noopUrlStorage":14,"../upload":15,"./fileReader":1,"./fingerprint":2,"./httpStack":3,"./urlStorage":11}],5:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;r.default=function(){return"undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase()}},{}],6:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var o=n(e("./isCordova")),i=n(e("./readAsByteArray"));function n(e){return e&&e.__esModule?e:{default:e}}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}r.default=function(){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),this._file=e,this.size=e.size}var e,r,n;return e=t,(r=[{key:"slice",value:function(e,t){if((0,o.default)())return(0,i.default)(this._file.slice(e,t));t=this._file.slice(e,t);return Promise.resolve({value:t})}},{key:"close",value:function(){}}])&&a(e.prototype,r),n&&a(e,n),t}()},{"./isCordova":8,"./readAsByteArray":9}],7:[function(e,t,r){"use strict";function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e){return void 0===e?0:void 0!==e.size?e.size:e.length}Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,r.default=function(){function r(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),this._chunkSize=t,this._buffer=void 0,this._bufferOffset=0,this._reader=e,this._done=!1}var e,t,n;return e=r,(t=[{key:"slice",value:function(e,t){return e<this._bufferOffset?Promise.reject(new Error("Requested data is before the reader's current offset")):this._readUntilEnoughDataOrDone(e,t)}},{key:"_readUntilEnoughDataOrDone",value:function(r,n){var o=this,e=n<=this._bufferOffset+i(this._buffer);if(this._done||e){var t=this._getDataFromBuffer(r,n),e=null==t&&this._done;return Promise.resolve({value:t,done:e})}return this._reader.read().then(function(e){var t=e.value;return e.done?o._done=!0:void 0===o._buffer?o._buffer=t:o._buffer=function(e,t){if(e.concat)return e.concat(t);if(e instanceof Blob)return new Blob([e,t],{type:e.type});if(e.set){var r=new e.constructor(e.length+t.length);return r.set(e),r.set(t,e.length),r}throw new Error("Unknown data type")}(o._buffer,t),o._readUntilEnoughDataOrDone(r,n)})}},{key:"_getDataFromBuffer",value:function(e,t){e>this._bufferOffset&&(this._buffer=this._buffer.slice(e-this._bufferOffset),this._bufferOffset=e);var r=0===i(this._buffer);return this._done&&r?null:this._buffer.slice(0,t-e)}},{key:"close",value:function(){this._reader.cancel&&this._reader.cancel()}}])&&o(e.prototype,t),n&&o(e,n),r}()},{}],8:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;r.default=function(){return"undefined"!=typeof window&&(void 0!==window.PhoneGap||void 0!==window.Cordova||void 0!==window.cordova)}},{}],9:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){return new Promise(function(t,r){var n=new FileReader;n.onload=function(){var e=new Uint8Array(n.result);t({value:e})},n.onerror=function(e){r(e)},n.readAsArrayBuffer(e)})}},{}],10:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){return new Promise(function(t,r){var n=new XMLHttpRequest;n.responseType="blob",n.onload=function(){var e=n.response;t(e)},n.onerror=function(e){r(e)},n.open("GET",e),n.send()})}},{}],11:[function(e,t,r){"use strict";function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(r,"__esModule",{value:!0}),r.WebStorageUrlStorage=r.canStoreURLs=void 0;var n=!1;try{var n="localStorage"in window,i="tusSupport";localStorage.setItem(i,localStorage.getItem(i))}catch(e){if(e.code!==e.SECURITY_ERR&&e.code!==e.QUOTA_EXCEEDED_ERR)throw e;n=!1}r.canStoreURLs=n,r.WebStorageUrlStorage=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,r,n;return t=e,(r=[{key:"findAllUploads",value:function(){var e=this._findEntries("tus::");return Promise.resolve(e)}},{key:"findUploadsByFingerprint",value:function(e){e=this._findEntries("tus::".concat(e,"::"));return Promise.resolve(e)}},{key:"removeUpload",value:function(e){return localStorage.removeItem(e),Promise.resolve()}},{key:"addUpload",value:function(e,t){var r=Math.round(1e12*Math.random()),r="tus::".concat(e,"::").concat(r);return localStorage.setItem(r,JSON.stringify(t)),Promise.resolve(r)}},{key:"_findEntries",value:function(e){for(var t=[],r=0;r<localStorage.length;r++){var n=localStorage.key(r);if(0===n.indexOf(e))try{var o=JSON.parse(localStorage.getItem(n));o.urlStorageKey=n,t.push(o)}catch(e){}}return t}}])&&o(t.prototype,r),n&&o(t,n),e}()},{}],12:[function(e,t,r){"use strict";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(r){var n=u();return function(){var e,t=s(r);return e=n?(e=s(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments),t=this,!(e=e)||"object"!==o(e)&&"function"!=typeof e?function(e){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(t):e}}function i(e){var n="function"==typeof Map?new Map:void 0;return(i=function(e){if(null===e||(t=e,-1===Function.toString.call(t).indexOf("[native code]")))return e;var t;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(e))return n.get(e);n.set(e,r)}function r(){return a(e,arguments,s(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),l(r,e)})(e)}function a(e,t,r){return(a=u()?Reflect.construct:function(e,t,r){var n=[null];n.push.apply(n,t);n=new(Function.bind.apply(e,n));return r&&l(n,r.prototype),n}).apply(null,arguments)}function u(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function l(e,t){return(l=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,r.default=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&l(e,t)}(s,i(Error));var u=n(s);function s(e){var t,r,n,o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,a=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(t=u.call(this,e)).originalRequest=i,t.originalResponse=a,null!=(t.causingError=o)&&(e+=", caused by ".concat(o.toString())),null!=i&&(r=i.getHeader("X-Request-ID")||"n/a",n=i.getMethod(),o=i.getURL(),i=a?a.getStatus():"n/a",a=a?a.getBody()||"":"n/a",e+=", originated from request (method: ".concat(n,", url: ").concat(o,", response code: ").concat(i,", response text: ").concat(a,", request id: ").concat(r,")")),t.message=e,t}return s}()},{}],13:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.enableDebugLog=function(){n=!0};var n=!(r.log=function(e){n&&console.log(e)})},{}],14:[function(e,t,r){"use strict";function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,r.default=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,r,n;return t=e,(r=[{key:"listAllUploads",value:function(){return Promise.resolve([])}},{key:"findUploadsByFingerprint",value:function(e){return Promise.resolve([])}},{key:"removeUpload",value:function(e){return Promise.resolve()}},{key:"addUpload",value:function(e,t){return Promise.resolve(null)}}])&&o(t.prototype,r),n&&o(t,n),e}()},{}],15:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=e("js-base64"),o=s(e("url-parse")),a=s(e("./error")),i=e("./logger"),u=s(e("./uuid"));function s(e){return e&&e.__esModule?e:{default:e}}function l(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function p(n){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?l(Object(o),!0).forEach(function(e){var t,r;t=n,e=o[r=e],r in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(o)):l(Object(o)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(o,e))})}return n}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var f={endpoint:null,uploadUrl:null,metadata:{},fingerprint:null,uploadSize:null,onProgress:null,onChunkComplete:null,onSuccess:null,onError:null,_onUploadUrlAvailable:null,overridePatchMethod:!1,headers:{},addRequestId:!1,onBeforeRequest:null,onAfterResponse:null,onShouldRetry:null,chunkSize:1/0,retryDelays:[0,1e3,3e3,5e3],parallelUploads:1,storeFingerprintForResuming:!0,removeFingerprintOnSuccess:!1,uploadLengthDeferred:!1,uploadDataDuringCreation:!1,urlStorage:null,fileReader:null,httpStack:null},e=function(){function f(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f),"resume"in t&&console.log("tus: The `resume` option has been removed in tus-js-client v2. Please use the URL storage API instead."),this.options=t,this.options.chunkSize=+this.options.chunkSize,this._urlStorage=this.options.urlStorage,this.file=e,this.url=null,this._req=null,this._fingerprint=null,this._urlStorageKey=null,this._offset=null,this._aborted=!1,this._size=null,this._source=null,this._retryAttempt=0,this._retryTimeout=null,this._offsetBeforeRetry=0,this._parallelUploads=null,this._parallelUploadUrls=null}var e,t,r;return e=f,r=[{key:"terminate",value:function(n,o){if(1<arguments.length&&"function"==typeof arguments[arguments.length-1])throw new Error("tus: the terminate function does not accept a callback since v2 anymore; please use the returned Promise instead");var i=v("DELETE",n,o=void 0===o?{}:o);return y(i,null,o).then(function(e){if(204!==e.getStatus())throw new a.default("tus: unexpected response while terminating upload",null,i,e)}).catch(function(e){if(!g(e=!(e instanceof a.default)?new a.default("tus: failed to terminate upload",e,i,null):e,0,o))throw e;var t=o.retryDelays[0],e=o.retryDelays.slice(1),r=p(p({},o),{},{retryDelays:e});return new Promise(function(e){return setTimeout(e,t)}).then(function(){return f.terminate(n,r)})})}}],(t=[{key:"findPreviousUploads",value:function(){var t=this;return this.options.fingerprint(this.file,this.options).then(function(e){return t._urlStorage.findUploadsByFingerprint(e)})}},{key:"resumeFromPreviousUpload",value:function(e){this.url=e.uploadUrl||null,this._parallelUploadUrls=e.parallelUploadUrls||null,this._urlStorageKey=e.urlStorageKey}},{key:"start",value:function(){var e,t=this,r=this.file;r?this.options.endpoint||this.options.uploadUrl?null==(e=this.options.retryDelays)||"[object Array]"===Object.prototype.toString.call(e)?(1<this.options.parallelUploads&&["uploadUrl","uploadSize","uploadLengthDeferred"].forEach(function(e){t.options[e]&&t._emitError(new Error("tus: cannot use the ".concat(e," option when parallelUploads is enabled")))}),this.options.fingerprint(r,this.options).then(function(e){return null==e?(0,i.log)("No fingerprint was calculated meaning that the upload cannot be stored in the URL storage."):(0,i.log)("Calculated fingerprint: ".concat(e)),t._fingerprint=e,t._source||t.options.fileReader.openFile(r,t.options.chunkSize)}).then(function(e){t._source=e,1<t.options.parallelUploads||null!=t._parallelUploadUrls?t._startParallelUpload():t._startSingleUpload()}).catch(function(e){t._emitError(e)})):this._emitError(new Error("tus: the `retryDelays` option must either be an array or null")):this._emitError(new Error("tus: neither an endpoint or an upload URL is provided")):this._emitError(new Error("tus: no file or stream to upload provided"))}},{key:"_startParallelUpload",value:function(){var u=this,s=this._size=this._source.size,l=0;this._parallelUploads=[];var e=null!=this._parallelUploadUrls?this._parallelUploadUrls.length:this.options.parallelUploads,c=function(e,t,r){for(var n=Math.floor(e/t),o=[],i=0;i<t;i++)o.push({start:n*i,end:n*(i+1)});o[t-1].end=e,r&&o.forEach(function(e,t){e.uploadUrl=r[t]||null});return o}(this._source.size,e,this._parallelUploadUrls);this._parallelUploadUrls=new Array(c.length);var r,e=c.map(function(o,i){var a=0;return u._source.slice(o.start,o.end).then(function(e){var n=e.value;return new Promise(function(e,t){var t=p(p({},u.options),{},{uploadUrl:o.uploadUrl||null,storeFingerprintForResuming:!1,removeFingerprintOnSuccess:!1,parallelUploads:1,metadata:{},headers:p(p({},u.options.headers),{},{"Upload-Concat":"partial"}),onSuccess:e,onError:t,onProgress:function(e){l=l-a+e,a=e,u._emitProgress(l,s)},_onUploadUrlAvailable:function(){u._parallelUploadUrls[i]=r.url,u._parallelUploadUrls.filter(function(e){return!!e}).length===c.length&&u._saveUploadInUrlStorage()}}),r=new f(n,t);r.start(),u._parallelUploads.push(r)})})});Promise.all(e).then(function(){(r=u._openRequest("POST",u.options.endpoint)).setHeader("Upload-Concat","final;".concat(u._parallelUploadUrls.join(" ")));var e=d(u.options.metadata);return""!==e&&r.setHeader("Upload-Metadata",e),u._sendRequest(r,null)}).then(function(e){var t;h(e.getStatus(),200)?null!=(t=e.getHeader("Location"))?(u.url=_(u.options.endpoint,t),(0,i.log)("Created upload at ".concat(u.url)),u._emitSuccess()):u._emitHttpError(r,e,"tus: invalid or missing Location header"):u._emitHttpError(r,e,"tus: unexpected response while creating upload")}).catch(function(e){u._emitError(e)})}},{key:"_startSingleUpload",value:function(){if(this.options.uploadLengthDeferred)this._size=null;else if(null!=this.options.uploadSize){if(this._size=+this.options.uploadSize,isNaN(this._size))return void this._emitError(new Error("tus: cannot convert `uploadSize` option into a number"))}else if(this._size=this._source.size,null==this._size)return void this._emitError(new Error("tus: cannot automatically derive upload's size from input. Specify it manually using the `uploadSize` option or use the `uploadLengthDeferred` option"));return this._aborted=!1,null!=this.url?((0,i.log)("Resuming upload from previous URL: ".concat(this.url)),void this._resumeUpload()):null!=this.options.uploadUrl?((0,i.log)("Resuming upload from provided URL: ".concat(this.options.uploadUrl)),this.url=this.options.uploadUrl,void this._resumeUpload()):((0,i.log)("Creating a new upload"),void this._createUpload())}},{key:"abort",value:function(t){var e=this;if(1<arguments.length&&"function"==typeof arguments[1])throw new Error("tus: the abort function does not accept a callback since v2 anymore; please use the returned Promise instead");return null!=this._parallelUploads&&this._parallelUploads.forEach(function(e){e.abort(t)}),null!==this._req&&(this._req.abort(),this._source.close()),this._aborted=!0,null!=this._retryTimeout&&(clearTimeout(this._retryTimeout),this._retryTimeout=null),t&&null!=this.url?f.terminate(this.url,this.options).then(function(){return e._removeFromUrlStorage()}):Promise.resolve()}},{key:"_emitHttpError",value:function(e,t,r,n){this._emitError(new a.default(r,n,e,t))}},{key:"_emitError",value:function(e){var t=this;if(!this._aborted){if(null!=this.options.retryDelays)if(null!=this._offset&&this._offset>this._offsetBeforeRetry&&(this._retryAttempt=0),g(e,this._retryAttempt,this.options)){var r=this.options.retryDelays[this._retryAttempt++];return this._offsetBeforeRetry=this._offset,void(this._retryTimeout=setTimeout(function(){t.start()},r))}if("function"!=typeof this.options.onError)throw e;this.options.onError(e)}}},{key:"_emitSuccess",value:function(){this.options.removeFingerprintOnSuccess&&this._removeFromUrlStorage(),"function"==typeof this.options.onSuccess&&this.options.onSuccess()}},{key:"_emitProgress",value:function(e,t){"function"==typeof this.options.onProgress&&this.options.onProgress(e,t)}},{key:"_emitChunkComplete",value:function(e,t,r){"function"==typeof this.options.onChunkComplete&&this.options.onChunkComplete(e,t,r)}},{key:"_createUpload",value:function(){var r,e,n=this;this.options.endpoint?(r=this._openRequest("POST",this.options.endpoint),this.options.uploadLengthDeferred?r.setHeader("Upload-Defer-Length",1):r.setHeader("Upload-Length",this._size),""!==(e=d(this.options.metadata))&&r.setHeader("Upload-Metadata",e),(this.options.uploadDataDuringCreation&&!this.options.uploadLengthDeferred?(this._offset=0,this._addChunkToRequest(r)):this._sendRequest(r,null)).then(function(e){if(h(e.getStatus(),200)){var t=e.getHeader("Location");if(null!=t){if(n.url=_(n.options.endpoint,t),(0,i.log)("Created upload at ".concat(n.url)),"function"==typeof n.options._onUploadUrlAvailable&&n.options._onUploadUrlAvailable(),0===n._size)return n._emitSuccess(),void n._source.close();n._saveUploadInUrlStorage(),n.options.uploadDataDuringCreation?n._handleUploadResponse(r,e):(n._offset=0,n._performUpload())}else n._emitHttpError(r,e,"tus: invalid or missing Location header")}else n._emitHttpError(r,e,"tus: unexpected response while creating upload")}).catch(function(e){n._emitHttpError(r,null,"tus: failed to create upload",e)})):this._emitError(new Error("tus: unable to create upload because no endpoint is provided"))}},{key:"_resumeUpload",value:function(){var n=this,o=this._openRequest("HEAD",this.url);this._sendRequest(o,null).then(function(e){var t=e.getStatus();if(!h(t,200))return h(t,400)&&n._removeFromUrlStorage(),423===t?void n._emitHttpError(o,e,"tus: upload is currently locked; retry later"):n.options.endpoint?(n.url=null,void n._createUpload()):void n._emitHttpError(o,e,"tus: unable to resume upload (new upload cannot be created without an endpoint)");var r=parseInt(e.getHeader("Upload-Offset"),10);if(isNaN(r))n._emitHttpError(o,e,"tus: invalid or missing offset value");else{t=parseInt(e.getHeader("Upload-Length"),10);if(!isNaN(t)||n.options.uploadLengthDeferred){if("function"==typeof n.options._onUploadUrlAvailable&&n.options._onUploadUrlAvailable(),r===t)return n._emitProgress(t,t),void n._emitSuccess();n._offset=r,n._performUpload()}else n._emitHttpError(o,e,"tus: invalid or missing length value")}}).catch(function(e){n._emitHttpError(o,null,"tus: failed to resume upload",e)})}},{key:"_performUpload",value:function(){var t,r=this;this._aborted||(this.options.overridePatchMethod?(t=this._openRequest("POST",this.url)).setHeader("X-HTTP-Method-Override","PATCH"):t=this._openRequest("PATCH",this.url),t.setHeader("Upload-Offset",this._offset),this._addChunkToRequest(t).then(function(e){h(e.getStatus(),200)?r._handleUploadResponse(t,e):r._emitHttpError(t,e,"tus: unexpected response while uploading chunk")}).catch(function(e){r._aborted||r._emitHttpError(t,null,"tus: failed to upload chunk at offset ".concat(r._offset),e)}))}},{key:"_addChunkToRequest",value:function(r){var n=this,t=this._offset,e=this._offset+this.options.chunkSize;return r.setProgressHandler(function(e){n._emitProgress(t+e,n._size)}),r.setHeader("Content-Type","application/offset+octet-stream"),(e===1/0||e>this._size)&&!this.options.uploadLengthDeferred&&(e=this._size),this._source.slice(t,e).then(function(e){var t=e.value,e=e.done;return n.options.uploadLengthDeferred&&e&&(n._size=n._offset+(t&&t.size?t.size:0),r.setHeader("Upload-Length",n._size)),null===t?n._sendRequest(r):(n._emitProgress(n._offset,n._size),n._sendRequest(r,t))})}},{key:"_handleUploadResponse",value:function(e,t){var r=parseInt(t.getHeader("Upload-Offset"),10);if(isNaN(r))this._emitHttpError(e,t,"tus: invalid or missing offset value");else{if(this._emitProgress(r,this._size),this._emitChunkComplete(r-this._offset,r,this._size),(this._offset=r)==this._size)return this._emitSuccess(),void this._source.close();this._performUpload()}}},{key:"_openRequest",value:function(e,t){t=v(e,t,this.options);return this._req=t}},{key:"_removeFromUrlStorage",value:function(){var t=this;this._urlStorageKey&&(this._urlStorage.removeUpload(this._urlStorageKey).catch(function(e){t._emitError(e)}),this._urlStorageKey=null)}},{key:"_saveUploadInUrlStorage",value:function(){var e,t=this;this.options.storeFingerprintForResuming&&this._fingerprint&&(e={size:this._size,metadata:this.options.metadata,creationTime:(new Date).toString()},this._parallelUploads?e.parallelUploadUrls=this._parallelUploadUrls:e.uploadUrl=this.url,this._urlStorage.addUpload(this._fingerprint,e).then(function(e){return t._urlStorageKey=e}).catch(function(e){t._emitError(e)}))}},{key:"_sendRequest",value:function(e){return y(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,this.options)}}])&&c(e.prototype,t),r&&c(e,r),f}();function d(e){var t,r=[];for(t in e)r.push("".concat(t," ").concat(n.Base64.encode(e[t])));return r.join(",")}function h(e,t){return t<=e&&e<t+100}function v(e,t,r){var n=r.httpStack.createRequest(e,t);n.setHeader("Tus-Resumable","1.0.0");var o,i=r.headers||{};for(o in i)n.setHeader(o,i[o]);return r.addRequestId&&(r=(0,u.default)(),n.setHeader("X-Request-ID",r)),n}function y(t,e,r){return("function"==typeof r.onBeforeRequest?Promise.resolve(r.onBeforeRequest(t)):Promise.resolve()).then(function(){return t.send(e).then(function(e){return("function"==typeof r.onAfterResponse?Promise.resolve(r.onAfterResponse(t,e)):Promise.resolve()).then(function(){return e})})})}function g(e,t,r){if(!(null==r.retryDelays||t>=r.retryDelays.length||null==e.originalRequest)){if(r&&"function"==typeof r.onShouldRetry)return r.onShouldRetry(e,t,r);e=e.originalResponse?e.originalResponse.getStatus():0;return(!h(e,400)||409===e||423===e)&&(e=!0,e=!("undefined"!=typeof window&&"navigator"in window&&!1===window.navigator.onLine)&&e)}}function _(e,t){return new o.default(t,e).toString()}e.defaultOptions=f,r.default=e},{"./error":12,"./logger":13,"./uuid":16,"js-base64":17,"url-parse":20}],16:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})}},{}],17:[function(e,O,n){!function(r){!function(){var e,t;e="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==r?r:this,t=function(t){"use strict";function r(e){if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?f(192|t>>>6)+f(128|63&t):f(224|t>>>12&15)+f(128|t>>>6&63)+f(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return f(240|t>>>18&7)+f(128|t>>>12&63)+f(128|t>>>6&63)+f(128|63&t)}function n(e){return e.replace(p,r)}function o(e){var t=[0,2,1][e.length%3],e=e.charCodeAt(0)<<16|(1<e.length?e.charCodeAt(1):0)<<8|(2<e.length?e.charCodeAt(2):0);return[l.charAt(e>>>18),l.charAt(e>>>12&63),2<=t?"=":l.charAt(e>>>6&63),1<=t?"=":l.charAt(63&e)].join("")}function i(e){return d(n(String(e)))}function s(e){return e.replace(/[+\/]/g,function(e){return"+"==e?"-":"_"}).replace(/=/g,"")}function a(e,t){return t?s(i(e)):i(e)}var e,u=(t=t||{}).Base64,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c=function(e){for(var t={},r=0,n=e.length;r<n;r++)t[e.charAt(r)]=r;return t}(l),f=String.fromCharCode,p=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,d=t.btoa&&"function"==typeof t.btoa?function(e){return t.btoa(e)}:function(e){if(e.match(/[^\x00-\xFF]/))throw new RangeError("The string contains invalid characters.");return e.replace(/[\s\S]{1,3}/g,o)};t.Uint8Array&&(e=function(e,t){for(var r="",n=0,o=e.length;n<o;n+=3){var i=e[n],a=e[n+1],u=e[n+2],i=i<<16|a<<8|u;r+=l.charAt(i>>>18)+l.charAt(i>>>12&63)+(void 0!==a?l.charAt(i>>>6&63):"=")+(void 0!==u?l.charAt(63&i):"=")}return t?s(r):r});function h(e){switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return f(55296+(t>>>10))+f(56320+(1023&t));case 3:return f((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return f((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}}function v(e){return e.replace(w,h)}function y(e){var t=e.length,r=t%4,e=(0<t?c[e.charAt(0)]<<18:0)|(1<t?c[e.charAt(1)]<<12:0)|(2<t?c[e.charAt(2)]<<6:0)|(3<t?c[e.charAt(3)]:0);return(e=[f(e>>>16),f(e>>>8&255),f(255&e)]).length-=[0,0,2,1][r],e.join("")}function g(e){return U(String(e).replace(/[^A-Za-z0-9\+\/]/g,""))}function _(e){return String(e).replace(/[-_]/g,function(e){return"-"==e?"+":"/"}).replace(/[^A-Za-z0-9\+\/]/g,"")}function m(e){return e=_(e),v(U(e))}var b,w=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,U=t.atob&&"function"==typeof t.atob?function(e){return t.atob(e)}:function(e){return e.replace(/\S{1,4}/g,y)};t.Uint8Array&&(b=function(e){return Uint8Array.from(g(_(e)),function(e){return e.charCodeAt(0)})});var S;return t.Base64={VERSION:"2.6.4",atob:g,btoa:d,fromBase64:m,toBase64:a,utob:n,encode:a,encodeURI:function(e){return a(e,!0)},btou:v,decode:m,noConflict:function(){var e=t.Base64;return t.Base64=u,e},fromUint8Array:e,toUint8Array:b},"function"==typeof Object.defineProperty&&(S=function(e){return{value:e,enumerable:!1,writable:!0,configurable:!0}},t.Base64.extendString=function(){Object.defineProperty(String.prototype,"fromBase64",S(function(){return m(this)})),Object.defineProperty(String.prototype,"toBase64",S(function(e){return a(this,e)})),Object.defineProperty(String.prototype,"toBase64URI",S(function(){return a(this,!0)}))}),t.Meteor&&(Base64=t.Base64),void 0!==O&&O.exports&&(O.exports.Base64=t.Base64),{Base64:t.Base64}},"object"==typeof n&&void 0!==O?O.exports=t(e):t(e)}.call(this)}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],18:[function(e,t,r){"use strict";var i=Object.prototype.hasOwnProperty;function a(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(e){return null}}function u(e){try{return encodeURIComponent(e)}catch(e){return null}}r.stringify=function(e,t){var r,n,o=[];for(n in"string"!=typeof(t=t||"")&&(t="?"),e)i.call(e,n)&&((r=e[n])||null!=r&&!isNaN(r)||(r=""),n=u(n),r=u(r),null!==n&&null!==r&&o.push(n+"="+r));return o.length?t+o.join("&"):""},r.parse=function(e){for(var t=/([^=?#&]+)=?([^&]*)/g,r={};o=t.exec(e);){var n=a(o[1]),o=a(o[2]);null===n||null===o||n in r||(r[n]=o)}return r}},{}],19:[function(e,t,r){"use strict";t.exports=function(e,t){if(t=t.split(":")[0],!(e=+e))return!1;switch(t){case"http":case"ws":return 80!==e;case"https":case"wss":return 443!==e;case"ftp":return 21!==e;case"gopher":return 70!==e;case"file":return!1}return 0!==e}},{}],20:[function(e,r,t){!function(a){!function(){"use strict";var p=e("requires-port"),d=e("querystringify"),h=/[\n\r\t]/g,o=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,u=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,v=/^[a-zA-Z]:/,t=/^[ \f\n\r\t\v\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/;function y(e){return(e||"").toString().replace(t,"")}var g=[["#","hash"],["?","query"],function(e,t){return m(t.protocol)?e.replace(/\\/g,"/"):e},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d+)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],i={hash:1,query:1};function _(e){var t,r="undefined"!=typeof window?window:void 0!==a?a:"undefined"!=typeof self?self:{},r=r.location||{},n={},r=typeof(e=e||r);if("blob:"===e.protocol)n=new w(unescape(e.pathname),{});else if("string"==r)for(t in n=new w(e,{}),i)delete n[t];else if("object"==r){for(t in e)t in i||(n[t]=e[t]);void 0===n.slashes&&(n.slashes=o.test(e.href))}return n}function m(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function b(e,t){e=(e=y(e)).replace(h,""),t=t||{};var r,n=u.exec(e),o=n[1]?n[1].toLowerCase():"",i=!!n[2],a=!!n[3],e=0;return i?e=a?(r=n[2]+n[3]+n[4],n[2].length+n[3].length):(r=n[2]+n[4],n[2].length):a?(r=n[3]+n[4],e=n[3].length):r=n[4],"file:"===o?2<=e&&(r=r.slice(2)):m(o)?r=n[4]:o?i&&(r=r.slice(2)):2<=e&&m(t.protocol)&&(r=n[4]),{protocol:o,slashes:i||m(o),slashesCount:e,rest:r}}function w(e,t,r){if(e=(e=y(e)).replace(h,""),!(this instanceof w))return new w(e,t,r);var n,o,i,a,u,s=g.slice(),l=typeof t,c=this,f=0;for("object"!=l&&"string"!=l&&(r=t,t=null),r&&"function"!=typeof r&&(r=d.parse),n=!(l=b(e||"",t=_(t))).protocol&&!l.slashes,c.slashes=l.slashes||n&&t.slashes,c.protocol=l.protocol||t.protocol||"",e=l.rest,("file:"===l.protocol&&(2!==l.slashesCount||v.test(e))||!l.slashes&&(l.protocol||l.slashesCount<2||!m(c.protocol)))&&(s[3]=[/(.*)/,"pathname"]);f<s.length;f++)"function"!=typeof(i=s[f])?(o=i[0],u=i[1],o!=o?c[u]=e:"string"==typeof o?~(a="@"===o?e.lastIndexOf(o):e.indexOf(o))&&(e="number"==typeof i[2]?(c[u]=e.slice(0,a),e.slice(a+i[2])):(c[u]=e.slice(a),e.slice(0,a))):(a=o.exec(e))&&(c[u]=a[1],e=e.slice(0,a.index)),c[u]=c[u]||n&&i[3]&&t[u]||"",i[4]&&(c[u]=c[u].toLowerCase())):e=i(e,c);r&&(c.query=r(c.query)),n&&t.slashes&&"/"!==c.pathname.charAt(0)&&(""!==c.pathname||""!==t.pathname)&&(c.pathname=function(e,t){if(""===e)return t;for(var r=(t||"/").split("/").slice(0,-1).concat(e.split("/")),n=r.length,e=r[n-1],o=!1,i=0;n--;)"."===r[n]?r.splice(n,1):".."===r[n]?(r.splice(n,1),i++):i&&(0===n&&(o=!0),r.splice(n,1),i--);return o&&r.unshift(""),"."!==e&&".."!==e||r.push(""),r.join("/")}(c.pathname,t.pathname)),"/"!==c.pathname.charAt(0)&&m(c.protocol)&&(c.pathname="/"+c.pathname),p(c.port,c.protocol)||(c.host=c.hostname,c.port=""),c.username=c.password="",c.auth&&(~(a=c.auth.indexOf(":"))?(c.username=c.auth.slice(0,a),c.username=encodeURIComponent(decodeURIComponent(c.username)),c.password=c.auth.slice(a+1),c.password=encodeURIComponent(decodeURIComponent(c.password))):c.username=encodeURIComponent(decodeURIComponent(c.auth)),c.auth=c.password?c.username+":"+c.password:c.username),c.origin="file:"!==c.protocol&&m(c.protocol)&&c.host?c.protocol+"//"+c.host:"null",c.href=c.toString()}w.prototype={set:function(e,t,r){var n=this;switch(e){case"query":"string"==typeof t&&t.length&&(t=(r||d.parse)(t)),n[e]=t;break;case"port":n[e]=t,p(t,n.protocol)?t&&(n.host=n.hostname+":"+t):(n.host=n.hostname,n[e]="");break;case"hostname":n[e]=t,n.port&&(t+=":"+n.port),n.host=t;break;case"host":n[e]=t,/:\d+$/.test(t)?(t=t.split(":"),n.port=t.pop(),n.hostname=t.join(":")):(n.hostname=t,n.port="");break;case"protocol":n.protocol=t.toLowerCase(),n.slashes=!r;break;case"pathname":case"hash":t?(o="pathname"===e?"/":"#",n[e]=t.charAt(0)!==o?o+t:t):n[e]=t;break;case"username":case"password":n[e]=encodeURIComponent(t);break;case"auth":var o=t.indexOf(":");~o?(n.username=t.slice(0,o),n.username=encodeURIComponent(decodeURIComponent(n.username)),n.password=t.slice(o+1),n.password=encodeURIComponent(decodeURIComponent(n.password))):n.username=encodeURIComponent(decodeURIComponent(t))}for(var i=0;i<g.length;i++){var a=g[i];a[4]&&(n[a[1]]=n[a[1]].toLowerCase())}return n.auth=n.password?n.username+":"+n.password:n.username,n.origin="file:"!==n.protocol&&m(n.protocol)&&n.host?n.protocol+"//"+n.host:"null",n.href=n.toString(),n},toString:function(e){e&&"function"==typeof e||(e=d.stringify);var t=this,r=t.protocol;return r&&":"!==r.charAt(r.length-1)&&(r+=":"),r+=t.protocol&&t.slashes||m(t.protocol)?"//":"",t.username?(r+=t.username,t.password&&(r+=":"+t.password),r+="@"):t.password?(r+=":"+t.password,r+="@"):"file:"!==t.protocol&&m(t.protocol)&&!t.host&&"/"!==t.pathname&&(r+="@"),r+=t.host+t.pathname,(e="object"==typeof t.query?e(t.query):t.query)&&(r+="?"!==e.charAt(0)?"?"+e:e),t.hash&&(r+=t.hash),r}},w.extractProtocol=b,w.location=_,w.trimLeft=y,w.qs=d,r.exports=w}.call(this)}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{querystringify:18,"requires-port":19}]},{},[4])(4)});
//# sourceMappingURL=tus.min.js.map