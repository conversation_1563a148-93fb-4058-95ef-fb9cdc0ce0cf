const YUNOCarouselList = (function($) {
    
    const carouselList = function() {
        Vue.component('yuno-carousel-list', {
            props: ["data"],
            template: `
                <section id="yunoCarouselList" class="yunoCarouselList">
                    <div class="container">
                        <h2 class="sectionTitle">{{data.title}}</h2>
                        <div class="carouselListControls" id="carouselListControls">
                            <button type="button" data-controls="prev" tabindex="-1" aria-controls="tns1"><i class="fa fa-long-arrow-left" aria-hidden="true"></i></button>
                            <button type="button" data-controls="next" tabindex="-1" aria-controls="tns1"><i class="fa fa-long-arrow-right" aria-hidden="true"></i></button>
                        </div>
                        <div id="carouselList" class="carouselList">
                            <div 
                                class="carouselCard"
                                v-for="(item, carouselIndex) in data.data"
                                :key="carouselIndex">
                                <div class="innerWrap">
                                    <figure class="cardImg">
                                        <img width="253" height="140" :src="item.MediaURL + imgParameter" :alt="item.ProductTitle">
                                    </figure>
                                    <div class="cardBody">
                                        <h3 class="cardTitle"><span v-html="item.ProductTitle"></span></h3>
                                        <p class="cardPrice">&#8377; {{item.UnitPrice}}</p>
                                    </div>
                                    <div class="cardFooter">
                                        <a :href="item.CourseLink">View Details</a>
                                    </div>
                                </div>  
                            </div>
                        </div>
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                imgParameter() {
                    return YUNOCommon.config.addVerion(true)
                }
            },
            mounted() {
                let slider = tns({
                    container: "#carouselList",
                    controlsContainer: "#carouselListControls",
                    loop: false,
                    responsive: {
                        500: {
                            items: 1
                        },
                        768: {
                            items: 2
                        },
                        992: {
                            items: 3
                        },
                        1200: {
                            items: 4
                        }
                    },
                    swipeAngle: false,
                    speed: 400,
                    nav: false,
                    mouseDrag: true,
                    nonce: yunoNonce
                });
            }
        });
    };

    return {
        carouselList: carouselList
    };
})(jQuery);

