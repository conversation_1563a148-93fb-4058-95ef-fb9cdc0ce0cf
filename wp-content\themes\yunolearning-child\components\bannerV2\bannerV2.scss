@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.ylIcon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'yuno-icon' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    
    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
    font-family: 'Material Icons Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
  
  .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

.dark87 {
    @include setFontColor($primaryCopyColor, 0.87);
}

.dark60 {
    @include setFontColor($primaryCopyColor, 0.6);
}

#app {
    .bannerV2 {
        padding: $gapLargest 0;
        @extend .dark87;
        background-color: $tertiary;
        
        @media (min-width: 768px) {
            padding: 0 0;
        }

        .container {
            padding: $gapLargest;
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center center;
            height: 400px;

            .row {
                height: 100%;
            }
        }

        .wrapper {
            color: $onSurface;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;

            h2 {
                @include setFont($headline2, 52px, 500, $gapSmall);
            }

            small {
                @include setFont($headline5, 28px, 700, 0);
            }

            .yunoSecondaryCTA {
                height: 56px;
                line-height: 40px;
                padding-left: $gapLargest;
                padding-right: $gapLargest;
                border-radius: 4px;
                font-size: $headline6;

                
            }

            .secondaryCTA {
                text-align: center;
                margin-top: $gap15;

                .smallerBody {
                    color: $onSurfaceVariant;
                    margin-bottom: $gapSmall
                }

                .plainLink {
                    text-decoration: underline
                }
            }
        }
    }
}