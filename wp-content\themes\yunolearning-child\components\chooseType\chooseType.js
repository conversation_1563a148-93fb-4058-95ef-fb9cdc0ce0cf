const YUNOChooseType = (function($) {
    
    const chooseType = function() {
        Vue.component('yuno-choose-type', {
            props: ["data"],
            template: `
                <section id="yunoChooseType" class="yunoChooseType">
                    <div class="container">
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <h1 class="sectionTitle">{{data.title}}</h1>
                                <h2 class="sectionSubtitle">{{data.subTitle}}</h2>
                            </div>
                        </div>
                        <ul class="optionsList">
                            <li 
                                v-for="(option, optionIndex) in data.data"
                                :key="optionIndex">
                                <template v-if="option.length">
                                    <div class="innerWrapper" :class="cardClass(option[0].segment)">
                                        <div class="listHeader">
                                            <h3 class="listTitle">
                                                <span v-html="option[0].segment"></span>
                                                <small class="helper">{{option[0].AgeLimit}}</small>
                                            </h3>
                                        </div>
                                        <div class="listBody">
                                            <p>{{option[0].ShortDescription}}</p>
                                        </div>
                                        <ul class="listFooter">
                                            <li
                                                v-for="(price, priceIndex) in option"
                                                :key="priceIndex">
                                                <h4 class="price">
                                                    <template v-if="price.UnitPrice === 0 && price.Subscription.length !== 0">
                                                        &#8377;{{option[0].PerClassPrice}}/class    
                                                    </template>
                                                    <template v-else>
                                                        &#8377;{{option[0].PerClassPrice}}/class
                                                    </template>
                                                </h4>
                                                <b-button tag="a"
                                                    :href="price.detailURL"
                                                    class="yunoPrimaryCTA wired small">
                                                    {{price.level}}
                                                </b-button>
                                            </li>
                                        </ul>
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="innerWrapper" :class="cardClass(option.segment)">
                                        <div class="listHeader">
                                            <h3 class="listTitle">
                                                {{option.segment}}
                                                <small class="helper">{{option.AgeLimit}}</small>
                                            </h3>
                                        </div>
                                        <div class="listBody">
                                            <p>{{option.ShortDescription}}</p>
                                        </div>
                                        <ul class="listFooter">
                                            <li>
                                                <h4 class="price">
                                                    &#8377;{{option.PerClassPrice}}/class
                                                </h4>
                                                <b-button tag="a"
                                                    :href="option.detailURL"
                                                    class="yunoPrimaryCTA wired small">
                                                    {{option.level}}
                                                </b-button>
                                            </li>
                                        </ul>
                                    </div>
                                </template>
                            </li>
                        </ul>
                    </div>
                </section>
            `,
            methods: {
                cardClass(className) {
                    return className.replace(/ /g,"-").toLowerCase();
                }
            }
        });
    };

    return {
        chooseType: chooseType
    };
})(jQuery);

