@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";
@import "../../assets/scss/icons";

#app {
	.yunoChooseType {
		padding: $gapLargest 0;
		@include setBGColor($lightBlue, 0.1);

		@media (min-width: 768px) {
			padding: $gapLargest * 2 0 $gapLargest;
		}

		.sectionTitle {
			font-size: $fontSizeLargest;
			color: $primaryColor;
			font-weight: 700;
			text-align: center;
			margin: 0;

			@media (min-width: 768px) {
				font-size: $fontSizeLargest + 16;
			}
		}

		.sectionSubtitle {
			font-size: $fontSizeLarger;
			font-weight: 400;
			text-align: center;
			margin: $gapLarge 0 0 0;
			line-height: $fontSizeLarger + 6;
		}

		.optionsList {
			display: flex;
			flex-wrap: wrap;
			margin: $gapLargest 0 0;

			@media (min-width: 768px) {
				margin: $gapLargest -6px 0;
			}

			> li {
				margin-bottom: $gapLarge;
				flex: 0 0 100%;

				@media (min-width: 768px) {
					flex: 0 0 50%;
					margin: 0;
					padding: 0 $gap15 $gapLargest;
				}

				@media (min-width: 992px) {
					flex: 0 0 25%;
					margin: 0;
					padding: 0 6px $gapLargest;
				}

				&:last-child {
					margin-bottom: 0;
				}

				.innerWrapper {
					background: $whiteBG;
					padding: $gapLarge;
					height: 100%;
					box-shadow: rgba(0,0,0,.117647) 0 0 15px;
					border-radius: 4px;
					overflow: hidden;
					position: relative;

					&:before {
						content: "";
						height: 3px;
						width: 100%;
						position: absolute;
						top: 0;
						left: 0;
					}

					.listHeader {
						display: flex;
						justify-content: space-between;
						border-bottom: 1px solid $grey;
						padding-bottom: 38px;
						margin-bottom: $gapLarge;
						min-height: 92px;
						position: relative;

						&:after {
							@extend .ylIcon;
							font-size: $fontSizeLargest + 8;
							margin-left: $gapSmall;
							color: $primaryColor
						}	
						.listTitle {
							font-size: $fontSizeLarger + 2;
							color: $primaryColor;
							font-weight: 500;
							margin: 0;

							.helper {
								display: block;
								font-size: $fontSizeSmall;
								@include setFontColor($primaryCopyColor, 0.7);
								font-weight: 400;
								padding-top: $gapSmaller;
								position: absolute;
								left: 0;
								bottom: $gap15;
							}
						}
					}

					&.working-professionals {
						&:before {
							background: $primaryColor;
						}

						.listHeader {
							&:after {
								content: "professionals";
							}	
						}	
					}

					&.students, &.teenagers {
						&:before {
							background: #FBBD00;
						}

						.listHeader {
							&:after {
								content: "students";
							}	
						}	
					}

					&.kids, &.preteens {
						&:before {
							background: #31AA52;
						}

						.listHeader {
							&:after {
								content: "kids";
							}	
						}	
					}

					&.homemakers {
						&:before {
							background: #EB4132;
						}

						.listHeader {
							&:after {
								content: "homemakers";
							}	
						}	
					}

					.listBody {
						margin-bottom: $gapLarge;
						

						@media (min-width: 768px) {
							min-height: 168px;
						}

						p {
							font-size: $fontSizeSmall;
							margin: 0;
							@include setFontColor($primaryCopyColor, 0.7);
						}
					}

					.listFooter {
						li {
							border-top: 1px solid $grey;
							padding: $gapLarge 0 $gapLarge;

							.price {
								font-size: $fontSizeLarger;
								font-weight: 500;
								margin-bottom: $gapLarge;

								small {
									margin-top: $gapSmaller;
									display: block;
									font-size: $fontSizeSmaller;
									@include setFontColor($primaryCopyColor, 0.7);
								}
							}
							.button {
								border-color: $primaryColor;
								transition: all 0.25s ease;

								&:hover {
									background: $primaryColor;
									color: $secondaryCopyColor;
									border-color: $primaryColor;
								}
							}
						}
					}
				}
			}
		}
	}
}