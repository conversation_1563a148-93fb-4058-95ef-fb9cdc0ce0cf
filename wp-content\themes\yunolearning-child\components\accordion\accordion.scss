@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.ylIcon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'yuno-icon' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    
    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
    font-family: 'Material Icons Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
  
  .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

#app {
    .dark87 {
        @include setFontColor($primaryCopyColor, 0.87);
    }

    .dark60 {
        @include setFontColor($primaryCopyColor, 0.6);
    }
    
    .yunoAccordion {
        padding: $gapLargest 0;

        @media (min-width: 768px) {
            padding: $gapLargest * 2 0 $gapLargest;
        }

        .sectionTitle {
            font-size: $headline3;
            line-height: 40px;
            @extend .dark87;
            text-align: center;
            margin-bottom: $gap15;
        }

        &.faq {
            .wrapper {
                @include setBGColor($primaryCopyColor, 0.02);
                padding: $gapLargest;
            }

            .collapse:not(.show) {
                display: block;
            }

            .faqCard {
                background: none;
                box-shadow: none;
                max-width: 100%;
                position: relative;
                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                margin-bottom: $gapSmall;
                padding: $gapSmall 0;
    
                &:last-child {
                    margin-bottom: 0;
                }
    
                &.collapse .collapse-trigger {
                    display: inline;
                    cursor: pointer;
    
                    .collapse-content {
                        display: inherit;
                    }
                }
    
                .card-header {
                    background-color: transparent;
                    align-items: stretch;
                    box-shadow: none;
                    display: flex;
                    border: 0;
                    padding: 0;
    
                    .card-header-title {
                        align-items: center;
                        display: flex;
                        padding: 0;
                        font-size: $subtitle1;
                        flex-grow: 1;
                        font-weight: 500;
                        @extend .dark87;
                        line-height: 24px;
                        margin: 0 0 $gapSmaller;
                    }
    
                    .card-header-icon {
                        align-items: center;
                        cursor: pointer;
                        display: flex;
                        padding: 0;
                        @extend .dark60;
    
                        .icon {
                            .mdi {
                                &:before { 
                                    content: "\e145";
                                    @extend .material-icons-outlined;
                                }
    
                                &.mdi-menu-down {
                                    &:before { 
                                        content: "\e15b";
                                    }						    	
                                }
                            }
                        }
                    }

                    &.active {
                        .card-header-title {
                            color: $primary;
                        }

                        .card-header-icon {
                            color: $primary;
                        }
                    }
                }
    
                .card-content, .card-footer {
                    background-color: transparent;
                }
    
                .card-content {
                    padding: 0;
                    margin: 0;
                    border-radius: 0;
                    font-size: $body2;
                    background: none;
                    line-height: 20px;
                    @extend .dark60;

                    .alphaList {
                        margin: 0 0 0 16px;
                        li {
                            list-style: lower-alpha outside;
                            margin-bottom: $gapSmaller;

                            &:last-child {
                                margin-bottom: 0;
                            }
                        }
                    }
                }
    
                .slide-enter-active, .slide-leave-active {
                    transition: .15s ease-out;
                }
    
                .slide-enter-to, .slide-leave {
                    max-height: 100px;
                    overflow: hidden;
                }
    
                .slide-enter, .slide-leave-to {
                    overflow: hidden;
                    max-height: 0;
                }
            }
        }
    }
}

