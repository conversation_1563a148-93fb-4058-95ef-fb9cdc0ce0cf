/*! @sentry/tracing & @sentry/browser 6.10.0 (1713dd9) | https://github.com/getsentry/sentry-javascript */
var Sentry=function(t){var n=function(t,i){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var i in n)n.hasOwnProperty(i)&&(t[i]=n[i])})(t,i)};function i(t,i){function r(){this.constructor=t}n(t,i),t.prototype=null===i?Object.create(i):(r.prototype=i.prototype,new r)}var r,e,o,u,s,a,c=function(){return(c=Object.assign||function(t){for(var n,i=1,r=arguments.length;i<r;i++)for(var e in n=arguments[i])Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e]);return t}).apply(this,arguments)};function f(t,n){var i={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.indexOf(r)<0&&(i[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var e=0;for(r=Object.getOwnPropertySymbols(t);e<r.length;e++)n.indexOf(r[e])<0&&(i[r[e]]=t[r[e]])}return i}function h(t){var n="function"==typeof Symbol&&t[Symbol.iterator],i=0;return n?n.call(t):{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}}}function v(t,n){var i="function"==typeof Symbol&&t[Symbol.iterator];if(!i)return t;var r,e,o=i.call(t),u=[];try{for(;(void 0===n||n-- >0)&&!(r=o.next()).done;)u.push(r.value)}catch(t){e={error:t}}finally{try{r&&!r.done&&(i=o.return)&&i.call(o)}finally{if(e)throw e.error}}return u}function d(){for(var t=[],n=0;n<arguments.length;n++)t=t.concat(v(arguments[n]));return t}function l(t){switch(Object.prototype.toString.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return S(t,Error)}}function p(t){return"[object ErrorEvent]"===Object.prototype.toString.call(t)}function m(t){return"[object DOMError]"===Object.prototype.toString.call(t)}function y(t){return"[object String]"===Object.prototype.toString.call(t)}function g(t){return null===t||"object"!=typeof t&&"function"!=typeof t}function b(t){return"[object Object]"===Object.prototype.toString.call(t)}function w(t){return"undefined"!=typeof Event&&S(t,Event)}function T(t){return"undefined"!=typeof Element&&S(t,Element)}function E(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function S(t,n){try{return t instanceof n}catch(t){return!1}}function x(t,n){try{for(var i=t,r=[],e=0,o=0,u=" > ".length,s=void 0;i&&e++<5&&!("html"===(s=_(i,n))||e>1&&o+r.length*u+s.length>=80);)r.push(s),o+=s.length,i=i.parentNode;return r.reverse().join(" > ")}catch(t){return"<unknown>"}}function _(t,n){var i,r,e,o,u,s,a,c=t,f=[];if(!c||!c.tagName)return"";f.push(c.tagName.toLowerCase());var h=(null===(i=n)||void 0===i?void 0:i.length)?n.filter(function(t){return c.getAttribute(t)}).map(function(t){return[t,c.getAttribute(t)]}):null;if(null===(r=h)||void 0===r?void 0:r.length)h.forEach(function(t){f.push("["+t[0]+'="'+t[1]+'"]')});else if(c.id&&f.push("#"+c.id),(e=c.className)&&y(e))for(o=e.split(/\s+/),a=0;a<o.length;a++)f.push("."+o[a]);var v=["type","name","title","alt"];for(a=0;a<v.length;a++)u=v[a],(s=c.getAttribute(u))&&f.push("["+u+'="'+s+'"]');return f.join("")}!function(t){t[t.None=0]="None",t[t.Error=1]="Error",t[t.Debug=2]="Debug",t[t.Verbose=3]="Verbose"}(r||(r={})),function(t){t.Ok="ok",t.Exited="exited",t.Crashed="crashed",t.Abnormal="abnormal"}(e||(e={})),function(t){t.Ok="ok",t.Errored="errored",t.Crashed="crashed"}(o||(o={})),(u=t.Severity||(t.Severity={})).Fatal="fatal",u.Error="error",u.Warning="warning",u.Log="log",u.Info="info",u.Debug="debug",u.Critical="critical",function(t){t.fromString=function(n){switch(n){case"debug":return t.Debug;case"info":return t.Info;case"warn":case"warning":return t.Warning;case"error":return t.Error;case"fatal":return t.Fatal;case"critical":return t.Critical;case"log":default:return t.Log}}}(t.Severity||(t.Severity={})),(s=t.Status||(t.Status={})).Unknown="unknown",s.Skipped="skipped",s.Success="success",s.RateLimit="rate_limit",s.Invalid="invalid",s.Failed="failed",function(t){t.fromHttpCode=function(n){return n>=200&&n<300?t.Success:429===n?t.RateLimit:n>=400&&n<500?t.Invalid:n>=500?t.Failed:t.Unknown}}(t.Status||(t.Status={})),function(t){t.Explicit="explicitly_set",t.Sampler="client_sampler",t.Rate="client_rate",t.Inheritance="inheritance"}(a||(a={}));var j=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,n){return t.__proto__=n,t}:function(t,n){for(var i in n)t.hasOwnProperty(i)||(t[i]=n[i]);return t});var k=function(t){function n(n){var i=this.constructor,r=t.call(this,n)||this;return r.message=n,r.name=i.prototype.constructor.name,j(r,i.prototype),r}return i(n,t),n}(Error),O=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+))?@)([\w.-]+)(?::(\d+))?\/(.+)/,D=function(){function t(t){"string"==typeof t?this.t(t):this.i(t),this.o()}return t.prototype.toString=function(t){void 0===t&&(t=!1);var n=this,i=n.host,r=n.path,e=n.pass,o=n.port,u=n.projectId;return n.protocol+"://"+n.publicKey+(t&&e?":"+e:"")+"@"+i+(o?":"+o:"")+"/"+(r?r+"/":r)+u},t.prototype.t=function(t){var n=O.exec(t);if(!n)throw new k("Invalid Dsn");var i=v(n.slice(1),6),r=i[0],e=i[1],o=i[2],u=void 0===o?"":o,s=i[3],a=i[4],c=void 0===a?"":a,f="",h=i[5],d=h.split("/");if(d.length>1&&(f=d.slice(0,-1).join("/"),h=d.pop()),h){var l=h.match(/^\d+/);l&&(h=l[0])}this.i({host:s,pass:u,path:f,projectId:h,port:c,protocol:r,publicKey:e})},t.prototype.i=function(t){"user"in t&&!("publicKey"in t)&&(t.publicKey=t.user),this.user=t.publicKey||"",this.protocol=t.protocol,this.publicKey=t.publicKey||"",this.pass=t.pass||"",this.host=t.host,this.port=t.port||"",this.path=t.path||"",this.projectId=t.projectId},t.prototype.o=function(){var t=this;if(["protocol","publicKey","host","projectId"].forEach(function(n){if(!t[n])throw new k("Invalid Dsn: "+n+" missing")}),!this.projectId.match(/^\d+$/))throw new k("Invalid Dsn: Invalid projectId "+this.projectId);if("http"!==this.protocol&&"https"!==this.protocol)throw new k("Invalid Dsn: Invalid protocol "+this.protocol);if(this.port&&isNaN(parseInt(this.port,10)))throw new k("Invalid Dsn: Invalid port "+this.port)},t}();function I(){return"[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0)}function N(t,n){return t.require(n)}function R(t,n){return void 0===n&&(n=0),"string"!=typeof t||0===n?t:t.length<=n?t:t.substr(0,n)+"..."}function C(t,n){if(!Array.isArray(t))return"";for(var i=[],r=0;r<t.length;r++){var e=t[r];try{i.push(String(e))}catch(t){i.push("[value cannot be serialized]")}}return i.join(n)}function M(t,n){return!!y(t)&&(i=n,"[object RegExp]"===Object.prototype.toString.call(i)?n.test(t):"string"==typeof n&&-1!==t.indexOf(n));var i}var A={};function L(){return I()?global:"undefined"!=typeof window?window:"undefined"!=typeof self?self:A}function q(){var t=L(),n=t.crypto||t.msCrypto;if(void 0!==n&&n.getRandomValues){var i=new Uint16Array(8);n.getRandomValues(i),i[3]=4095&i[3]|16384,i[4]=16383&i[4]|32768;var r=function(t){for(var n=t.toString(16);n.length<4;)n="0"+n;return n};return r(i[0])+r(i[1])+r(i[2])+r(i[3])+r(i[4])+r(i[5])+r(i[6])+r(i[7])}return"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,function(t){var n=16*Math.random()|0;return("x"===t?n:3&n|8).toString(16)})}function F(t){if(!t)return{};var n=t.match(/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!n)return{};var i=n[6]||"",r=n[8]||"";return{host:n[4],path:n[5],protocol:n[2],relative:n[5]+i+r}}function P(t){if(t.message)return t.message;if(t.exception&&t.exception.values&&t.exception.values[0]){var n=t.exception.values[0];return n.type&&n.value?n.type+": "+n.value:n.type||n.value||t.event_id||"<unknown>"}return t.event_id||"<unknown>"}function U(t){var n=L();if(!("console"in n))return t();var i=n.console,r={};["debug","info","warn","error","log","assert"].forEach(function(t){t in n.console&&i[t].__sentry_original__&&(r[t]=i[t],i[t]=i[t].__sentry_original__)});var e=t();return Object.keys(r).forEach(function(t){i[t]=r[t]}),e}function H(t,n,i){t.exception=t.exception||{},t.exception.values=t.exception.values||[],t.exception.values[0]=t.exception.values[0]||{},t.exception.values[0].value=t.exception.values[0].value||n||"",t.exception.values[0].type=t.exception.values[0].type||i||"Error"}function B(t,n){void 0===n&&(n={});try{t.exception.values[0].mechanism=t.exception.values[0].mechanism||{},Object.keys(n).forEach(function(i){t.exception.values[0].mechanism[i]=n[i]})}catch(t){}}var J=6e4;var X=L(),G="Sentry Logger ",W=function(){function t(){this.u=!1}return t.prototype.disable=function(){this.u=!1},t.prototype.enable=function(){this.u=!0},t.prototype.log=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.u&&U(function(){X.console.log(G+"[Log]: "+t.join(" "))})},t.prototype.warn=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.u&&U(function(){X.console.warn(G+"[Warn]: "+t.join(" "))})},t.prototype.error=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this.u&&U(function(){X.console.error(G+"[Error]: "+t.join(" "))})},t}();X.__SENTRY__=X.__SENTRY__||{};var $=X.__SENTRY__.logger||(X.__SENTRY__.logger=new W),z=function(){function t(){this.s="function"==typeof WeakSet,this.h=this.s?new WeakSet:[]}return t.prototype.memoize=function(t){if(this.s)return!!this.h.has(t)||(this.h.add(t),!1);for(var n=0;n<this.h.length;n++){if(this.h[n]===t)return!0}return this.h.push(t),!1},t.prototype.unmemoize=function(t){if(this.s)this.h.delete(t);else for(var n=0;n<this.h.length;n++)if(this.h[n]===t){this.h.splice(n,1);break}},t}(),K="<anonymous>";function V(t){try{return t&&"function"==typeof t&&t.name||K}catch(t){return K}}function Y(t,n,i){if(n in t){var r=t[n],e=i(r);if("function"==typeof e)try{e.prototype=e.prototype||{},Object.defineProperties(e,{__sentry_original__:{enumerable:!1,value:r}})}catch(t){}t[n]=e}}function Q(t){if(l(t)){var n=t,i={message:n.message,name:n.name,stack:n.stack};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(i[r]=n[r]);return i}if(w(t)){var e=t,o={};o.type=e.type;try{o.target=T(e.target)?x(e.target):Object.prototype.toString.call(e.target)}catch(t){o.target="<unknown>"}try{o.currentTarget=T(e.currentTarget)?x(e.currentTarget):Object.prototype.toString.call(e.currentTarget)}catch(t){o.currentTarget="<unknown>"}for(var r in"undefined"!=typeof CustomEvent&&S(t,CustomEvent)&&(o.detail=e.detail),e)Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e);return o}return t}function Z(t){return function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(t))}function tt(t,n,i){void 0===n&&(n=3),void 0===i&&(i=102400);var r=rt(t,n);return Z(r)>i?tt(t,n-1,i):r}function nt(t,n){return"domain"===n&&t&&"object"==typeof t&&t.v?"[Domain]":"domainEmitter"===n?"[DomainEmitter]":"undefined"!=typeof global&&t===global?"[Global]":"undefined"!=typeof window&&t===window?"[Window]":"undefined"!=typeof document&&t===document?"[Document]":b(i=t)&&"nativeEvent"in i&&"preventDefault"in i&&"stopPropagation"in i?"[SyntheticEvent]":"number"==typeof t&&t!=t?"[NaN]":void 0===t?"[undefined]":"function"==typeof t?"[Function: "+V(t)+"]":"symbol"==typeof t?"["+String(t)+"]":"bigint"==typeof t?"[BigInt: "+String(t)+"]":t;var i}function it(t,n,i,r){if(void 0===i&&(i=1/0),void 0===r&&(r=new z),0===i)return function(t){var n=Object.prototype.toString.call(t);if("string"==typeof t)return t;if("[object Object]"===n)return"[Object]";if("[object Array]"===n)return"[Array]";var i=nt(t);return g(i)?i:n}(n);if(null!=n&&"function"==typeof n.toJSON)return n.toJSON();var e=nt(n,t);if(g(e))return e;var o=Q(n),u=Array.isArray(n)?[]:{};if(r.memoize(n))return"[Circular ~]";for(var s in o)Object.prototype.hasOwnProperty.call(o,s)&&(u[s]=it(s,o[s],i-1,r));return r.unmemoize(n),u}function rt(t,n){try{return JSON.parse(JSON.stringify(t,function(t,i){return it(t,i,n)}))}catch(t){return"**non-serializable**"}}function et(t,n){void 0===n&&(n=40);var i=Object.keys(Q(t));if(i.sort(),!i.length)return"[object has no keys]";if(i[0].length>=n)return R(i[0],n);for(var r=i.length;r>0;r--){var e=i.slice(0,r).join(", ");if(!(e.length>n))return r===i.length?e:R(e,n)}return""}function ot(t){var n,i;if(b(t)){var r=t,e={};try{for(var o=h(Object.keys(r)),u=o.next();!u.done;u=o.next()){var s=u.value;void 0!==r[s]&&(e[s]=ot(r[s]))}}catch(t){n={error:t}}finally{try{u&&!u.done&&(i=o.return)&&i.call(o)}finally{if(n)throw n.error}}return e}return Array.isArray(t)?t.map(ot):t}function ut(){if(!("fetch"in L()))return!1;try{return new Headers,new Request(""),new Response,!0}catch(t){return!1}}function st(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function at(){if(!ut())return!1;try{return new Request("_",{referrerPolicy:"origin"}),!0}catch(t){return!1}}var ct,ft=L(),ht={},vt={};function dt(t){if(!vt[t])switch(vt[t]=!0,t){case"console":!function(){if(!("console"in ft))return;["debug","info","warn","error","log","assert"].forEach(function(t){t in ft.console&&Y(ft.console,t,function(n){return function(){for(var i=[],r=0;r<arguments.length;r++)i[r]=arguments[r];pt("console",{args:i,level:t}),n&&Function.prototype.apply.call(n,ft.console,i)}})})}();break;case"dom":!function(){if(!("document"in ft))return;var t=pt.bind(null,"dom"),n=Tt(t,!0);ft.document.addEventListener("click",n,!1),ft.document.addEventListener("keypress",n,!1),["EventTarget","Node"].forEach(function(n){var i=ft[n]&&ft[n].prototype;i&&i.hasOwnProperty&&i.hasOwnProperty("addEventListener")&&(Y(i,"addEventListener",function(n){return function(i,r,e){if("click"===i||"keypress"==i)try{var o=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},u=o[i]=o[i]||{refCount:0};if(!u.handler){var s=Tt(t);u.handler=s,n.call(this,i,s,e)}u.refCount+=1}catch(t){}return n.call(this,i,r,e)}}),Y(i,"removeEventListener",function(t){return function(n,i,r){if("click"===n||"keypress"==n)try{var e=this.__sentry_instrumentation_handlers__||{},o=e[n];o&&(o.refCount-=1,o.refCount<=0&&(t.call(this,n,o.handler,r),o.handler=void 0,delete e[n]),0===Object.keys(e).length&&delete this.__sentry_instrumentation_handlers__)}catch(t){}return t.call(this,n,i,r)}}))})}();break;case"xhr":!function(){if(!("XMLHttpRequest"in ft))return;var t=[],n=[],i=XMLHttpRequest.prototype;Y(i,"open",function(i){return function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];var o=this,u=r[1];o.__sentry_xhr__={method:y(r[0])?r[0].toUpperCase():r[0],url:r[1]},y(u)&&"POST"===o.__sentry_xhr__.method&&u.match(/sentry_key/)&&(o.__sentry_own_request__=!0);var s=function(){if(4===o.readyState){try{o.__sentry_xhr__&&(o.__sentry_xhr__.status_code=o.status)}catch(t){}try{var i=t.indexOf(o);if(-1!==i){t.splice(i);var e=n.splice(i)[0];o.__sentry_xhr__&&void 0!==e[0]&&(o.__sentry_xhr__.body=e[0])}}catch(t){}pt("xhr",{args:r,endTimestamp:Date.now(),startTimestamp:Date.now(),xhr:o})}};return"onreadystatechange"in o&&"function"==typeof o.onreadystatechange?Y(o,"onreadystatechange",function(t){return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];return s(),t.apply(o,n)}}):o.addEventListener("readystatechange",s),i.apply(o,r)}}),Y(i,"send",function(i){return function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];return t.push(this),n.push(r),pt("xhr",{args:r,startTimestamp:Date.now(),xhr:this}),i.apply(this,r)}})}();break;case"fetch":!function(){if(!function(){if(!ut())return!1;var t=L();if(st(t.fetch))return!0;var n=!1,i=t.document;if(i&&"function"==typeof i.createElement)try{var r=i.createElement("iframe");r.hidden=!0,i.head.appendChild(r),r.contentWindow&&r.contentWindow.fetch&&(n=st(r.contentWindow.fetch)),i.head.removeChild(r)}catch(t){$.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",t)}return n}())return;Y(ft,"fetch",function(t){return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var r={args:n,fetchData:{method:mt(n),url:yt(n)},startTimestamp:Date.now()};return pt("fetch",c({},r)),t.apply(ft,n).then(function(t){return pt("fetch",c(c({},r),{endTimestamp:Date.now(),response:t})),t},function(t){throw pt("fetch",c(c({},r),{endTimestamp:Date.now(),error:t})),t})}})}();break;case"history":!function(){if(t=L(),n=t.chrome,i=n&&n.app&&n.app.runtime,r="history"in t&&!!t.history.pushState&&!!t.history.replaceState,i||!r)return;var t,n,i,r;var e=ft.onpopstate;function o(t){return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var r=n.length>2?n[2]:void 0;if(r){var e=ct,o=String(r);ct=o,pt("history",{from:e,to:o})}return t.apply(this,n)}}ft.onpopstate=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var i=ft.location.href,r=ct;if(ct=i,pt("history",{from:r,to:i}),e)try{return e.apply(this,t)}catch(t){}},Y(ft.history,"pushState",o),Y(ft.history,"replaceState",o)}();break;case"error":Et=ft.onerror,ft.onerror=function(t,n,i,r,e){return pt("error",{column:r,error:e,line:i,msg:t,url:n}),!!Et&&Et.apply(this,arguments)};break;case"unhandledrejection":xt=ft.onunhandledrejection,ft.onunhandledrejection=function(t){return pt("unhandledrejection",t),!xt||xt.apply(this,arguments)};break;default:$.warn("unknown instrumentation type:",t)}}function lt(t){t&&"string"==typeof t.type&&"function"==typeof t.callback&&(ht[t.type]=ht[t.type]||[],ht[t.type].push(t.callback),dt(t.type))}function pt(t,n){var i,r;if(t&&ht[t])try{for(var e=h(ht[t]||[]),o=e.next();!o.done;o=e.next()){var u=o.value;try{u(n)}catch(n){$.error("Error while triggering instrumentation handler.\nType: "+t+"\nName: "+V(u)+"\nError: "+n)}}}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=e.return)&&r.call(e)}finally{if(i)throw i.error}}}function mt(t){return void 0===t&&(t=[]),"Request"in ft&&S(t[0],Request)&&t[0].method?String(t[0].method).toUpperCase():t[1]&&t[1].method?String(t[1].method).toUpperCase():"GET"}function yt(t){return void 0===t&&(t=[]),"string"==typeof t[0]?t[0]:"Request"in ft&&S(t[0],Request)?t[0].url:String(t[0])}var gt,bt,wt=1e3;function Tt(t,n){return void 0===n&&(n=!1),function(i){if(i&&bt!==i&&!function(t){if("keypress"!==t.type)return!1;try{var n=t.target;if(!n||!n.tagName)return!0;if("INPUT"===n.tagName||"TEXTAREA"===n.tagName||n.isContentEditable)return!1}catch(t){}return!0}(i)){var r="keypress"===i.type?"input":i.type;void 0===gt?(t({event:i,name:r,global:n}),bt=i):function(t,n){if(!t)return!0;if(t.type!==n.type)return!0;try{if(t.target!==n.target)return!0}catch(t){}return!1}(bt,i)&&(t({event:i,name:r,global:n}),bt=i),clearTimeout(gt),gt=ft.setTimeout(function(){gt=void 0},wt)}}}var Et=null;var St,xt=null;!function(t){t.PENDING="PENDING",t.RESOLVED="RESOLVED",t.REJECTED="REJECTED"}(St||(St={}));var _t=function(){function t(t){var n=this;this.l=St.PENDING,this.p=[],this.m=function(t){n.g(St.RESOLVED,t)},this.T=function(t){n.g(St.REJECTED,t)},this.g=function(t,i){n.l===St.PENDING&&(E(i)?i.then(n.m,n.T):(n.l=t,n.S=i,n._()))},this.j=function(t){n.p=n.p.concat(t),n._()},this._=function(){if(n.l!==St.PENDING){var t=n.p.slice();n.p=[],t.forEach(function(t){t.done||(n.l===St.RESOLVED&&t.onfulfilled&&t.onfulfilled(n.S),n.l===St.REJECTED&&t.onrejected&&t.onrejected(n.S),t.done=!0)})}};try{t(this.m,this.T)}catch(t){this.T(t)}}return t.resolve=function(n){return new t(function(t){t(n)})},t.reject=function(n){return new t(function(t,i){i(n)})},t.all=function(n){return new t(function(i,r){if(Array.isArray(n))if(0!==n.length){var e=n.length,o=[];n.forEach(function(n,u){t.resolve(n).then(function(t){o[u]=t,0===(e-=1)&&i(o)}).then(null,r)})}else i([]);else r(new TypeError("Promise.all requires an array as input."))})},t.prototype.then=function(n,i){var r=this;return new t(function(t,e){r.j({done:!1,onfulfilled:function(i){if(n)try{return void t(n(i))}catch(t){return void e(t)}else t(i)},onrejected:function(n){if(i)try{return void t(i(n))}catch(t){return void e(t)}else e(n)}})})},t.prototype.catch=function(t){return this.then(function(t){return t},t)},t.prototype.finally=function(n){var i=this;return new t(function(t,r){var e,o;return i.then(function(t){o=!1,e=t,n&&n()},function(t){o=!0,e=t,n&&n()}).then(function(){o?r(e):t(e)})})},t.prototype.toString=function(){return"[object SyncPromise]"},t}(),jt=function(){function t(t){this.k=t,this.O=[]}return t.prototype.isReady=function(){return void 0===this.k||this.length()<this.k},t.prototype.add=function(t){var n=this;if(!this.isReady())return _t.reject(new k("Not adding Promise due to buffer limit reached."));var i=t();return-1===this.O.indexOf(i)&&this.O.push(i),i.then(function(){return n.remove(i)}).then(null,function(){return n.remove(i).then(null,function(){})}),i},t.prototype.remove=function(t){return this.O.splice(this.O.indexOf(t),1)[0]},t.prototype.length=function(){return this.O.length},t.prototype.drain=function(t){var n=this;return new _t(function(i){var r=setTimeout(function(){t&&t>0&&i(!1)},t);_t.all(n.O).then(function(){clearTimeout(r),i(!0)}).then(null,function(){i(!0)})})},t}(),kt={nowSeconds:function(){return Date.now()/1e3}};var Ot=I()?function(){try{return N(module,"perf_hooks").performance}catch(t){return}}():function(){var t=L().performance;if(t&&t.now)return{now:function(){return t.now()},timeOrigin:Date.now()-t.now()}}(),Dt=void 0===Ot?kt:{nowSeconds:function(){return(Ot.timeOrigin+Ot.now())/1e3}},It=kt.nowSeconds.bind(kt),Nt=Dt.nowSeconds.bind(Dt),Rt=Nt,Ct=function(){var t=L().performance;if(t&&t.now){var n=t.now(),i=Date.now(),r=t.timeOrigin?Math.abs(t.timeOrigin+n-i):36e5,e=r<36e5,o=t.timing&&t.timing.navigationStart,u="number"==typeof o?Math.abs(o+n-i):36e5;return e||u<36e5?r<=u?t.timeOrigin:o:i}}(),Mt=function(){function t(){this.D=!1,this.I=[],this.N=[],this.R=[],this.C={},this.M={},this.A={},this.L={}}return t.clone=function(n){var i=new t;return n&&(i.R=d(n.R),i.M=c({},n.M),i.A=c({},n.A),i.L=c({},n.L),i.C=n.C,i.q=n.q,i.F=n.F,i.P=n.P,i.U=n.U,i.H=n.H,i.N=d(n.N),i.B=n.B),i},t.prototype.addScopeListener=function(t){this.I.push(t)},t.prototype.addEventProcessor=function(t){return this.N.push(t),this},t.prototype.setUser=function(t){return this.C=t||{},this.P&&this.P.update({user:t}),this.J(),this},t.prototype.getUser=function(){return this.C},t.prototype.getRequestSession=function(){return this.B},t.prototype.setRequestSession=function(t){return this.B=t,this},t.prototype.setTags=function(t){return this.M=c(c({},this.M),t),this.J(),this},t.prototype.setTag=function(t,n){var i;return this.M=c(c({},this.M),((i={})[t]=n,i)),this.J(),this},t.prototype.setExtras=function(t){return this.A=c(c({},this.A),t),this.J(),this},t.prototype.setExtra=function(t,n){var i;return this.A=c(c({},this.A),((i={})[t]=n,i)),this.J(),this},t.prototype.setFingerprint=function(t){return this.H=t,this.J(),this},t.prototype.setLevel=function(t){return this.q=t,this.J(),this},t.prototype.setTransactionName=function(t){return this.U=t,this.J(),this},t.prototype.setTransaction=function(t){return this.setTransactionName(t)},t.prototype.setContext=function(t,n){var i;return null===n?delete this.L[t]:this.L=c(c({},this.L),((i={})[t]=n,i)),this.J(),this},t.prototype.setSpan=function(t){return this.F=t,this.J(),this},t.prototype.getSpan=function(){return this.F},t.prototype.getTransaction=function(){var t,n,i,r,e=this.getSpan();return(null===(t=e)||void 0===t?void 0:t.transaction)?null===(n=e)||void 0===n?void 0:n.transaction:(null===(r=null===(i=e)||void 0===i?void 0:i.spanRecorder)||void 0===r?void 0:r.spans[0])?e.spanRecorder.spans[0]:void 0},t.prototype.setSession=function(t){return t?this.P=t:delete this.P,this.J(),this},t.prototype.getSession=function(){return this.P},t.prototype.update=function(n){if(!n)return this;if("function"==typeof n){var i=n(this);return i instanceof t?i:this}return n instanceof t?(this.M=c(c({},this.M),n.M),this.A=c(c({},this.A),n.A),this.L=c(c({},this.L),n.L),n.C&&Object.keys(n.C).length&&(this.C=n.C),n.q&&(this.q=n.q),n.H&&(this.H=n.H),n.B&&(this.B=n.B)):b(n)&&(n=n,this.M=c(c({},this.M),n.tags),this.A=c(c({},this.A),n.extra),this.L=c(c({},this.L),n.contexts),n.user&&(this.C=n.user),n.level&&(this.q=n.level),n.fingerprint&&(this.H=n.fingerprint),n.requestSession&&(this.B=n.requestSession)),this},t.prototype.clear=function(){return this.R=[],this.M={},this.A={},this.C={},this.L={},this.q=void 0,this.U=void 0,this.H=void 0,this.B=void 0,this.F=void 0,this.P=void 0,this.J(),this},t.prototype.addBreadcrumb=function(t,n){var i="number"==typeof n?Math.min(n,100):100;if(i<=0)return this;var r=c({timestamp:It()},t);return this.R=d(this.R,[r]).slice(-i),this.J(),this},t.prototype.clearBreadcrumbs=function(){return this.R=[],this.J(),this},t.prototype.applyToEvent=function(t,n){var i;if(this.A&&Object.keys(this.A).length&&(t.extra=c(c({},this.A),t.extra)),this.M&&Object.keys(this.M).length&&(t.tags=c(c({},this.M),t.tags)),this.C&&Object.keys(this.C).length&&(t.user=c(c({},this.C),t.user)),this.L&&Object.keys(this.L).length&&(t.contexts=c(c({},this.L),t.contexts)),this.q&&(t.level=this.q),this.U&&(t.transaction=this.U),this.F){t.contexts=c({trace:this.F.getTraceContext()},t.contexts);var r=null===(i=this.F.transaction)||void 0===i?void 0:i.name;r&&(t.tags=c({transaction:r},t.tags))}return this.X(t),t.breadcrumbs=d(t.breadcrumbs||[],this.R),t.breadcrumbs=t.breadcrumbs.length>0?t.breadcrumbs:void 0,this.G(d(At(),this.N),t,n)},t.prototype.G=function(t,n,i,r){var e=this;return void 0===r&&(r=0),new _t(function(o,u){var s=t[r];if(null===n||"function"!=typeof s)o(n);else{var a=s(c({},n),i);E(a)?a.then(function(n){return e.G(t,n,i,r+1).then(o)}).then(null,u):e.G(t,a,i,r+1).then(o).then(null,u)}})},t.prototype.J=function(){var t=this;this.D||(this.D=!0,this.I.forEach(function(n){n(t)}),this.D=!1)},t.prototype.X=function(t){t.fingerprint=t.fingerprint?Array.isArray(t.fingerprint)?t.fingerprint:[t.fingerprint]:[],this.H&&(t.fingerprint=t.fingerprint.concat(this.H)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint},t}();function At(){var t=L();return t.__SENTRY__=t.__SENTRY__||{},t.__SENTRY__.globalEventProcessors=t.__SENTRY__.globalEventProcessors||[],t.__SENTRY__.globalEventProcessors}function Lt(t){At().push(t)}var qt=function(){function t(t){this.errors=0,this.sid=q(),this.duration=0,this.status=e.Ok,this.init=!0,this.ignoreDuration=!1;var n=Nt();this.timestamp=n,this.started=n,t&&this.update(t)}return t.prototype.update=function(t){if(void 0===t&&(t={}),t.user&&(!this.ipAddress&&t.user.ip_address&&(this.ipAddress=t.user.ip_address),this.did||t.did||(this.did=t.user.id||t.user.email||t.user.username)),this.timestamp=t.timestamp||Nt(),t.ignoreDuration&&(this.ignoreDuration=t.ignoreDuration),t.sid&&(this.sid=32===t.sid.length?t.sid:q()),void 0!==t.init&&(this.init=t.init),!this.did&&t.did&&(this.did=""+t.did),"number"==typeof t.started&&(this.started=t.started),this.ignoreDuration)this.duration=void 0;else if("number"==typeof t.duration)this.duration=t.duration;else{var n=this.timestamp-this.started;this.duration=n>=0?n:0}t.release&&(this.release=t.release),t.environment&&(this.environment=t.environment),!this.ipAddress&&t.ipAddress&&(this.ipAddress=t.ipAddress),!this.userAgent&&t.userAgent&&(this.userAgent=t.userAgent),"number"==typeof t.errors&&(this.errors=t.errors),t.status&&(this.status=t.status)},t.prototype.close=function(t){t?this.update({status:t}):this.status===e.Ok?this.update({status:e.Exited}):this.update()},t.prototype.toJSON=function(){return ot({sid:""+this.sid,init:this.init,started:new Date(1e3*this.started).toISOString(),timestamp:new Date(1e3*this.timestamp).toISOString(),status:this.status,errors:this.errors,did:"number"==typeof this.did||"string"==typeof this.did?""+this.did:void 0,duration:this.duration,attrs:ot({release:this.release,environment:this.environment,ip_address:this.ipAddress,user_agent:this.userAgent})})},t}(),Ft=4,Pt=function(){function t(t,n,i){void 0===n&&(n=new Mt),void 0===i&&(i=Ft),this.W=i,this.$=[{}],this.getStackTop().scope=n,this.bindClient(t)}return t.prototype.isOlderThan=function(t){return this.W<t},t.prototype.bindClient=function(t){this.getStackTop().client=t,t&&t.setupIntegrations&&t.setupIntegrations()},t.prototype.pushScope=function(){var t=Mt.clone(this.getScope());return this.getStack().push({client:this.getClient(),scope:t}),t},t.prototype.popScope=function(){return!(this.getStack().length<=1)&&!!this.getStack().pop()},t.prototype.withScope=function(t){var n=this.pushScope();try{t(n)}finally{this.popScope()}},t.prototype.getClient=function(){return this.getStackTop().client},t.prototype.getScope=function(){return this.getStackTop().scope},t.prototype.getStack=function(){return this.$},t.prototype.getStackTop=function(){return this.$[this.$.length-1]},t.prototype.captureException=function(t,n){var i=this.K=q(),r=n;if(!n){var e=void 0;try{throw new Error("Sentry syntheticException")}catch(t){e=t}r={originalException:t,syntheticException:e}}return this.V("captureException",t,c(c({},r),{event_id:i})),i},t.prototype.captureMessage=function(t,n,i){var r=this.K=q(),e=i;if(!i){var o=void 0;try{throw new Error(t)}catch(t){o=t}e={originalException:t,syntheticException:o}}return this.V("captureMessage",t,n,c(c({},e),{event_id:r})),r},t.prototype.captureEvent=function(t,n){var i=this.K=q();return this.V("captureEvent",t,c(c({},n),{event_id:i})),i},t.prototype.lastEventId=function(){return this.K},t.prototype.addBreadcrumb=function(t,n){var i=this.getStackTop(),r=i.scope,e=i.client;if(r&&e){var o=e.getOptions&&e.getOptions()||{},u=o.beforeBreadcrumb,s=void 0===u?null:u,a=o.maxBreadcrumbs,f=void 0===a?100:a;if(!(f<=0)){var h=It(),v=c({timestamp:h},t),d=s?U(function(){return s(v,n)}):v;null!==d&&r.addBreadcrumb(d,f)}}},t.prototype.setUser=function(t){var n=this.getScope();n&&n.setUser(t)},t.prototype.setTags=function(t){var n=this.getScope();n&&n.setTags(t)},t.prototype.setExtras=function(t){var n=this.getScope();n&&n.setExtras(t)},t.prototype.setTag=function(t,n){var i=this.getScope();i&&i.setTag(t,n)},t.prototype.setExtra=function(t,n){var i=this.getScope();i&&i.setExtra(t,n)},t.prototype.setContext=function(t,n){var i=this.getScope();i&&i.setContext(t,n)},t.prototype.configureScope=function(t){var n=this.getStackTop(),i=n.scope,r=n.client;i&&r&&t(i)},t.prototype.run=function(t){var n=Ht(this);try{t(this)}finally{Ht(n)}},t.prototype.getIntegration=function(t){var n=this.getClient();if(!n)return null;try{return n.getIntegration(t)}catch(n){return $.warn("Cannot retrieve integration "+t.id+" from the current Hub"),null}},t.prototype.startSpan=function(t){return this.Y("startSpan",t)},t.prototype.startTransaction=function(t,n){return this.Y("startTransaction",t,n)},t.prototype.traceHeaders=function(){return this.Y("traceHeaders")},t.prototype.captureSession=function(t){if(void 0===t&&(t=!1),t)return this.endSession();this.Z()},t.prototype.endSession=function(){var t,n,i,r,e;null===(i=null===(n=null===(t=this.getStackTop())||void 0===t?void 0:t.scope)||void 0===n?void 0:n.getSession())||void 0===i||i.close(),this.Z(),null===(e=null===(r=this.getStackTop())||void 0===r?void 0:r.scope)||void 0===e||e.setSession()},t.prototype.startSession=function(t){var n=this.getStackTop(),i=n.scope,r=n.client,o=r&&r.getOptions()||{},u=o.release,s=o.environment,a=(L().navigator||{}).userAgent,f=new qt(c(c(c({release:u,environment:s},i&&{user:i.getUser()}),a&&{userAgent:a}),t));if(i){var h=i.getSession&&i.getSession();h&&h.status===e.Ok&&h.update({status:e.Exited}),this.endSession(),i.setSession(f)}return f},t.prototype.Z=function(){var t=this.getStackTop(),n=t.scope,i=t.client;if(n){var r=n.getSession&&n.getSession();r&&i&&i.captureSession&&i.captureSession(r)}},t.prototype.V=function(t){for(var n,i=[],r=1;r<arguments.length;r++)i[r-1]=arguments[r];var e=this.getStackTop(),o=e.scope,u=e.client;u&&u[t]&&(n=u)[t].apply(n,d(i,[o]))},t.prototype.Y=function(t){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];var r=Ut().__SENTRY__;if(r&&r.extensions&&"function"==typeof r.extensions[t])return r.extensions[t].apply(this,n);$.warn("Extension method "+t+" couldn't be found, doing nothing.")},t}();function Ut(){var t=L();return t.__SENTRY__=t.__SENTRY__||{extensions:{},hub:void 0},t}function Ht(t){var n=Ut(),i=Xt(n);return Gt(n,t),i}function Bt(){var t=Ut();return Jt(t)&&!Xt(t).isOlderThan(Ft)||Gt(t,new Pt),I()?function(t){var n,i,r;try{var e=null===(r=null===(i=null===(n=Ut().__SENTRY__)||void 0===n?void 0:n.extensions)||void 0===i?void 0:i.domain)||void 0===r?void 0:r.active;if(!e)return Xt(t);if(!Jt(e)||Xt(e).isOlderThan(Ft)){var o=Xt(t).getStackTop();Gt(e,new Pt(o.client,Mt.clone(o.scope)))}return Xt(e)}catch(n){return Xt(t)}}(t):Xt(t)}function Jt(t){return!!(t&&t.__SENTRY__&&t.__SENTRY__.hub)}function Xt(t){return t&&t.__SENTRY__&&t.__SENTRY__.hub?t.__SENTRY__.hub:(t.__SENTRY__=t.__SENTRY__||{},t.__SENTRY__.hub=new Pt,t.__SENTRY__.hub)}function Gt(t,n){return!!t&&(t.__SENTRY__=t.__SENTRY__||{},t.__SENTRY__.hub=n,!0)}function Wt(t){for(var n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];var r=Bt();if(r&&r[t])return r[t].apply(r,d(n));throw new Error("No hub defined or "+t+" was not found on the hub, please open a bug report.")}function captureException(t,n){var i;try{throw new Error("Sentry syntheticException")}catch(t){i=t}return Wt("captureException",t,{captureContext:n,originalException:t,syntheticException:i})}function $t(t){Wt("withScope",t)}var zt=function(){function t(t,n,i){void 0===n&&(n={}),this.dsn=t,this.tt=new D(t),this.metadata=n,this.nt=i}return t.prototype.getDsn=function(){return this.tt},t.prototype.forceEnvelope=function(){return!!this.nt},t.prototype.getBaseApiEndpoint=function(){var t=this.getDsn(),n=t.protocol?t.protocol+":":"",i=t.port?":"+t.port:"";return n+"//"+t.host+i+(t.path?"/"+t.path:"")+"/api/"},t.prototype.getStoreEndpoint=function(){return this.it("store")},t.prototype.getStoreEndpointWithUrlEncodedAuth=function(){return this.getStoreEndpoint()+"?"+this.rt()},t.prototype.getEnvelopeEndpointWithUrlEncodedAuth=function(){return this.forceEnvelope()?this.nt:this.et()+"?"+this.rt()},t.prototype.getStoreEndpointPath=function(){var t=this.getDsn();return(t.path?"/"+t.path:"")+"/api/"+t.projectId+"/store/"},t.prototype.getRequestHeaders=function(t,n){var i=this.getDsn(),r=["Sentry sentry_version=7"];return r.push("sentry_client="+t+"/"+n),r.push("sentry_key="+i.publicKey),i.pass&&r.push("sentry_secret="+i.pass),{"Content-Type":"application/json","X-Sentry-Auth":r.join(", ")}},t.prototype.getReportDialogEndpoint=function(t){void 0===t&&(t={});var n=this.getDsn(),i=this.getBaseApiEndpoint()+"embed/error-page/",r=[];for(var e in r.push("dsn="+n.toString()),t)if("dsn"!==e)if("user"===e){if(!t.user)continue;t.user.name&&r.push("name="+encodeURIComponent(t.user.name)),t.user.email&&r.push("email="+encodeURIComponent(t.user.email))}else r.push(encodeURIComponent(e)+"="+encodeURIComponent(t[e]));return r.length?i+"?"+r.join("&"):i},t.prototype.et=function(){return this.it("envelope")},t.prototype.it=function(t){return this.nt?this.nt:""+this.getBaseApiEndpoint()+this.getDsn().projectId+"/"+t+"/"},t.prototype.rt=function(){var t,n={sentry_key:this.getDsn().publicKey,sentry_version:"7"};return t=n,Object.keys(t).map(function(n){return encodeURIComponent(n)+"="+encodeURIComponent(t[n])}).join("&")},t}(),Kt=[];function Vt(t){return t.reduce(function(t,n){return t.every(function(t){return n.name!==t.name})&&t.push(n),t},[])}function Yt(t){var n={};return function(t){var n=t.defaultIntegrations&&d(t.defaultIntegrations)||[],i=t.integrations,r=d(Vt(n));Array.isArray(i)?r=d(r.filter(function(t){return i.every(function(n){return n.name!==t.name})}),Vt(i)):"function"==typeof i&&(r=i(r),r=Array.isArray(r)?r:[r]);var e=r.map(function(t){return t.name});return-1!==e.indexOf("Debug")&&r.push.apply(r,d(r.splice(e.indexOf("Debug"),1))),r}(t).forEach(function(t){n[t.name]=t,function(t){-1===Kt.indexOf(t.name)&&(t.setupOnce(Lt,Bt),Kt.push(t.name),$.log("Integration installed: "+t.name))}(t)}),n}var Qt=function(){function t(t,n){this.ot={},this.ut=0,this.st=new t(n),this.at=n,n.dsn&&(this.ct=new D(n.dsn))}return t.prototype.captureException=function(t,n,i){var r=this,e=n&&n.event_id;return this.ft(this.ht().eventFromException(t,n).then(function(t){return r.vt(t,n,i)}).then(function(t){e=t})),e},t.prototype.captureMessage=function(t,n,i,r){var e=this,o=i&&i.event_id,u=g(t)?this.ht().eventFromMessage(String(t),n,i):this.ht().eventFromException(t,i);return this.ft(u.then(function(t){return e.vt(t,i,r)}).then(function(t){o=t})),o},t.prototype.captureEvent=function(t,n,i){var r=n&&n.event_id;return this.ft(this.vt(t,n,i).then(function(t){r=t})),r},t.prototype.captureSession=function(t){this.dt()?"string"!=typeof t.release?$.warn("Discarded session because of missing or non-string release"):(this.lt(t),t.update({init:!1})):$.warn("SDK not enabled, will not capture session.")},t.prototype.getDsn=function(){return this.ct},t.prototype.getOptions=function(){return this.at},t.prototype.flush=function(t){var n=this;return this.pt(t).then(function(i){return n.ht().getTransport().close(t).then(function(t){return i&&t})})},t.prototype.close=function(t){var n=this;return this.flush(t).then(function(t){return n.getOptions().enabled=!1,t})},t.prototype.setupIntegrations=function(){this.dt()&&(this.ot=Yt(this.at))},t.prototype.getIntegration=function(t){try{return this.ot[t.id]||null}catch(n){return $.warn("Cannot retrieve integration "+t.id+" from the current Client"),null}},t.prototype.yt=function(t,n){var i,r,o=!1,u=!1,s=n.exception&&n.exception.values;if(s){u=!0;try{for(var a=h(s),f=a.next();!f.done;f=a.next()){var v=f.value.mechanism;if(v&&!1===v.handled){o=!0;break}}}catch(t){i={error:t}}finally{try{f&&!f.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}}var d=t.status===e.Ok;(d&&0===t.errors||d&&o)&&(t.update(c(c({},o&&{status:e.Crashed}),{errors:t.errors||Number(u||o)})),this.captureSession(t))},t.prototype.lt=function(t){this.ht().sendSession(t)},t.prototype.pt=function(t){var n=this;return new _t(function(i){var r=0,e=setInterval(function(){0==n.ut?(clearInterval(e),i(!0)):(r+=1,t&&r>=t&&(clearInterval(e),i(!1)))},1)})},t.prototype.ht=function(){return this.st},t.prototype.dt=function(){return!1!==this.getOptions().enabled&&void 0!==this.ct},t.prototype.gt=function(t,n,i){var r=this,e=this.getOptions().normalizeDepth,o=void 0===e?3:e,u=c(c({},t),{event_id:t.event_id||(i&&i.event_id?i.event_id:q()),timestamp:t.timestamp||It()});this.bt(u),this.wt(u);var s=n;i&&i.captureContext&&(s=Mt.clone(s).update(i.captureContext));var a=_t.resolve(u);return s&&(a=s.applyToEvent(u,i)),a.then(function(t){return"number"==typeof o&&o>0?r.Tt(t,o):t})},t.prototype.Tt=function(t,n){if(!t)return null;var i=c(c(c(c(c({},t),t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map(function(t){return c(c({},t),t.data&&{data:rt(t.data,n)})})}),t.user&&{user:rt(t.user,n)}),t.contexts&&{contexts:rt(t.contexts,n)}),t.extra&&{extra:rt(t.extra,n)});t.contexts&&t.contexts.trace&&(i.contexts.trace=t.contexts.trace);var r=this.getOptions().Et;return(void 0===r?{}:r).ensureNoCircularStructures?rt(i):i},t.prototype.bt=function(t){var n=this.getOptions(),i=n.environment,r=n.release,e=n.dist,o=n.maxValueLength,u=void 0===o?250:o;"environment"in t||(t.environment="environment"in n?i:"production"),void 0===t.release&&void 0!==r&&(t.release=r),void 0===t.dist&&void 0!==e&&(t.dist=e),t.message&&(t.message=R(t.message,u));var s=t.exception&&t.exception.values&&t.exception.values[0];s&&s.value&&(s.value=R(s.value,u));var a=t.request;a&&a.url&&(a.url=R(a.url,u))},t.prototype.wt=function(t){var n=Object.keys(this.ot);n.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=d(t.sdk.integrations||[],n))},t.prototype.St=function(t){this.ht().sendEvent(t)},t.prototype.vt=function(t,n,i){return this.xt(t,n,i).then(function(t){return t.event_id},function(t){$.error(t)})},t.prototype.xt=function(t,n,i){var r=this,e=this.getOptions(),o=e.beforeSend,u=e.sampleRate;if(!this.dt())return _t.reject(new k("SDK not enabled, will not capture event."));var s="transaction"===t.type;return!s&&"number"==typeof u&&Math.random()>u?_t.reject(new k("Discarding event because it's not included in the random sample (sampling rate = "+u+")")):this.gt(t,i,n).then(function(t){if(null===t)throw new k("An event processor returned null, will not send event.");if(n&&n.data&&!0===n.data.__sentry__||s||!o)return t;var i=o(t,n);return r._t(i)}).then(function(t){if(null===t)throw new k("`beforeSend` returned `null`, will not send event.");var n=i&&i.getSession&&i.getSession();return!s&&n&&r.yt(n,t),r.St(t),t}).then(null,function(t){if(t instanceof k)throw t;throw r.captureException(t,{data:{__sentry__:!0},originalException:t}),new k("Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: "+t)})},t.prototype.ft=function(t){var n=this;this.ut+=1,t.then(function(t){return n.ut-=1,t},function(t){return n.ut-=1,t})},t.prototype._t=function(t){var n="`beforeSend` method has to return `null` or a valid event.";if(E(t))return t.then(function(t){if(!b(t)&&null!==t)throw new k(n);return t},function(t){throw new k("beforeSend rejected with "+t)});if(!b(t)&&null!==t)throw new k(n);return t},t}(),Zt=function(){function n(){}return n.prototype.sendEvent=function(n){return _t.resolve({reason:"NoopTransport: Event has been skipped because no Dsn is configured.",status:t.Status.Skipped})},n.prototype.close=function(t){return _t.resolve(!0)},n}(),tn=function(){function t(t){this.at=t,this.at.dsn||$.warn("No DSN provided, backend will not do anything."),this.jt=this.kt()}return t.prototype.eventFromException=function(t,n){throw new k("Backend has to implement `eventFromException` method")},t.prototype.eventFromMessage=function(t,n,i){throw new k("Backend has to implement `eventFromMessage` method")},t.prototype.sendEvent=function(t){this.jt.sendEvent(t).then(null,function(t){$.error("Error while sending event: "+t)})},t.prototype.sendSession=function(t){this.jt.sendSession?this.jt.sendSession(t).then(null,function(t){$.error("Error while sending session: "+t)}):$.warn("Dropping session because custom transport doesn't implement sendSession")},t.prototype.getTransport=function(){return this.jt},t.prototype.kt=function(){return new Zt},t}();function nn(t){if(t.metadata&&t.metadata.sdk){var n=t.metadata.sdk;return{name:n.name,version:n.version}}}function rn(t,n){return n?(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||n.name,t.sdk.version=t.sdk.version||n.version,t.sdk.integrations=d(t.sdk.integrations||[],n.integrations||[]),t.sdk.packages=d(t.sdk.packages||[],n.packages||[]),t):t}function en(t,n){var i=nn(n),r="aggregates"in t?"sessions":"session";return{body:JSON.stringify(c(c({sent_at:(new Date).toISOString()},i&&{sdk:i}),n.forceEnvelope()&&{dsn:n.getDsn().toString()}))+"\n"+JSON.stringify({type:r})+"\n"+JSON.stringify(t),type:r,url:n.getEnvelopeEndpointWithUrlEncodedAuth()}}function on(t,n){var i=nn(n),r=t.type||"event",e="transaction"===r||n.forceEnvelope(),o=t.debug_meta||{},u=o.transactionSampling,s=f(o,["transactionSampling"]),a=u||{},h=a.method,v=a.rate;0===Object.keys(s).length?delete t.debug_meta:t.debug_meta=s;var d={body:JSON.stringify(i?rn(t,n.metadata.sdk):t),type:r,url:e?n.getEnvelopeEndpointWithUrlEncodedAuth():n.getStoreEndpointWithUrlEncodedAuth()};if(e){var l=JSON.stringify(c(c({event_id:t.event_id,sent_at:(new Date).toISOString()},i&&{sdk:i}),n.forceEnvelope()&&{dsn:n.getDsn().toString()}))+"\n"+JSON.stringify({type:r,sample_rates:[{id:h,rate:v}]})+"\n"+d.body;d.body=l}return d}var un,sn="6.10.0",an=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(){un=Function.prototype.toString,Function.prototype.toString=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var i=this.__sentry_original__||this;return un.apply(i,t)}},t.id="FunctionToString",t}(),cn=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/],fn=function(){function t(n){void 0===n&&(n={}),this.at=n,this.name=t.id}return t.prototype.setupOnce=function(){Lt(function(n){var i=Bt();if(!i)return n;var r=i.getIntegration(t);if(r){var e=i.getClient(),o=e?e.getOptions():{},u="function"==typeof r.Ot?r.Ot(o):{};return"function"!=typeof r.Dt?n:r.Dt(n,u)?null:n}return n})},t.prototype.Dt=function(t,n){return this.It(t,n)?($.warn("Event dropped due to being internal Sentry Error.\nEvent: "+P(t)),!0):this.Nt(t,n)?($.warn("Event dropped due to being matched by `ignoreErrors` option.\nEvent: "+P(t)),!0):this.Rt(t,n)?($.warn("Event dropped due to being matched by `denyUrls` option.\nEvent: "+P(t)+".\nUrl: "+this.Ct(t)),!0):!this.Mt(t,n)&&($.warn("Event dropped due to not being matched by `allowUrls` option.\nEvent: "+P(t)+".\nUrl: "+this.Ct(t)),!0)},t.prototype.It=function(t,n){if(!n.ignoreInternal)return!1;try{return t&&t.exception&&t.exception.values&&t.exception.values[0]&&"SentryError"===t.exception.values[0].type||!1}catch(t){return!1}},t.prototype.Nt=function(t,n){return!(!n.ignoreErrors||!n.ignoreErrors.length)&&this.At(t).some(function(t){return n.ignoreErrors.some(function(n){return M(t,n)})})},t.prototype.Rt=function(t,n){if(!n.denyUrls||!n.denyUrls.length)return!1;var i=this.Ct(t);return!!i&&n.denyUrls.some(function(t){return M(i,t)})},t.prototype.Mt=function(t,n){if(!n.allowUrls||!n.allowUrls.length)return!0;var i=this.Ct(t);return!i||n.allowUrls.some(function(t){return M(i,t)})},t.prototype.Ot=function(t){return void 0===t&&(t={}),{allowUrls:d(this.at.whitelistUrls||[],this.at.allowUrls||[],t.whitelistUrls||[],t.allowUrls||[]),denyUrls:d(this.at.blacklistUrls||[],this.at.denyUrls||[],t.blacklistUrls||[],t.denyUrls||[]),ignoreErrors:d(this.at.ignoreErrors||[],t.ignoreErrors||[],cn),ignoreInternal:void 0===this.at.ignoreInternal||this.at.ignoreInternal}},t.prototype.At=function(t){if(t.message)return[t.message];if(t.exception)try{var n=t.exception.values&&t.exception.values[0]||{},i=n.type,r=void 0===i?"":i,e=n.value,o=void 0===e?"":e;return[""+o,r+": "+o]}catch(n){return $.error("Cannot extract message for event "+P(t)),[]}return[]},t.prototype.Ct=function(t){try{if(t.stacktrace){var n=t.stacktrace.frames;return n&&n[n.length-1].filename||null}if(t.exception){var i=t.exception.values&&t.exception.values[0].stacktrace&&t.exception.values[0].stacktrace.frames;return i&&i[i.length-1].filename||null}return null}catch(n){return $.error("Cannot extract url for event "+P(t)),null}},t.id="InboundFilters",t}(),hn=Object.freeze({__proto__:null,FunctionToString:an,InboundFilters:fn}),vn="?",dn=/^\s*at (?:(.*?) ?\()?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,ln=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|capacitor).*?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. \/=]+)(?::(\d+))?(?::(\d+))?\s*$/i,pn=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,mn=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,yn=/\((\S*)(?::(\d+))(?::(\d+))\)/,gn=/Minified React error #\d+;/i;function bn(t){var n=null,i=0;t&&("number"==typeof t.framesToPop?i=t.framesToPop:gn.test(t.message)&&(i=1));try{if(n=function(t){if(!t||!t.stacktrace)return null;for(var n,i=t.stacktrace,r=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,e=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\((.*)\))? in (.*):\s*$/i,o=i.split("\n"),u=[],s=0;s<o.length;s+=2){var a=null;(n=r.exec(o[s]))?a={url:n[2],func:n[3],args:[],line:+n[1],column:null}:(n=e.exec(o[s]))&&(a={url:n[6],func:n[3]||n[4],args:n[5]?n[5].split(","):[],line:+n[1],column:+n[2]}),a&&(!a.func&&a.line&&(a.func=vn),u.push(a))}if(!u.length)return null;return{message:Tn(t),name:t.name,stack:u}}(t))return wn(n,i)}catch(t){}try{if(n=function(t){if(!t||!t.stack)return null;for(var n,i,r,e=[],o=t.stack.split("\n"),u=0;u<o.length;++u){if(i=dn.exec(o[u])){var s=i[2]&&0===i[2].indexOf("native");i[2]&&0===i[2].indexOf("eval")&&(n=yn.exec(i[2]))&&(i[2]=n[1],i[3]=n[2],i[4]=n[3]);var a=i[2]&&0===i[2].indexOf("address at ")?i[2].substr("address at ".length):i[2],c=i[1]||vn,f=-1!==c.indexOf("safari-extension"),h=-1!==c.indexOf("safari-web-extension");(f||h)&&(c=-1!==c.indexOf("@")?c.split("@")[0]:vn,a=f?"safari-extension:"+a:"safari-web-extension:"+a),r={url:a,func:c,args:s?[i[2]]:[],line:i[3]?+i[3]:null,column:i[4]?+i[4]:null}}else if(i=pn.exec(o[u]))r={url:i[2],func:i[1]||vn,args:[],line:+i[3],column:i[4]?+i[4]:null};else{if(!(i=ln.exec(o[u])))continue;i[3]&&i[3].indexOf(" > eval")>-1&&(n=mn.exec(i[3]))?(i[1]=i[1]||"eval",i[3]=n[1],i[4]=n[2],i[5]=""):0!==u||i[5]||void 0===t.columnNumber||(e[0].column=t.columnNumber+1),r={url:i[3],func:i[1]||vn,args:i[2]?i[2].split(","):[],line:i[4]?+i[4]:null,column:i[5]?+i[5]:null}}!r.func&&r.line&&(r.func=vn),e.push(r)}if(!e.length)return null;return{message:Tn(t),name:t.name,stack:e}}(t))return wn(n,i)}catch(t){}return{message:Tn(t),name:t&&t.name,stack:[],failed:!0}}function wn(t,n){try{return c(c({},t),{stack:t.stack.slice(n)})}catch(n){return t}}function Tn(t){var n=t&&t.message;return n?n.error&&"string"==typeof n.error.message?n.error.message:n:"No error message"}var En=50;function Sn(t){var n=_n(t.stack),i={type:t.name,value:t.message};return n&&n.length&&(i.stacktrace={frames:n}),void 0===i.type&&""===i.value&&(i.value="Unrecoverable error caught"),i}function xn(t){return{exception:{values:[Sn(t)]}}}function _n(t){if(!t||!t.length)return[];var n=t,i=n[0].func||"",r=n[n.length-1].func||"";return-1===i.indexOf("captureMessage")&&-1===i.indexOf("captureException")||(n=n.slice(1)),-1!==r.indexOf("sentryWrapped")&&(n=n.slice(0,-1)),n.slice(0,En).map(function(t){return{colno:null===t.column?void 0:t.column,filename:t.url||n[0].url,function:t.func||"?",in_app:!0,lineno:null===t.line?void 0:t.line}}).reverse()}function jn(t,n,i){var r,e;if(void 0===i&&(i={}),p(t)&&t.error)return r=xn(bn(t=t.error));if(m(t)||(e=t,"[object DOMException]"===Object.prototype.toString.call(e))){var o=t,u=o.name||(m(o)?"DOMError":"DOMException"),s=o.message?u+": "+o.message:u;return H(r=kn(s,n,i),s),"code"in o&&(r.tags=c(c({},r.tags),{"DOMException.code":""+o.code})),r}return l(t)?r=xn(bn(t)):b(t)||w(t)?(B(r=function(t,n,i){var r={exception:{values:[{type:w(t)?t.constructor.name:i?"UnhandledRejection":"Error",value:"Non-Error "+(i?"promise rejection":"exception")+" captured with keys: "+et(t)}]},extra:{__serialized__:tt(t)}};if(n){var e=_n(bn(n).stack);r.stacktrace={frames:e}}return r}(t,n,i.rejection),{synthetic:!0}),r):(H(r=kn(t,n,i),""+t,void 0),B(r,{synthetic:!0}),r)}function kn(t,n,i){void 0===i&&(i={});var r={message:t};if(i.attachStacktrace&&n){var e=_n(bn(n).stack);r.stacktrace={frames:e}}return r}var On={event:"error",transaction:"transaction",session:"session",attachment:"attachment"},Dn=function(){function n(t){this.options=t,this.O=new jt(30),this.Lt={},this.qt=new zt(t.dsn,t.Ft,t.tunnel),this.url=this.qt.getStoreEndpointWithUrlEncodedAuth()}return n.prototype.sendEvent=function(t){throw new k("Transport Class has to implement `sendEvent` method")},n.prototype.close=function(t){return this.O.drain(t)},n.prototype.Pt=function(n){var i=n.requestType,r=n.response,e=n.headers,o=n.resolve,u=n.reject,s=t.Status.fromHttpCode(r.status);this.Ut(e)&&$.warn("Too many "+i+" requests, backing off until: "+this.Ht(i)),s!==t.Status.Success?u(r):o({status:s})},n.prototype.Ht=function(t){var n=On[t];return this.Lt[n]||this.Lt.all},n.prototype.Bt=function(t){return this.Ht(t)>new Date(Date.now())},n.prototype.Ut=function(t){var n,i,r,e,o=Date.now(),u=t["x-sentry-rate-limits"],s=t["retry-after"];if(u){try{for(var a=h(u.trim().split(",")),c=a.next();!c.done;c=a.next()){var f=c.value.split(":",2),v=parseInt(f[0],10),d=1e3*(isNaN(v)?60:v);try{for(var l=(r=void 0,h(f[1].split(";"))),p=l.next();!p.done;p=l.next()){var m=p.value;this.Lt[m||"all"]=new Date(o+d)}}catch(t){r={error:t}}finally{try{p&&!p.done&&(e=l.return)&&e.call(l)}finally{if(r)throw r.error}}}}catch(t){n={error:t}}finally{try{c&&!c.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}return!0}return!!s&&(this.Lt.all=new Date(o+function(t,n){if(!n)return J;var i=parseInt(""+n,10);if(!isNaN(i))return 1e3*i;var r=Date.parse(""+n);return isNaN(r)?J:r-t}(o,s)),!0)},n}();var In=function(t){function n(n,i){void 0===i&&(i=function(){var t,n,i=L();if(st(i.fetch))return i.fetch.bind(i);var r=i.document,e=i.fetch;if("function"==typeof(null===(t=r)||void 0===t?void 0:t.createElement))try{var o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o),(null===(n=o.contentWindow)||void 0===n?void 0:n.fetch)&&(e=o.contentWindow.fetch),r.head.removeChild(o)}catch(t){$.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",t)}return e.bind(i)}());var r=t.call(this,n)||this;return r.Jt=i,r}return i(n,t),n.prototype.sendEvent=function(t){return this.Xt(on(t,this.qt),t)},n.prototype.sendSession=function(t){return this.Xt(en(t,this.qt),t)},n.prototype.Xt=function(t,n){var i=this;if(this.Bt(t.type))return Promise.reject({event:n,type:t.type,reason:"Transport for "+t.type+" requests locked till "+this.Ht(t.type)+" due to too many requests.",status:429});var r={body:t.body,method:"POST",referrerPolicy:at()?"origin":""};return void 0!==this.options.fetchParameters&&Object.assign(r,this.options.fetchParameters),void 0!==this.options.headers&&(r.headers=this.options.headers),this.O.add(function(){return new _t(function(n,e){i.Jt(t.url,r).then(function(r){var o={"x-sentry-rate-limits":r.headers.get("X-Sentry-Rate-Limits"),"retry-after":r.headers.get("Retry-After")};i.Pt({requestType:t.type,response:r,headers:o,resolve:n,reject:e})}).catch(e)})})},n}(Dn),Nn=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return i(n,t),n.prototype.sendEvent=function(t){return this.Xt(on(t,this.qt),t)},n.prototype.sendSession=function(t){return this.Xt(en(t,this.qt),t)},n.prototype.Xt=function(t,n){var i=this;return this.Bt(t.type)?Promise.reject({event:n,type:t.type,reason:"Transport for "+t.type+" requests locked till "+this.Ht(t.type)+" due to too many requests.",status:429}):this.O.add(function(){return new _t(function(n,r){var e=new XMLHttpRequest;for(var o in e.onreadystatechange=function(){if(4===e.readyState){var o={"x-sentry-rate-limits":e.getResponseHeader("X-Sentry-Rate-Limits"),"retry-after":e.getResponseHeader("Retry-After")};i.Pt({requestType:t.type,response:e,headers:o,resolve:n,reject:r})}},e.open("POST",t.url),i.options.headers)i.options.headers.hasOwnProperty(o)&&e.setRequestHeader(o,i.options.headers[o]);e.send(t.body)})})},n}(Dn),Rn=Object.freeze({__proto__:null,BaseTransport:Dn,FetchTransport:In,XHRTransport:Nn}),Cn=function(n){function r(){return null!==n&&n.apply(this,arguments)||this}return i(r,n),r.prototype.eventFromException=function(n,i){return function(n,i,r){var e=jn(i,r&&r.syntheticException||void 0,{attachStacktrace:n.attachStacktrace});return B(e,{handled:!0,type:"generic"}),e.level=t.Severity.Error,r&&r.event_id&&(e.event_id=r.event_id),_t.resolve(e)}(this.at,n,i)},r.prototype.eventFromMessage=function(n,i,r){return void 0===i&&(i=t.Severity.Info),function(n,i,r,e){void 0===r&&(r=t.Severity.Info);var o=kn(i,e&&e.syntheticException||void 0,{attachStacktrace:n.attachStacktrace});return o.level=r,e&&e.event_id&&(o.event_id=e.event_id),_t.resolve(o)}(this.at,n,i,r)},r.prototype.kt=function(){if(!this.at.dsn)return n.prototype.kt.call(this);var t=c(c({},this.at.transportOptions),{dsn:this.at.dsn,tunnel:this.at.tunnel,Ft:this.at.Ft});return this.at.transport?new this.at.transport(t):ut()?new In(t):new Nn(t)},r}(tn),Mn=0;function An(){return Mn>0}function Ln(t,n,i){if(void 0===n&&(n={}),"function"!=typeof t)return t;try{if(t.__sentry__)return t;if(t.__sentry_wrapped__)return t.__sentry_wrapped__}catch(n){return t}var sentryWrapped=function(){var r=Array.prototype.slice.call(arguments);try{i&&"function"==typeof i&&i.apply(this,arguments);var e=r.map(function(t){return Ln(t,n)});return t.handleEvent?t.handleEvent.apply(this,e):t.apply(this,e)}catch(t){throw Mn+=1,setTimeout(function(){Mn-=1}),$t(function(i){i.addEventProcessor(function(t){var i=c({},t);return n.mechanism&&(H(i,void 0,void 0),B(i,n.mechanism)),i.extra=c(c({},i.extra),{arguments:r}),i}),captureException(t)}),t}};try{for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(sentryWrapped[r]=t[r])}catch(t){}t.prototype=t.prototype||{},sentryWrapped.prototype=t.prototype,Object.defineProperty(t,"__sentry_wrapped__",{enumerable:!1,value:sentryWrapped}),Object.defineProperties(sentryWrapped,{__sentry__:{enumerable:!1,value:!0},__sentry_original__:{enumerable:!1,value:t}});try{Object.getOwnPropertyDescriptor(sentryWrapped,"name").configurable&&Object.defineProperty(sentryWrapped,"name",{get:function(){return t.name}})}catch(t){}return sentryWrapped}function qn(t){if(void 0===t&&(t={}),t.eventId)if(t.dsn){var n=document.createElement("script");n.async=!0,n.src=new zt(t.dsn).getReportDialogEndpoint(t),t.onLoad&&(n.onload=t.onLoad),(document.head||document.body).appendChild(n)}else $.error("Missing dsn option in showReportDialog call");else $.error("Missing eventId option in showReportDialog call")}var Fn=function(){function n(t){this.name=n.id,this.Gt=!1,this.Wt=!1,this.at=c({onerror:!0,onunhandledrejection:!0},t)}return n.prototype.setupOnce=function(){Error.stackTraceLimit=50,this.at.onerror&&($.log("Global Handler attached: onerror"),this.$t()),this.at.onunhandledrejection&&($.log("Global Handler attached: onunhandledrejection"),this.zt())},n.prototype.$t=function(){var t=this;this.Gt||(lt({callback:function(i){var r=i.error,e=Bt(),o=e.getIntegration(n),u=r&&!0===r.__sentry_own_request__;if(o&&!An()&&!u){var s=e.getClient(),a=void 0===r&&y(i.msg)?t.Kt(i.msg,i.url,i.line,i.column):t.Vt(jn(r||i.msg,void 0,{attachStacktrace:s&&s.getOptions().attachStacktrace,rejection:!1}),i.url,i.line,i.column);B(a,{handled:!1,type:"onerror"}),e.captureEvent(a,{originalException:r})}},type:"error"}),this.Gt=!0)},n.prototype.zt=function(){var i=this;this.Wt||(lt({callback:function(r){var e=r;try{"reason"in r?e=r.reason:"detail"in r&&"reason"in r.detail&&(e=r.detail.reason)}catch(t){}var o=Bt(),u=o.getIntegration(n),s=e&&!0===e.__sentry_own_request__;if(!u||An()||s)return!0;var a=o.getClient(),c=g(e)?i.Yt(e):jn(e,void 0,{attachStacktrace:a&&a.getOptions().attachStacktrace,rejection:!0});c.level=t.Severity.Error,B(c,{handled:!1,type:"onunhandledrejection"}),o.captureEvent(c,{originalException:e})},type:"unhandledrejection"}),this.Wt=!0)},n.prototype.Kt=function(t,n,i,r){var e,o=p(t)?t.message:t,u=o.match(/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i);u&&(e=u[1],o=u[2]);var s={exception:{values:[{type:e||"Error",value:o}]}};return this.Vt(s,n,i,r)},n.prototype.Yt=function(t){return{exception:{values:[{type:"UnhandledRejection",value:"Non-Error promise rejection captured with value: "+String(t)}]}}},n.prototype.Vt=function(t,n,i,r){t.exception=t.exception||{},t.exception.values=t.exception.values||[],t.exception.values[0]=t.exception.values[0]||{},t.exception.values[0].stacktrace=t.exception.values[0].stacktrace||{},t.exception.values[0].stacktrace.frames=t.exception.values[0].stacktrace.frames||[];var e=isNaN(parseInt(r,10))?void 0:r,o=isNaN(parseInt(i,10))?void 0:i,u=y(n)&&n.length>0?n:function(){try{return document.location.href}catch(t){return""}}();return 0===t.exception.values[0].stacktrace.frames.length&&t.exception.values[0].stacktrace.frames.push({colno:e,filename:u,function:"?",in_app:!0,lineno:o}),t},n.id="GlobalHandlers",n}(),Pn=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Un=function(){function t(n){this.name=t.id,this.at=c({XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0},n)}return t.prototype.setupOnce=function(){var t=L();(this.at.setTimeout&&Y(t,"setTimeout",this.Qt.bind(this)),this.at.setInterval&&Y(t,"setInterval",this.Qt.bind(this)),this.at.requestAnimationFrame&&Y(t,"requestAnimationFrame",this.Zt.bind(this)),this.at.XMLHttpRequest&&"XMLHttpRequest"in t&&Y(XMLHttpRequest.prototype,"send",this.tn.bind(this)),this.at.eventTarget)&&(Array.isArray(this.at.eventTarget)?this.at.eventTarget:Pn).forEach(this.nn.bind(this))},t.prototype.Qt=function(t){return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var r=n[0];return n[0]=Ln(r,{mechanism:{data:{function:V(t)},handled:!0,type:"instrument"}}),t.apply(this,n)}},t.prototype.Zt=function(t){return function(n){return t.call(this,Ln(n,{mechanism:{data:{function:"requestAnimationFrame",handler:V(t)},handled:!0,type:"instrument"}}))}},t.prototype.nn=function(t){var n=L(),i=n[t]&&n[t].prototype;i&&i.hasOwnProperty&&i.hasOwnProperty("addEventListener")&&(Y(i,"addEventListener",function(n){return function(i,r,e){try{"function"==typeof r.handleEvent&&(r.handleEvent=Ln(r.handleEvent.bind(r),{mechanism:{data:{function:"handleEvent",handler:V(r),target:t},handled:!0,type:"instrument"}}))}catch(t){}return n.call(this,i,Ln(r,{mechanism:{data:{function:"addEventListener",handler:V(r),target:t},handled:!0,type:"instrument"}}),e)}}),Y(i,"removeEventListener",function(t){return function(n,i,r){var e,o=i;try{var u=null===(e=o)||void 0===e?void 0:e.__sentry_wrapped__;u&&t.call(this,n,u,r)}catch(t){}return t.call(this,n,o,r)}}))},t.prototype.tn=function(t){return function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var r=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(function(t){t in r&&"function"==typeof r[t]&&Y(r,t,function(n){var i={mechanism:{data:{function:t,handler:V(n)},handled:!0,type:"instrument"}};return n.__sentry_original__&&(i.mechanism.data.handler=V(n.__sentry_original__)),Ln(n,i)})}),t.apply(this,n)}},t.id="TryCatch",t}(),Hn=function(){function n(t){this.name=n.id,this.at=c({console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0},t)}return n.prototype.addSentryBreadcrumb=function(t){this.at.sentry&&Bt().addBreadcrumb({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:P(t)},{event:t})},n.prototype.setupOnce=function(){var t=this;this.at.console&&lt({callback:function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];t.in.apply(t,d(n))},type:"console"}),this.at.dom&&lt({callback:function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];t.rn.apply(t,d(n))},type:"dom"}),this.at.xhr&&lt({callback:function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];t.en.apply(t,d(n))},type:"xhr"}),this.at.fetch&&lt({callback:function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];t.on.apply(t,d(n))},type:"fetch"}),this.at.history&&lt({callback:function(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];t.un.apply(t,d(n))},type:"history"})},n.prototype.in=function(n){var i={category:"console",data:{arguments:n.args,logger:"console"},level:t.Severity.fromString(n.level),message:C(n.args," ")};if("assert"===n.level){if(!1!==n.args[0])return;i.message="Assertion failed: "+(C(n.args.slice(1)," ")||"console.assert"),i.data.arguments=n.args.slice(1)}Bt().addBreadcrumb(i,{input:n.args,level:n.level})},n.prototype.rn=function(t){var n,i="object"==typeof this.at.dom?this.at.dom.serializeAttribute:void 0;"string"==typeof i&&(i=[i]);try{n=t.event.target?x(t.event.target,i):x(t.event,i)}catch(t){n="<unknown>"}0!==n.length&&Bt().addBreadcrumb({category:"ui."+t.name,message:n},{event:t.event,name:t.name,global:t.global})},n.prototype.en=function(t){if(t.endTimestamp){if(t.xhr.__sentry_own_request__)return;var n=t.xhr.__sentry_xhr__||{},i=n.method,r=n.url,e=n.status_code,o=n.body;Bt().addBreadcrumb({category:"xhr",data:{method:i,url:r,status_code:e},type:"http"},{xhr:t.xhr,input:o})}else;},n.prototype.on=function(n){n.endTimestamp&&(n.fetchData.url.match(/sentry_key/)&&"POST"===n.fetchData.method||(n.error?Bt().addBreadcrumb({category:"fetch",data:n.fetchData,level:t.Severity.Error,type:"http"},{data:n.error,input:n.args}):Bt().addBreadcrumb({category:"fetch",data:c(c({},n.fetchData),{status_code:n.response.status}),type:"http"},{input:n.args,response:n.response})))},n.prototype.un=function(t){var n=L(),i=t.from,r=t.to,e=F(n.location.href),o=F(i),u=F(r);o.path||(o=e),e.protocol===u.protocol&&e.host===u.host&&(r=u.relative),e.protocol===o.protocol&&e.host===o.host&&(i=o.relative),Bt().addBreadcrumb({category:"navigation",data:{from:i,to:r}})},n.id="Breadcrumbs",n}(),Bn="cause",Jn=5,Xn=function(){function t(n){void 0===n&&(n={}),this.name=t.id,this.sn=n.key||Bn,this.k=n.limit||Jn}return t.prototype.setupOnce=function(){Lt(function(n,i){var r=Bt().getIntegration(t);return r?r.an(n,i):n})},t.prototype.an=function(t,n){if(!(t.exception&&t.exception.values&&n&&S(n.originalException,Error)))return t;var i=this.cn(n.originalException,this.sn);return t.exception.values=d(i,t.exception.values),t},t.prototype.cn=function(t,n,i){if(void 0===i&&(i=[]),!S(t[n],Error)||i.length+1>=this.k)return i;var r=Sn(bn(t[n]));return this.cn(t[n],n,d([r],i))},t.id="LinkedErrors",t}(),Gn=L(),Wn=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(){Lt(function(n){var i,r,e;if(Bt().getIntegration(t)){if(!Gn.navigator&&!Gn.location&&!Gn.document)return n;var o=(null===(i=n.request)||void 0===i?void 0:i.url)||(null===(r=Gn.location)||void 0===r?void 0:r.href),u=(Gn.document||{}).referrer,s=(Gn.navigator||{}).userAgent,a=c(c(c({},null===(e=n.request)||void 0===e?void 0:e.headers),u&&{Referer:u}),s&&{"User-Agent":s}),f=c(c({},o&&{url:o}),{headers:a});return c(c({},n),{request:f})}return n})},t.id="UserAgent",t}(),$n=function(){function t(){this.name=t.id}return t.prototype.setupOnce=function(n,i){n(function(n){var r=i().getIntegration(t);if(r){try{if(r.Dt(n,r.fn))return null}catch(t){return r.fn=n}return r.fn=n}return n})},t.prototype.Dt=function(t,n){return!!n&&(!!this.hn(t,n)||!!this.vn(t,n))},t.prototype.hn=function(t,n){var i=t.message,r=n.message;return!(!i&&!r)&&(!(i&&!r||!i&&r)&&(i===r&&(!!this.dn(t,n)&&!!this.ln(t,n))))},t.prototype.pn=function(t){var n=t.exception;if(n)try{return n.values[0].stacktrace.frames}catch(t){return}else if(t.stacktrace)return t.stacktrace.frames},t.prototype.ln=function(t,n){var i=this.pn(t),r=this.pn(n);if(!i&&!r)return!0;if(i&&!r||!i&&r)return!1;if(i=i,(r=r).length!==i.length)return!1;for(var e=0;e<r.length;e++){var o=r[e],u=i[e];if(o.filename!==u.filename||o.lineno!==u.lineno||o.colno!==u.colno||o.function!==u.function)return!1}return!0},t.prototype.mn=function(t){return t.exception&&t.exception.values&&t.exception.values[0]},t.prototype.vn=function(t,n){var i=this.mn(n),r=this.mn(t);return!(!i||!r)&&(i.type===r.type&&i.value===r.value&&(!!this.dn(t,n)&&!!this.ln(t,n)))},t.prototype.dn=function(t,n){var i=t.fingerprint,r=n.fingerprint;if(!i&&!r)return!0;if(i&&!r||!i&&r)return!1;i=i,r=r;try{return!(i.join("")!==r.join(""))}catch(t){return!1}},t.id="Dedupe",t}(),zn=Object.freeze({__proto__:null,GlobalHandlers:Fn,TryCatch:Un,Breadcrumbs:Hn,LinkedErrors:Xn,UserAgent:Wn,Dedupe:$n}),Kn=function(t){function n(n){void 0===n&&(n={});return n.Ft=n.Ft||{},n.Ft.sdk=n.Ft.sdk||{name:"sentry.javascript.browser",packages:[{name:"npm:@sentry/browser",version:sn}],version:sn},t.call(this,Cn,n)||this}return i(n,t),n.prototype.showReportDialog=function(t){void 0===t&&(t={}),L().document&&(this.dt()?qn(c(c({},t),{dsn:t.dsn||this.getDsn()})):$.error("Trying to call showReportDialog with Sentry Client disabled"))},n.prototype.gt=function(n,i,r){return n.platform=n.platform||"javascript",t.prototype.gt.call(this,n,i,r)},n.prototype.St=function(n){var i=this.getIntegration(Hn);i&&i.addSentryBreadcrumb(n),t.prototype.St.call(this,n)},n}(Qt),Vn=[new fn,new an,new Un,new Hn,new Fn,new Xn,new $n,new Wn];var Yn={},Qn=L();Qn.Sentry&&Qn.Sentry.Integrations&&(Yn=Qn.Sentry.Integrations);var Zn,ti=c(c(c({},Yn),hn),zn);!function(t){t.Ok="ok",t.DeadlineExceeded="deadline_exceeded",t.Unauthenticated="unauthenticated",t.PermissionDenied="permission_denied",t.NotFound="not_found",t.ResourceExhausted="resource_exhausted",t.InvalidArgument="invalid_argument",t.Unimplemented="unimplemented",t.Unavailable="unavailable",t.InternalError="internal_error",t.UnknownError="unknown_error",t.Cancelled="cancelled",t.AlreadyExists="already_exists",t.FailedPrecondition="failed_precondition",t.Aborted="aborted",t.OutOfRange="out_of_range",t.DataLoss="data_loss"}(Zn||(Zn={})),function(t){t.fromHttpCode=function(n){if(n<400)return t.Ok;if(n>=400&&n<500)switch(n){case 401:return t.Unauthenticated;case 403:return t.PermissionDenied;case 404:return t.NotFound;case 409:return t.AlreadyExists;case 413:return t.FailedPrecondition;case 429:return t.ResourceExhausted;default:return t.InvalidArgument}if(n>=500&&n<600)switch(n){case 501:return t.Unimplemented;case 503:return t.Unavailable;case 504:return t.DeadlineExceeded;default:return t.InternalError}return t.UnknownError}}(Zn||(Zn={}));var ni=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function ii(t){var n;return void 0===t&&(t=null===(n=Bt().getClient())||void 0===n?void 0:n.getOptions()),!!t&&("tracesSampleRate"in t||"tracesSampler"in t)}function ri(t){var n,i;return void 0===t&&(t=Bt()),null===(i=null===(n=t)||void 0===n?void 0:n.getScope())||void 0===i?void 0:i.getTransaction()}function ei(t){return t/1e3}function oi(){var t=ri();t&&($.log("[Tracing] Transaction: "+Zn.InternalError+" -> Global error occured"),t.setStatus(Zn.InternalError))}var ui=function(){function t(t){void 0===t&&(t=1e3),this.spans=[],this.yn=t}return t.prototype.add=function(t){this.spans.length>this.yn?t.spanRecorder=void 0:this.spans.push(t)},t}(),si=function(){function t(t){if(this.traceId=q(),this.spanId=q().substring(16),this.startTimestamp=Rt(),this.tags={},this.data={},!t)return this;t.traceId&&(this.traceId=t.traceId),t.spanId&&(this.spanId=t.spanId),t.parentSpanId&&(this.parentSpanId=t.parentSpanId),"sampled"in t&&(this.sampled=t.sampled),t.op&&(this.op=t.op),t.description&&(this.description=t.description),t.data&&(this.data=t.data),t.tags&&(this.tags=t.tags),t.status&&(this.status=t.status),t.startTimestamp&&(this.startTimestamp=t.startTimestamp),t.endTimestamp&&(this.endTimestamp=t.endTimestamp)}return t.prototype.child=function(t){return this.startChild(t)},t.prototype.startChild=function(n){var i=new t(c(c({},n),{parentSpanId:this.spanId,sampled:this.sampled,traceId:this.traceId}));return i.spanRecorder=this.spanRecorder,i.spanRecorder&&i.spanRecorder.add(i),i.transaction=this.transaction,i},t.prototype.setTag=function(t,n){var i;return this.tags=c(c({},this.tags),((i={})[t]=n,i)),this},t.prototype.setData=function(t,n){var i;return this.data=c(c({},this.data),((i={})[t]=n,i)),this},t.prototype.setStatus=function(t){return this.status=t,this},t.prototype.setHttpStatus=function(t){this.setTag("http.status_code",String(t));var n=Zn.fromHttpCode(t);return n!==Zn.UnknownError&&this.setStatus(n),this},t.prototype.isSuccess=function(){return this.status===Zn.Ok},t.prototype.finish=function(t){this.endTimestamp="number"==typeof t?t:Rt()},t.prototype.toTraceparent=function(){var t="";return void 0!==this.sampled&&(t=this.sampled?"-1":"-0"),this.traceId+"-"+this.spanId+t},t.prototype.toContext=function(){return ot({data:this.data,description:this.description,endTimestamp:this.endTimestamp,op:this.op,parentSpanId:this.parentSpanId,sampled:this.sampled,spanId:this.spanId,startTimestamp:this.startTimestamp,status:this.status,tags:this.tags,traceId:this.traceId})},t.prototype.updateWithContext=function(t){var n,i,r,e,o;return this.data=null!=(n=t.data)?n:{},this.description=t.description,this.endTimestamp=t.endTimestamp,this.op=t.op,this.parentSpanId=t.parentSpanId,this.sampled=t.sampled,this.spanId=null!=(i=t.spanId)?i:this.spanId,this.startTimestamp=null!=(r=t.startTimestamp)?r:this.startTimestamp,this.status=t.status,this.tags=null!=(e=t.tags)?e:{},this.traceId=null!=(o=t.traceId)?o:this.traceId,this},t.prototype.getTraceContext=function(){return ot({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,trace_id:this.traceId})},t.prototype.toJSON=function(){return ot({data:Object.keys(this.data).length>0?this.data:void 0,description:this.description,op:this.op,parent_span_id:this.parentSpanId,span_id:this.spanId,start_timestamp:this.startTimestamp,status:this.status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this.endTimestamp,trace_id:this.traceId})},t}(),ai=function(t){function n(n,i){var r=t.call(this,n)||this;return r.gn={},r.bn=Bt(),S(i,Pt)&&(r.bn=i),r.name=n.name||"",r.metadata=n.metadata||{},r.wn=n.trimEnd,r.transaction=r,r}return i(n,t),n.prototype.setName=function(t){this.name=t},n.prototype.initSpanRecorder=function(t){void 0===t&&(t=1e3),this.spanRecorder||(this.spanRecorder=new ui(t)),this.spanRecorder.add(this)},n.prototype.setMeasurements=function(t){this.gn=c({},t)},n.prototype.setMetadata=function(t){this.metadata=c(c({},this.metadata),t)},n.prototype.finish=function(n){var i=this;if(void 0===this.endTimestamp){if(this.name||($.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this.name="<unlabeled transaction>"),t.prototype.finish.call(this,n),!0===this.sampled){var r=this.spanRecorder?this.spanRecorder.spans.filter(function(t){return t!==i&&t.endTimestamp}):[];this.wn&&r.length>0&&(this.endTimestamp=r.reduce(function(t,n){return t.endTimestamp&&n.endTimestamp?t.endTimestamp>n.endTimestamp?t:n:t}).endTimestamp);var e={contexts:{trace:this.getTraceContext()},spans:r,start_timestamp:this.startTimestamp,tags:this.tags,timestamp:this.endTimestamp,transaction:this.name,type:"transaction",debug_meta:this.metadata};return Object.keys(this.gn).length>0&&($.log("[Measurements] Adding measurements to transaction",JSON.stringify(this.gn,void 0,2)),e.measurements=this.gn),$.log("[Tracing] Finishing "+this.op+" transaction: "+this.name+"."),this.bn.captureEvent(e)}$.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled.")}},n.prototype.toContext=function(){var n=t.prototype.toContext.call(this);return ot(c(c({},n),{name:this.name,trimEnd:this.wn}))},n.prototype.updateWithContext=function(n){var i;return t.prototype.updateWithContext.call(this,n),this.name=null!=(i=n.name)?i:"",this.wn=n.trimEnd,this},n}(si),ci=1e3,fi=function(t){function n(n,i,r,e){void 0===r&&(r="");var o=t.call(this,e)||this;return o.Tn=n,o.En=i,o.transactionSpanId=r,o}return i(n,t),n.prototype.add=function(n){var i=this;n.spanId!==this.transactionSpanId&&(n.finish=function(t){n.endTimestamp="number"==typeof t?t:Rt(),i.En(n.spanId)},void 0===n.endTimestamp&&this.Tn(n.spanId)),t.prototype.add.call(this,n)},n}(ui),hi=function(t){function n(n,i,r,e){void 0===r&&(r=ci),void 0===e&&(e=!1);var o=t.call(this,n,i)||this;return o.Sn=i,o.xn=r,o._n=e,o.activities={},o.jn=0,o.kn=0,o.On=!1,o.Dn=[],i&&e&&(vi(i),$.log("Setting idle transaction on scope. Span ID: "+o.spanId),i.configureScope(function(t){return t.setSpan(o)})),o.In=setTimeout(function(){o.On||o.finish()},o.xn),o}return i(n,t),n.prototype.finish=function(n){var i,r,e=this;if(void 0===n&&(n=Rt()),this.On=!0,this.activities={},this.spanRecorder){$.log("[Tracing] finishing IdleTransaction",new Date(1e3*n).toISOString(),this.op);try{for(var o=h(this.Dn),u=o.next();!u.done;u=o.next()){(0,u.value)(this,n)}}catch(t){i={error:t}}finally{try{u&&!u.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}this.spanRecorder.spans=this.spanRecorder.spans.filter(function(t){if(t.spanId===e.spanId)return!0;t.endTimestamp||(t.endTimestamp=n,t.setStatus(Zn.Cancelled),$.log("[Tracing] cancelling span since transaction ended early",JSON.stringify(t,void 0,2)));var i=t.startTimestamp<n;return i||$.log("[Tracing] discarding Span since it happened after Transaction was finished",JSON.stringify(t,void 0,2)),i}),$.log("[Tracing] flushing IdleTransaction")}else $.log("[Tracing] No active IdleTransaction");return this._n&&vi(this.Sn),t.prototype.finish.call(this,n)},n.prototype.registerBeforeFinishCallback=function(t){this.Dn.push(t)},n.prototype.initSpanRecorder=function(t){var n=this;if(!this.spanRecorder){this.spanRecorder=new fi(function(t){n.On||n.Tn(t)},function(t){n.On||n.En(t)},this.spanId,t),$.log("Starting heartbeat"),this.Nn()}this.spanRecorder.add(this)},n.prototype.Tn=function(t){this.In&&(clearTimeout(this.In),this.In=void 0),$.log("[Tracing] pushActivity: "+t),this.activities[t]=!0,$.log("[Tracing] new activities count",Object.keys(this.activities).length)},n.prototype.En=function(t){var n=this;if(this.activities[t]&&($.log("[Tracing] popActivity "+t),delete this.activities[t],$.log("[Tracing] new activities count",Object.keys(this.activities).length)),0===Object.keys(this.activities).length){var i=this.xn,r=Rt()+i/1e3;setTimeout(function(){n.On||n.finish(r)},i)}},n.prototype.Rn=function(){if(clearTimeout(this.jn),!this.On){var t=Object.keys(this.activities),n=t.length?t.reduce(function(t,n){return t+n}):"";n===this.Cn?this.kn+=1:this.kn=1,this.Cn=n,this.kn>=3?($.log("[Tracing] Transaction finished because of no change for 3 heart beats"),this.setStatus(Zn.DeadlineExceeded),this.setTag("heartbeat","failed"),this.finish()):this.Nn()}},n.prototype.Nn=function(){var t=this;$.log("pinging Heartbeat -> current counter: "+this.kn),this.jn=setTimeout(function(){t.Rn()},5e3)},n}(ai);function vi(t){if(t){var n=t.getScope();if(n)n.getTransaction()&&n.setSpan(void 0)}}function di(){var t=this.getScope();if(t){var n=t.getSpan();if(n)return{"sentry-trace":n.toTraceparent()}}return{}}function li(t,n,i){return ii()?void 0!==t.sampled?(t.setMetadata({transactionSampling:{method:a.Explicit}}),t):("function"==typeof n.tracesSampler?(r=n.tracesSampler(i),t.setMetadata({transactionSampling:{method:a.Sampler,rate:Number(r)}})):void 0!==i.parentSampled?(r=i.parentSampled,t.setMetadata({transactionSampling:{method:a.Inheritance}})):(r=n.tracesSampleRate,t.setMetadata({transactionSampling:{method:a.Rate,rate:Number(r)}})),function(t){if(isNaN(t)||"number"!=typeof t&&"boolean"!=typeof t)return $.warn("[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got "+JSON.stringify(t)+" of type "+JSON.stringify(typeof t)+"."),!1;if(t<0||t>1)return $.warn("[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got "+t+"."),!1;return!0}(r)?r?(t.sampled=Math.random()<r,t.sampled?($.log("[Tracing] starting "+t.op+" transaction - "+t.name),t):($.log("[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = "+Number(r)+")"),t)):($.log("[Tracing] Discarding transaction because "+("function"==typeof n.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),t.sampled=!1,t):($.warn("[Tracing] Discarding transaction because of invalid sample rate."),t.sampled=!1,t)):(t.sampled=!1,t);var r}function pi(t,n){var i,r,e=(null===(i=this.getClient())||void 0===i?void 0:i.getOptions())||{},o=new ai(t,this);return(o=li(o,e,c({parentSampled:t.parentSampled,transactionContext:t},n))).sampled&&o.initSpanRecorder(null===(r=e.Et)||void 0===r?void 0:r.maxSpans),o}function mi(){var t=Ut();if(t.__SENTRY__){var n={mongodb:function(){return new(N(module,"./integrations/mongo").Mongo)},mongoose:function(){return new(N(module,"./integrations/mongo").Mongo)({mongoose:!0})},mysql:function(){return new(N(module,"./integrations/mysql").Mysql)},pg:function(){return new(N(module,"./integrations/postgres").Postgres)}},i=Object.keys(n).filter(function(t){return!!function(t){var n;try{n=N(module,t)}catch(t){}try{var i=N(module,"process").cwd;n=N(module,i()+"/node_modules/"+t)}catch(t){}return n}(t)}).map(function(t){try{return n[t]()}catch(t){return}}).filter(function(t){return t});i.length>0&&(t.__SENTRY__.integrations=d(t.__SENTRY__.integrations||[],i))}}function yi(){var t;(t=Ut()).__SENTRY__&&(t.__SENTRY__.extensions=t.__SENTRY__.extensions||{},t.__SENTRY__.extensions.startTransaction||(t.__SENTRY__.extensions.startTransaction=pi),t.__SENTRY__.extensions.traceHeaders||(t.__SENTRY__.extensions.traceHeaders=di)),I()&&mi(),lt({callback:oi,type:"error"}),lt({callback:oi,type:"unhandledrejection"})}var gi=L();var bi=function(t,n,i){var r;return function(e){n.value>=0&&(e||i)&&(n.delta=n.value-(r||0),(n.delta||void 0===r)&&(r=n.value,t(n)))}},wi=function(t,n){return{name:t,value:null!=n?n:-1,delta:0,entries:[],id:"v2-"+Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12)}},Ti=function(t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){if("first-input"===t&&!("PerformanceEventTiming"in self))return;var i=new PerformanceObserver(function(t){return t.getEntries().map(n)});return i.observe({type:t,buffered:!0}),i}}catch(t){}},Ei=function(t,n){var i=function(r){"pagehide"!==r.type&&"hidden"!==document.visibilityState||(t(r),n&&(removeEventListener("visibilitychange",i,!0),removeEventListener("pagehide",i,!0)))};addEventListener("visibilitychange",i,!0),addEventListener("pagehide",i,!0)},Si=-1,xi=function(){return Si<0&&(Si="hidden"===document.visibilityState?0:1/0,Ei(function(t){var n=t.timeStamp;Si=n},!0)),{get firstHiddenTime(){return Si}}},_i={},ji=L(),ki=function(){function t(){var t;this.gn={},this.Mn=0,!I()&&(null===(t=ji)||void 0===t?void 0:t.performance)&&(ji.performance.mark&&ji.performance.mark("sentry-tracing-init"),this.An(),this.Ln(),this.qn())}return t.prototype.addPerformanceEntries=function(t){var n=this;if(ji&&ji.performance&&ji.performance.getEntries&&Ct){$.log("[Tracing] Adding & adjusting spans using Performance API");var i,r,e,o,u,s=ei(Ct);if(ji.document&&ji.document.scripts)for(var a=0;a<ji.document.scripts.length;a++)if("true"===ji.document.scripts[a].dataset.entry){i=ji.document.scripts[a].src;break}if(ji.performance.getEntries().slice(this.Mn).forEach(function(a){var c=ei(a.startTime),f=ei(a.duration);if(!("navigation"===t.op&&s+c<t.startTimestamp))switch(a.entryType){case"navigation":!function(t,n,i){Oi({transaction:t,entry:n,event:"unloadEvent",timeOrigin:i}),Oi({transaction:t,entry:n,event:"redirect",timeOrigin:i}),Oi({transaction:t,entry:n,event:"domContentLoadedEvent",timeOrigin:i}),Oi({transaction:t,entry:n,event:"loadEvent",timeOrigin:i}),Oi({transaction:t,entry:n,event:"connect",timeOrigin:i}),Oi({transaction:t,entry:n,event:"secureConnection",timeOrigin:i,eventEnd:"connectEnd",description:"TLS/SSL"}),Oi({transaction:t,entry:n,event:"fetch",timeOrigin:i,eventEnd:"domainLookupStart",description:"cache"}),Oi({transaction:t,entry:n,event:"domainLookup",timeOrigin:i,description:"DNS"}),function(t,n,i){Di(t,{op:"browser",description:"request",startTimestamp:i+ei(n.requestStart),endTimestamp:i+ei(n.responseEnd)}),Di(t,{op:"browser",description:"response",startTimestamp:i+ei(n.responseStart),endTimestamp:i+ei(n.responseEnd)})}(t,n,i)}(t,a,s),o=s+ei(a.responseStart),u=s+ei(a.requestStart);break;case"mark":case"paint":case"measure":var h=function(t,n,i,r,e){var o=e+i,u=o+r;return Di(t,{description:n.name,endTimestamp:u,op:n.entryType,startTimestamp:o}),o}(t,a,c,f,s);void 0===e&&"sentry-tracing-init"===a.name&&(e=h);var v=xi(),d=a.startTime<v.firstHiddenTime;"first-paint"===a.name&&d&&($.log("[Measurements] Adding FP"),n.gn.fp={value:a.startTime},n.gn["mark.fp"]={value:h}),"first-contentful-paint"===a.name&&d&&($.log("[Measurements] Adding FCP"),n.gn.fcp={value:a.startTime},n.gn["mark.fcp"]={value:h});break;case"resource":var l=a.name.replace(window.location.origin,""),p=function(t,n,i,r,e,o){if("xmlhttprequest"===n.initiatorType||"fetch"===n.initiatorType)return;var u={};"transferSize"in n&&(u["Transfer Size"]=n.transferSize);"encodedBodySize"in n&&(u["Encoded Body Size"]=n.encodedBodySize);"decodedBodySize"in n&&(u["Decoded Body Size"]=n.decodedBodySize);var s=o+r,a=s+e;return Di(t,{description:i,endTimestamp:a,op:n.initiatorType?"resource."+n.initiatorType:"resource",startTimestamp:s,data:u}),a}(t,a,l,c,f,s);void 0===r&&(i||"").indexOf(l)>-1&&(r=p)}}),void 0!==r&&void 0!==e&&Di(t,{description:"evaluation",endTimestamp:e,op:"script",startTimestamp:r}),this.Mn=Math.max(performance.getEntries().length-1,0),this.Fn(t),"pageload"===t.op){var c=ei(Ct);"number"==typeof o&&($.log("[Measurements] Adding TTFB"),this.gn.ttfb={value:1e3*(o-t.startTimestamp)},"number"==typeof u&&u<=o&&(this.gn["ttfb.requestTime"]={value:1e3*(o-u)})),["fcp","fp","lcp"].forEach(function(i){if(n.gn[i]&&!(c>=t.startTimestamp)){var r=n.gn[i].value,e=c+ei(r),o=Math.abs(1e3*(e-t.startTimestamp)),u=o-r;$.log("[Measurements] Normalized "+i+" from "+r+" to "+o+" ("+u+")"),n.gn[i].value=o}}),this.gn["mark.fid"]&&this.gn.fid&&Di(t,{description:"first input delay",endTimestamp:this.gn["mark.fid"].value+ei(this.gn.fid.value),op:"web.vitals",startTimestamp:this.gn["mark.fid"].value}),"fcp"in this.gn||delete this.gn.cls,t.setMeasurements(this.gn),this.Pn(t)}}},t.prototype.Pn=function(t){this.Un&&($.log("[Measurements] Adding LCP Data"),this.Un.element&&t.setTag("lcp.element",x(this.Un.element)),this.Un.id&&t.setTag("lcp.id",this.Un.id),this.Un.url&&t.setTag("lcp.url",this.Un.url.trim().slice(0,200)),t.setTag("lcp.size",this.Un.size)),this.Hn&&this.Hn.sources&&($.log("[Measurements] Adding CLS Data"),this.Hn.sources.forEach(function(n,i){return t.setTag("cls.source."+(i+1),x(n.node))}))},t.prototype.An=function(){var t,n,i,r,e,o,u,s,a=this;t=function(t){var n=t.entries.pop();n&&($.log("[Measurements] Adding CLS"),a.gn.cls={value:t.value},a.Hn=n)},r=wi("CLS",0),e=0,o=[],(s=Ti("layout-shift",u=function(t){if(t&&!t.hadRecentInput){var n=o[0],u=o[o.length-1];e&&0!==o.length&&t.startTime-u.startTime<1e3&&t.startTime-n.startTime<5e3?(e+=t.value,o.push(t)):(e=t.value,o=[t]),e>r.value&&(r.value=e,r.entries=o,i&&i())}}))&&(i=bi(t,r,n),Ei(function(){s.takeRecords().map(u),i(!0)}))},t.prototype.Fn=function(t){var n=ji.navigator;if(n){var i=n.connection;i&&(i.effectiveType&&t.setTag("effectiveConnectionType",i.effectiveType),i.type&&t.setTag("connectionType",i.type),Ii(i.rtt)&&(this.gn["connection.rtt"]={value:i.rtt}),Ii(i.downlink)&&(this.gn["connection.downlink"]={value:i.downlink})),Ii(n.deviceMemory)&&t.setTag("deviceMemory",String(n.deviceMemory)),Ii(n.hardwareConcurrency)&&t.setTag("hardwareConcurrency",String(n.hardwareConcurrency))}},t.prototype.Ln=function(){var t=this;!function(t,n){var i,r=xi(),e=wi("LCP"),o=function(t){var n=t.startTime;n<r.firstHiddenTime&&(e.value=n,e.entries.push(t)),i&&i()},u=Ti("largest-contentful-paint",o);if(u){i=bi(t,e,n);var s=function(){_i[e.id]||(u.takeRecords().map(o),u.disconnect(),_i[e.id]=!0,i(!0))};["keydown","click"].forEach(function(t){addEventListener(t,s,{once:!0,capture:!0})}),Ei(s,!0)}}(function(n){var i=n.entries.pop();if(i){var r=ei(Ct),e=ei(i.startTime);$.log("[Measurements] Adding LCP"),t.gn.lcp={value:n.value},t.gn["mark.lcp"]={value:r+e},t.Un=i}})},t.prototype.qn=function(){var t,n,i,r,e,o,u,s=this;t=function(t){var n=t.entries.pop();if(n){var i=ei(Ct),r=ei(n.startTime);$.log("[Measurements] Adding FID"),s.gn.fid={value:t.value},s.gn["mark.fid"]={value:i+r}}},r=xi(),e=wi("FID"),(u=Ti("first-input",o=function(t){i&&t.startTime<r.firstHiddenTime&&(e.value=t.processingStart-t.startTime,e.entries.push(t),i(!0))}))&&(i=bi(t,e,n),Ei(function(){u.takeRecords().map(o),u.disconnect()},!0))},t}();function Oi(t){var n=t.transaction,i=t.entry,r=t.event,e=t.timeOrigin,o=t.eventEnd,u=t.description,s=o?i[o]:i[r+"End"],a=i[r+"Start"];a&&s&&Di(n,{op:"browser",description:null!=u?u:r,startTimestamp:e+ei(a),endTimestamp:e+ei(s)})}function Di(t,n){var i=n.startTimestamp,r=f(n,["startTimestamp"]);return i&&t.startTimestamp>i&&(t.startTimestamp=i),t.startChild(c({startTimestamp:i},r))}function Ii(t){return"number"==typeof t&&isFinite(t)}var Ni={traceFetch:!0,traceXHR:!0,tracingOrigins:["localhost",/^\//]};function Ri(t){var n=c(c({},Ni),t),i=n.traceFetch,r=n.traceXHR,e=n.tracingOrigins,o=n.shouldCreateSpanForRequest,u={},s=function(t){if(u[t])return u[t];var n=e;return u[t]=n.some(function(n){return M(t,n)})&&!M(t,"sentry_key"),u[t]},a=s;"function"==typeof o&&(a=function(t){return s(t)&&o(t)});var f={};i&&lt({callback:function(t){!function(t,n,i){if(!ii()||!t.fetchData||!n(t.fetchData.url))return;if(t.endTimestamp&&t.fetchData.__span){var r=i[t.fetchData.__span];return void(r&&(t.response?r.setHttpStatus(t.response.status):t.error&&r.setStatus(Zn.InternalError),r.finish(),delete i[t.fetchData.__span]))}var e=ri();if(e){var r=e.startChild({data:c(c({},t.fetchData),{type:"fetch"}),description:t.fetchData.method+" "+t.fetchData.url,op:"http.client"});t.fetchData.__span=r.spanId,i[r.spanId]=r;var o=t.args[0]=t.args[0],u=t.args[1]=t.args[1]||{},s=u.headers;S(o,Request)&&(s=o.headers),s?"function"==typeof s.append?s.append("sentry-trace",r.toTraceparent()):s=Array.isArray(s)?d(s,[["sentry-trace",r.toTraceparent()]]):c(c({},s),{"sentry-trace":r.toTraceparent()}):s={"sentry-trace":r.toTraceparent()},u.headers=s}}(t,a,f)},type:"fetch"}),r&&lt({callback:function(t){!function(t,n,i){var r,e;if(!ii()||(null===(r=t.xhr)||void 0===r?void 0:r.__sentry_own_request__)||!((null===(e=t.xhr)||void 0===e?void 0:e.__sentry_xhr__)&&n(t.xhr.__sentry_xhr__.url)))return;var o=t.xhr.__sentry_xhr__;if(t.endTimestamp&&t.xhr.__sentry_xhr_span_id__){var u=i[t.xhr.__sentry_xhr_span_id__];return void(u&&(u.setHttpStatus(o.status_code),u.finish(),delete i[t.xhr.__sentry_xhr_span_id__]))}var s=ri();if(s){var u=s.startChild({data:c(c({},o.data),{type:"xhr",method:o.method,url:o.url}),description:o.method+" "+o.url,op:"http.client"});if(t.xhr.__sentry_xhr_span_id__=u.spanId,i[t.xhr.__sentry_xhr_span_id__]=u,t.xhr.setRequestHeader)try{t.xhr.setRequestHeader("sentry-trace",u.toTraceparent())}catch(t){}}}(t,a,f)},type:"xhr"})}var Ci=L();var Mi=c({idleTimeout:ci,markBackgroundTransactions:!0,maxTransactionDuration:600,routingInstrumentation:function(t,n,i){if(void 0===n&&(n=!0),void 0===i&&(i=!0),Ci&&Ci.location){var r,e=Ci.location.href;n&&(r=t({name:Ci.location.pathname,op:"pageload"})),i&&lt({callback:function(n){var i=n.to,o=n.from;void 0===o&&e&&-1!==e.indexOf(i)?e=void 0:o!==i&&(e=void 0,r&&($.log("[Tracing] Finishing current transaction with op: "+r.op),r.finish()),r=t({name:Ci.location.pathname,op:"navigation"}))},type:"history"})}else $.warn("Could not initialize routing instrumentation due to invalid location")},startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0},Ni),Ai=function(){function t(n){this.name=t.id,this.Bn=new ki,this.Jn=!1;var i=Ni.tracingOrigins;n&&n.tracingOrigins&&Array.isArray(n.tracingOrigins)&&0!==n.tracingOrigins.length?i=n.tracingOrigins:this.Jn=!0,this.options=c(c(c({},Mi),n),{tracingOrigins:i})}return t.prototype.setupOnce=function(t,n){var i=this;this.Xn=n,this.Jn&&($.warn("[Tracing] You need to define `tracingOrigins` in the options. Set an array of urls or patterns to trace."),$.warn("[Tracing] We added a reasonable default for you: "+Ni.tracingOrigins));var r=this.options,e=r.routingInstrumentation,o=r.startTransactionOnLocationChange,u=r.startTransactionOnPageLoad,s=r.markBackgroundTransactions,a=r.traceFetch,c=r.traceXHR,f=r.tracingOrigins,h=r.shouldCreateSpanForRequest;e(function(t){return i.Gn(t)},u,o),s&&(gi&&gi.document?gi.document.addEventListener("visibilitychange",function(){var t=ri();gi.document.hidden&&t&&($.log("[Tracing] Transaction: "+Zn.Cancelled+" -> since tab moved to the background, op: "+t.op),t.status||t.setStatus(Zn.Cancelled),t.setTag("visibilitychange","document.hidden"),t.finish())}):$.warn("[Tracing] Could not set up background tab detection due to lack of global document")),Ri({traceFetch:a,traceXHR:c,tracingOrigins:f,shouldCreateSpanForRequest:h})},t.prototype.Gn=function(t){var n=this;if(this.Xn){var i=this.options,r=i.beforeNavigate,e=i.idleTimeout,o=i.maxTransactionDuration,u="pageload"===t.op?function(){var t=(n="sentry-trace",i=document.querySelector("meta[name="+n+"]"),i?i.getAttribute("content"):null);var n,i;if(t)return function(t){var n=t.match(ni);if(n){var i=void 0;return"1"===n[3]?i=!0:"0"===n[3]&&(i=!1),{traceId:n[1],parentSampled:i,parentSpanId:n[2]}}}(t);return}():void 0,s=c(c(c({},t),u),{trimEnd:!0}),a="function"==typeof r?r(s):s,f=void 0===a?c(c({},s),{sampled:!1}):a;!1===f.sampled&&$.log("[Tracing] Will not send "+f.op+" transaction because of beforeNavigate."),$.log("[Tracing] Starting "+f.op+" transaction on scope");var h=function(t,n,i,r,e){var o,u,s=(null===(o=t.getClient())||void 0===o?void 0:o.getOptions())||{},a=new hi(n,t,i,r);return(a=li(a,s,c({parentSampled:n.parentSampled,transactionContext:n},e))).sampled&&a.initSpanRecorder(null===(u=s.Et)||void 0===u?void 0:u.maxSpans),a}(this.Xn(),f,e,!0,{location:L().location});return h.registerBeforeFinishCallback(function(t,i){n.Bn.addPerformanceEntries(t),function(t,n,i){var r=i-n.startTimestamp;i&&(r>t||r<0)&&(n.setStatus(Zn.DeadlineExceeded),n.setTag("maxTransactionDurationExceeded","true"))}(1e3*o,t,i)}),h}$.warn("[Tracing] Did not create "+t.op+" transaction because _getCurrentHub is invalid.")},t.id="BrowserTracing",t}();var Li={},qi=L();qi.Sentry&&qi.Sentry.Integrations&&(Li=qi.Sentry.Integrations);var Fi=c(c(c({},Li),ti),{BrowserTracing:Ai});return yi(),t.BrowserClient=Kn,t.Hub=Pt,t.Integrations=Fi,t.SDK_NAME="sentry.javascript.browser",t.SDK_VERSION=sn,t.Scope=Mt,t.Span=si,t.Transports=Rn,t.addBreadcrumb=function(t){Wt("addBreadcrumb",t)},t.addExtensionMethods=yi,t.addGlobalEventProcessor=Lt,t.captureEvent=function(t){return Wt("captureEvent",t)},t.captureException=captureException,t.captureMessage=function(t,n){var i;try{throw new Error(t)}catch(t){i=t}return Wt("captureMessage",t,"string"==typeof n?n:void 0,c({originalException:t,syntheticException:i},"string"!=typeof n?{captureContext:n}:void 0))},t.close=function(t){var n=Bt().getClient();return n?n.close(t):_t.reject(!1)},t.configureScope=function(t){Wt("configureScope",t)},t.defaultIntegrations=Vn,t.flush=function(t){var n=Bt().getClient();return n?n.flush(t):_t.reject(!1)},t.forceLoad=function(){},t.getCurrentHub=Bt,t.getHubFromCarrier=Xt,t.init=function(t){if(void 0===t&&(t={}),void 0===t.defaultIntegrations&&(t.defaultIntegrations=Vn),void 0===t.release){var n=L();n.SENTRY_RELEASE&&n.SENTRY_RELEASE.id&&(t.release=n.SENTRY_RELEASE.id)}void 0===t.autoSessionTracking&&(t.autoSessionTracking=!0),function(t,n){var i;!0===n.debug&&$.enable();var r=Bt();null===(i=r.getScope())||void 0===i||i.update(n.initialScope);var e=new t(n);r.bindClient(e)}(Kn,t),t.autoSessionTracking&&function(){if(void 0!==L().document){var t=Bt();"function"==typeof t.startSession&&"function"==typeof t.captureSession&&(t.startSession({ignoreDuration:!0}),t.captureSession(),lt({callback:function(n){var i=n.from,r=n.to;void 0!==i&&i!==r&&(t.startSession({ignoreDuration:!0}),t.captureSession())},type:"history"}))}else $.warn("Session tracking in non-browser environment with @sentry/browser is not supported.")}()},t.lastEventId=function(){return Bt().lastEventId()},t.onLoad=function(t){t()},t.setContext=function(t,n){Wt("setContext",t,n)},t.setExtra=function(t,n){Wt("setExtra",t,n)},t.setExtras=function(t){Wt("setExtras",t)},t.setTag=function(t,n){Wt("setTag",t,n)},t.setTags=function(t){Wt("setTags",t)},t.setUser=function(t){Wt("setUser",t)},t.showReportDialog=function(t){void 0===t&&(t={});var n=Bt(),i=n.getScope();i&&(t.user=c(c({},i.getUser()),t.user)),t.eventId||(t.eventId=n.lastEventId());var r=n.getClient();r&&r.showReportDialog(t)},t.startTransaction=function(t,n){return Wt("startTransaction",c({},t),n)},t.withScope=$t,t.wrap=function(t){return Ln(t)()},t}({});
//# sourceMappingURL=bundle.tracing.min.js.map