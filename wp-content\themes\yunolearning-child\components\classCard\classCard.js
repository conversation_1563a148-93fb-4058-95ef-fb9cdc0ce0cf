const YUNOClassCard = (function($) {
    
    const classCard = function() {
        Vue.component('yuno-class-card', {
            props: ["data", "options"],
            template: `
                <article class="classCard">
                    <div class="row">
                        <div class="col-12 col-md-6 col-lg-6">
                            <div class="classIntroWrapper">
                                <div class="classSchedule noSM">
                                    <h3 class="date">{{data.date.day}}</h3>
                                    <p class="month">{{data.date.month}}</p>
                                    <span class="year">{{data.date.year}}</span>
                                </div>
                                <div class="classIntro">
                                    <div class="wrapper">
                                        <h2 class="classTitle"><a :href="data.url">{{data.title}}</a></h2>
                                        <div class="classID" v-if="data.zoom_meeting_id !== undefined && options.module === 'instructorHome' && userRole.data !== 'Learner'" @click="copyToClipboard('yunoZoomMeetingID-' + data.id)">
                                            <span class="idLabel">Zoom Meeting ID:</span>
                                            <span class="idVal">{{ data.zoom_meeting_id }}</span>
                                            <span class="material-icons">content_copy</span> 
                                            <b-input class="idField" :id="'yunoZoomMeetingID-' + data.id" :value="data.zoom_meeting_id" readonly></b-input>
                                        </div>
                                        <span class="classType" v-if="data.category">{{data.category}}</span>  
                                    </div> 
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 col-lg-6">
                            <div class="classMetaWrapper">
                                <div class="classSchedule noMD-LG">
                                    <h3 class="date">{{data.date.day}}</h3>
                                    <p class="month">{{data.date.month}}</p>
                                    <span class="year">{{data.date.year}}</span>
                                </div>
                                <ul class="classMeta">
                                    <li>
                                        <div class="caption">Time</div>
                                        <div class="value">{{data.time}}</div>
                                    </li>
                                    <li>
                                        <div class="caption">Duration</div>
                                        <div class="value">{{data.duration}}</div>
                                    </li>
                                    <li v-if="data.start">
                                        <div class="caption">Start</div>
                                        <div class="value">{{data.start}}</div>
                                    </li>
                                </ul>
                                <div class="ctaWrapper">
                                    <template v-if="data.recording_url !== undefined">
                                        <template v-if="data.recording.length !== 0">
                                            <b-button tag="a"
                                                :href="data.recording_url"
                                                target="_blank"
                                                class="yunoPrimaryCTA">
                                                View Recording
                                            </b-button>
                                        </template>
                                        <template v-else>
                                            <b-button tag="a"
                                                href="#"
                                                target="_self"
                                                class="yunoPrimaryCTA isDisabled">
                                                Recording not available
                                            </b-button>
                                        </template>
                                    </template>
                                    <template v-else>
                                        <b-button tag="a"
                                            :href="data.zoom_url !== '' ? data.zoom_url : '#'"
                                            target="_blank"
                                            @click="classStatus(data)"
                                            class="yunoPrimaryCTA">
                                            Launch Class
                                        </b-button>
                                        <template v-if="userRole.data === 'Instructor' && data.class_type !== 'webinar'">
                                            <ul class="actionList" v-if="options.module === 'instructorHome' && data.category !== 'Demo'">
                                                <li><a href="/class-schedule" @click="editClass(data)"><i class="fa fa-pencil" aria-hidden="true"></i></a></li>
                                                <li><a href="#" @click="deleteClass(data.delete_url, $event, data.id)"><i class="fa fa-trash-o" aria-hidden="true"></i></a></li>
                                            </ul>
                                        </template>
                                    </template>
                                </div>    
                            </div>
                        </div>
                    </div>
                </article>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                ...Vuex.mapState([
                    'loader',
                    'userRole',
                    'updateLink'
                ]),
            },
            async created() {
                
            },
            mounted() {
                
            },
            methods: {
                classStatus(data) {
                    this.setSlassStatus(data);
                },
                setSlassStatus(data) {
                    const options = {
                        apiURL: YUNOCommon.config.user("classLaunchStatus", {classID: data.id}),
                        module: "gotData",
                        store: "updateLink",
                        callback: false
                    };
        
                    this.$store.dispatch('fetchData', options);
                },
                copyToClipboard(ele) {
                    let copyText = document.getElementById(ele);

                    copyText.select();
                    copyText.setSelectionRange(0, 99999)
                    document.execCommand("copy");

                    this.$buefy.toast.open({
                        duration: 1000,
                        message: `Copy to clipboard`,
                        position: 'is-bottom',
                        type: "is-info"
                    });
                },
                editClass(data) {
                    localStorage.removeItem('classEditState');

                    localStorage.setItem('classEditState', JSON.stringify(data));
                },
                initiateDelete(apiURL, classID) {
                    this.loader.isActive = true;
                    this.loader.overlay = true;

                    const options = {
                        apiURL: apiURL,
                        module: "classDelete",
                        componentInstance: this,
                        classID: classID
                    };

                    this.$store.dispatch('deleteData', options);
                },
                deleteClass(apiURL, $event, classID) {
                    $event.preventDefault();

                    this.$buefy.dialog.confirm({
                        title: 'Deleting Class',
                        message: 'Are you sure you want to <b>delete</b> this class?',
                        confirmText: 'Delete Class',
                        type: 'is-danger',
                        onConfirm: () => this.initiateDelete(apiURL, classID)
                    });
                }
            }
        });
    };

    return {
        classCard: classCard
    };
})(jQuery);

