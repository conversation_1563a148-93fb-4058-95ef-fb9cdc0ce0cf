const validationMsg={messages:{required:"Mobile number is required",numeric:"Numbers only",min:"Minium 10 numbers required",max:"Maxium 10 numbers required",is:"Required",is_not:"New batch shouldn't be same as current batch"}};Sentry.init({dsn:"https://<EMAIL>/5690147",integrations:[new Sentry.Integrations.BrowserTracing],release:"yuno-unbounce-page",tracesSampleRate:1,tracingOptions:{trackComponents:!0},logErrors:!0}),YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yunolp-sign-in",{props:["category","productcode","leadstatus","contenttype","contentid","ctalabel","ctabg","ctahasicon","ctafontsize","ctatextcolor","hasiframe"],template:'\n        <validation-observer ref="lpSignInObserver" v-slot="{handleSubmit, invalid}">\n            <form id="lpSignInForm" class="lpForm" @submit.prevent="handleSubmit(initForm)">\n                <validation-provider :customMessages="{ required: message.required }"  :rules="{required:true, numeric: true, min: 10, max: 10}" v-slot="{ errors, classes }">\n                    <div class="field">\n                        <label v-if="false" for="userMobile">Mobile</label>\n                        <input inputmode="numeric" pattern="[0-9]*" id="userMobile" placeholder="Enter your mobile number" :class="classes" type="text" v-model="signIn.mobile">\n                        <p class="error">{{errors[0]}}</p>\n                    </div>\n                    <button class="googleLogin" \n                        :class="[ctabg !== undefined && ctabg !== \'\' ? ctabg : \'\', !ctahasicon ? \'noIcon\' : \'\', ctafontsize !== undefined && ctafontsize !== \'\' ? ctafontsize : \'\', ctatextcolor !== undefined && ctatextcolor !== \'\' ? ctatextcolor : \'\']"\n                        type="submit">\n                        <template v-if="ctalabel !== undefined && ctalabel !== \'\'">\n                            {{ ctalabel }}\n                        </template>\n                        <template v-else>\n                            Sign up with Google\n                        </template>\n                    </button>\n                </validation-provider>\n            </form>    \n        </validation-observer>\n    ',data:()=>({message:{required:"Mobile number is required"},signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""}}}),computed:{},mounted(){},methods:{setPayload(){let e=this.signIn,t=document.title.replace(/\s+/g,"-").toLowerCase();e.categoryURL=`/${this.$props.category}`,e.productCode=this.$props.productcode,e.leadStatus=this.$props.leadstatus,e.variant=`${window.ub.page.variantId}-${t}`,e.utmSource=YUNOCommon.getQueryParameter("utm_source"),e.utmCampaign=YUNOCommon.getQueryParameter("utm_campaign"),e.utmMedium=YUNOCommon.getQueryParameter("utm_medium"),e.adGroupID=YUNOCommon.getQueryParameter("adgroupid"),e.adContent=YUNOCommon.getQueryParameter("ad_content"),e.utmTerm=YUNOCommon.getQueryParameter("utm_term"),e.gclid=YUNOCommon.getQueryParameter("gclid"),e.content.type=void 0!==this.$props.contenttype?this.$props.contenttype:"",e.content.id=void 0!==this.$props.contentid?this.$props.contentid:""},signInURL(e,t){let o="";switch(t){case"dev":o="https://accounts.google.com/o/oauth2/auth/oauthchooseaccount?response_type=code&access_type=offline&client_id=***********-cquruv3ca8e2upq7bflldlpl8rq5cl5d.apps.googleusercontent.com&redirect_uri=https%3A%2F%2Fcharlesdickens.wpengine.com%2Fauth&state="+e+"&scope=email%20profile&approval_prompt=force&flowName=GeneralOAuthFlow";break;case"stage":o="https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id=***********-cquruv3ca8e2upq7bflldlpl8rq5cl5d.apps.googleusercontent.com&redirect_uri=https://yunoenglish.wpengine.com/auth&state="+e+"&scope=email%20profile&approval_prompt=force&flowName=GeneralOAuthFlow";break;case"prod":o="https://accounts.google.com/o/oauth2/auth/oauthchooseaccount?response_type=code&access_type=offline&client_id=************-pdgbpet2n7tebqbodvtuot016ccj97bq.apps.googleusercontent.com&redirect_uri=https%3A%2F%2Fwww.yunolearning.com%2Fielts%2Fsignup&state="+e+"&scope=email%20profile&approval_prompt=force&flowName=GeneralOAuthFlow"}return o},initForm(){this.setPayload();const e=JSON.stringify(this.signIn);setTimeout(()=>{this.$props.hasiframe?window.parent.location.href=this.signInURL(encodeURI(e),"stage"):window.location.href=this.signInURL(encodeURI(e),"stage")},50)}}});const app=new Vue({el:"#yunoLPSignIn",data:()=>({}),async created(){},computed:{},mounted(){},methods:{}});