@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
    .chooseOptions {
        padding: $gapLargest 0;

        @media (min-width: 768px) {
			padding: $gapLargest * 2 0 $gapLargest;
		}

        .sectionTitle {
			font-size: $fontSizeLargest;
			color: $primaryColor;
			font-weight: 700;
			text-align: center;
			margin: 0;

			@media (min-width: 768px) {
				font-size: $fontSizeLargest + 16;
			}
		}

		.sectionSubtitle {
			font-size: $fontSizeLarger;
			font-weight: 400;
			text-align: center;
			margin: $gapLarge 0 0 0;
			line-height: $fontSizeLarger + 6;
		}

        .optionsWrapper {
            margin-top: $gapLargest;
        }

        .optionCard {
            border: 1px solid $grey;
            margin-bottom: $gapLargest;

            @media (min-width: 768px) {
                margin-bottom: 0;
                height: 100%;    
            }

            a {
                display: flex;
                height: 100%;
                flex-direction: column;

                &:hover {
                    text-decoration: none;

                    .optionCaption {
                        color: $secondaryColor;
                    }
                }
            }

            .optionImg {
                width: 100%;
                height: auto;
            }

            figcaption {
                padding: $gap15;
                height: 100%;
                display: flex;
                align-items: center;
            }

            .optionCaption {
                font-size: $fontSizeLarger;
                font-weight: 600;
                color: $primaryCopyColor;
            }
        }
    }
}