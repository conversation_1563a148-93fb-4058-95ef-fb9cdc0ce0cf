Vue.component("yuno-availability-v2", {
  props: {
    data: {
      type: Array,
      required: false,
    },
    options: {
      type: Object,
      required: false,
    },
  },
  template: `
        <div>
          	<ul class="hoursWrapper">
				<li class="item" v-for="(item, index) in data" :key="index">
					<div class="itemWrapper">
						<div class="day">{{ item.day }}</div>
						<div class="slots">
							<b-field>
								<b-switch v-model="item.isDayOff"
  									@input="onOpen($event ? false : true, index)"
									:true-value="false"
									:false-value="true">
									<template v-if="item.isDayOff">
										Unavailable
									</template>
									<template v-else>
										Available
									</template>
								</b-switch>
							</b-field>
						</div>
					</div>
					<div class="hours" v-if="!item.isDayOff"> 
						<template v-for="(slot, slotIndex) in item.availability">
							<div class="hourWrapper">
								<validation-provider 
									tag="div"
									class="chooseHour" 
									:rules="{required:true, isOverlapping: slot.isOverlapping}" 
									v-slot="{ errors, classes }"
									:customMessages="{ required: message.required}"
								>
									<div :class="{ hasError: errors && errors.length > 0 }">
										<b-dropdown
											:class="classes"
											v-model="slot.startTime"
											aria-role="list"
											class="filterMenu"
										>
											<button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
												<span><template>Start</template> {{ slot.startTime  }}</span>
												<b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
											</button>
											<template v-for="time in timesList">
												<b-dropdown-item
  													@click="onFilterItemSelect(slot, time, 'start', item)"
  													:key="time"
													:value="slot.startTime"
													aria-role="listitem">
													<span>{{time}}</span>
												</b-dropdown-item>
											</template>
										</b-dropdown>
										<p class="error" v-if="false">{{errors[0]}}</p>
									</div>
								</validation-provider>
								<validation-provider 
									:customMessages="{ required: message.required, is_not: message.isNot, greaterThen: message.greaterThen }"
									tag="div" 
									class="chooseHour"
									:rules="{required:true, is_not: slot.startTime, isOverlapping: slot.isOverlapping, isEndTime: slot.isEndTime}" 
									v-slot="{ errors, classes }"
								>
									<div :class="{ hasError: errors && errors.length > 0 }">
										<b-dropdown
											:class="classes"
											v-model="slot.endTime"
											aria-role="list"
											class="filterMenu"
										>
											<button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
												<span>End {{ slot.endTime }}</span>
												<b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
											</button>
											<template v-for="time in timesList">
												<b-dropdown-item
  													@click="onFilterItemSelect(slot, time, 'end', item)"
  													:key="time"
													:value="slot.endTime"
													@click.native="slot.endTime = time"
													aria-role="listitem">
													<span>{{time}}</span>
												</b-dropdown-item>
											</template>
										</b-dropdown>
										<p class="error">{{errors[0]}}</p>
									</div>
								</validation-provider>
								<b-button 
  									@click="removeSlot(index, slotIndex)" 
									class="yunoPrimaryCTA iconOnly removeSlot noBG">
									<span class="material-icons-outlined">close</span>
								</b-button>
								<b-tooltip label="Copy time to all"
									type="is-dark"
									position="is-top">
									<b-button
										v-if="slotIndex === 0"
										@click="copySlot(item, slotIndex)" 
										class=" iconOnly copySlot noBG">
										<span class="material-icons-outlined">content_copy</span>
									</b-button>    
								</b-tooltip>
							</div>
						</template>
						<template>
							<div class="addSlotWrapper">
								<b-button 
									@click="addSlot(index)" 
									class="yunoPrimaryCTA addSlot noBG">
									Add Hours
								</b-button>
							</div>
						</template>
					</div>
				</li>
          	</ul>
        </div>
  `,
  data() {
    return {
      message: {
        required: "Required",
        isNot: "Value should not be same as start time",
        error: "",
      },
      timesList: [
        "12:00 AM",
        "12:30 AM",
        "1:00 AM",
        "1:30 AM",
        "2:00 AM",
        "2:30 AM",
        "3:00 AM",
        "3:30 AM",
        "4:00 AM",
        "4:30 AM",
        "5:00 AM",
        "5:30 AM",
        "6:00 AM",
        "6:30 AM",
        "7:00 AM",
        "7:30 AM",
        "8:00 AM",
        "8:30 AM",
        "9:00 AM",
        "9:30 AM",
        "10:00 AM",
        "10:30 AM",
        "11:00 AM",
        "11:30 AM",
        "12:00 PM",
        "12:30 PM",
        "1:00 PM",
        "1:30 PM",
        "2:00 PM",
        "2:30 PM",
        "3:00 PM",
        "3:30 PM",
        "4:00 PM",
        "4:30 PM",
        "5:00 PM",
        "5:30 PM",
        "6:00 PM",
        "6:30 PM",
        "7:00 PM",
        "7:30 PM",
        "8:00 PM",
        "8:30 PM",
        "9:00 PM",
        "9:30 PM",
        "10:00 PM",
        "10:30 PM",
        "11:00 PM",
        "11:30 PM",
      ],
    };
  },
  methods: {
    onOpen(value, index) {
      if (value && this.data[index].availability.length === 0) {
        this.data[index].availability.push({
          startTime: "",
          endTime: "",
        });
      }
    },
    timeToMinutes(timeStr) {
      if (!timeStr) return NaN;
      // Normalize all whitespace to a single space
      const [time, period] = timeStr.trim().split(/\s+/);
      if (!time || !period) return NaN;
      let [hours, minutes] = time.split(":").map(Number);

      // Convert to 24-hour format
      if (period === "PM" && hours !== 12) {
        hours += 12;
      } else if (period === "AM" && hours === 12) {
        hours = 0;
      }

      return hours * 60 + minutes;
    },
    onFilterItemSelect(slot, newTime, type, item) {
      console.log(slot, newTime, type, item);
      if (type === "start") {
        slot.startTime = newTime;
      } else if (type === "end") {
        slot.endTime = newTime;
      }
      this.manageOverlappingSlot(slot, item);
      this.manageEndTime(slot);
    },
    manageEndTime(slot) {
      const currentStart = this.timeToMinutes(slot.startTime);
      const currentEnd = this.timeToMinutes(slot.endTime);

      if (slot.startTime === "" || slot.endTime === "") {
        slot.isEndTime = false;
        return;
      }

      if (currentEnd <= currentStart) {
        slot.isEndTime = true;
      } else {
        slot.isEndTime = false;
      }
    },
    manageOverlappingSlot(slot, item) {
      // Only check if both start and end times are set
      if (slot.startTime === "" || slot.endTime === "") {
        slot.isOverlapping = false;
        return;
      }
      const currentStart = this.timeToMinutes(slot.startTime);
      const currentEnd = this.timeToMinutes(slot.endTime);

      slot.isOverlapping = false;

      item.availability.forEach((existingSlot) => {
        if (existingSlot === slot) return;
        const existingStart = this.timeToMinutes(existingSlot.startTime);
        const existingEnd = this.timeToMinutes(existingSlot.endTime);

        if (currentStart < existingEnd && existingStart < currentEnd) {
          slot.isOverlapping = true;
        }
      });
    },
    addSlot(index) {
      this.data[index].availability.push({
        startTime: "",
        endTime: "",
        isOverlapping: false,
        isEndTime: false,
      });
    },
    removeSlot(index, slotIndex) {
      this.data[index].availability.splice(slotIndex, 1);
      if (this.data[index].availability.length === 0) {
        this.data[index].isDayOff = true;
      }
    },
    copySlot(item, slotIndex) {
      // Create a deep copy of the availability slots from the source day
      const copiedAvailability = JSON.parse(JSON.stringify(item.availability));
      // Iterate through all days in the data array
      this.data.forEach((day) => {
        // Only process days that are not marked as "day off"
        if (!day.isDayOff) {
          // Replace the target day's availability with the copied slots
          day.availability = JSON.parse(JSON.stringify(copiedAvailability));
        }
      });
    },
  },
});
