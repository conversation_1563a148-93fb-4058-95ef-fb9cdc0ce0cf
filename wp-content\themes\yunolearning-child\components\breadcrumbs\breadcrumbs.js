Vue.component('yuno-breadcrumbs', {
    props: {
        data: {
            type: Array,
            required: true
        }
    },
    template: `
        <ul class="breadcrumbs">
            <li v-for="(item, index) in data" :key="index">
                <template v-if="item.url === null">
                    <span>{{ item.label }}</span>
                </template>
                <template v-else>
                    <a :href="item.url" :class="[item.isActive ? 'active' : '']">{{ item.label }}</a>
                </template>
            </li>
        </ul>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});