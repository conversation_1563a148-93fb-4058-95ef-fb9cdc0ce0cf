const YUNOCarouselCards = (function($) {
    
    const carouselCards = function() {
        Vue.component('yuno-carousel-cards', {
            props: ["options"],
            template: `
                <section class="carouselCards" :class="{'noTopGap': !options.topGap, 'cardListEmpty': options.list.length === 0}">
                    <div class="container">
                        <h2 v-if="options.title !== undefined" class="carouselCardTitle">{{options.title}}</h2>
                        <div class="videoLPPlayer" v-if="options.videoURL !== undefined && options.videoURL !== ''">
                            <iframe width="560" height="315" :src="options.videoURL" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                        </div>
                        <div class="sliderWrapper">
                            <div class="carouselListControls">
                                <button class="prev" type="button" data-controls="prev" tabindex="-1" aria-controls="tns1">
                                    <i class="fa fa-chevron-left" aria-hidden="true"></i>
                                </button>
                                <button class="next" type="button" data-controls="next" tabindex="-1" aria-controls="tns1">
                                    <i class="fa fa-chevron-right" aria-hidden="true"></i>
                                </button>
                            </div>
                            <div :id="options.sectionID" class="carouselSlide">
                                <div 
                                    v-for="(slide, slideIndex) in options.list"
                                    :key="slideIndex"
                                    class="slide">
                                    <article class="cardItem">
                                        <div class="slideInnerWrapper" :class="[slide.videoURL !== undefined ? 'hasVideo' : '']">
                                            <template v-if="slide.videoURL === undefined">
                                                <p class="cardContent">{{slide.content}}</p>
                                                <figure class="authorImg" v-if="slide.authorImg !== undefined">
                                                    <img :src="slide.authorImg" :alt="slide.name">
                                                </figure>
                                            </template>
                                            <template v-else>
                                                <div class="slidePlayer">
                                                    <iframe width="560" height="315" :src="'https://www.youtube.com/embed/' + slide.videoURL" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                                                </div>
                                            </template>
                                            <h4 class="cardName">
                                                <template v-if="slide.authorProfileURL !== undefined">
                                                    <a :href="slide.authorProfileURL">
                                                        {{slide.name}}
                                                    </a>
                                                </template>
                                                <template v-else>
                                                    {{slide.name}}
                                                </template>
                                                <small class="cardDate" v-if="slide.date !== undefined && slide.date !== ''">{{slide.date}}</small>
                                            </h4>
                                        </div>
                                    </article>
                                </div>
                            </div>
                        </div>
                        <div class="ctaWrapper" v-if="options.demoCTA !== undefined">
                            <b-button 
                                tag="a"
                                href="tel:+91-8847251466"
                                class="yunoSecondaryCTA big">
                                Call for free demo
                            </b-button>
                        </div>
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                
            },
            created() {
                
            },
            mounted() {
                this.initSlider();
                this.richSnippet();
                this.videoRichSnippet();
            },
            methods: {
                initSlider() {
                    const sectionId = this.$props.options.sectionID;

                    let slider = tns({
                        container: "#"+ sectionId +"",
                        controlsContainer: ".carouselListControls",
                        loop: true,
                        responsive: {
                            500: {
                                items: 1
                            },
                            768: {
                                items: 1
                            },
                            992: {
                                items: 1
                            },
                            1200: {
                                items: 1
                            }
                        },
                        swipeAngle: false,
                        speed: 400,
                        nav: false,
                        mouseDrag: true,
                        autoplay: true,
                        autoplayTimeout: 6000,
                        nonce: yunoNonce
                    });
                },
                structuredData(structuredDataObj) {
                    const script = document.createElement('script');
                    
                    script.setAttribute('type', 'application/ld+json');

                    let inlineScript = document.createTextNode(JSON.stringify(structuredDataObj));

                    script.appendChild(inlineScript); 
                    document.head.appendChild(script);
                },
                richSnippet() {
                    const data = this.$props.options;

                    if (data.hasVideoReviews === undefined && !data.hasVideoReviews && data.list.length !== 0) {
                        let structuredDataText = {
                                "@context": "https://schema.org",
                                "@type": "LocalBusiness",
                                "name": "Yuno Learning",
                                "image": "https://www.yunolearning.com/wp-content/themes/yunolearning-child/assets/images/yuno_logo.png",
                                "priceRange": "₹₹₹₹",
                                "telephone": "+917411781928",
                                "address": {
                                    "@type": "PostalAddress",
                                    "streetAddress": "Yuno Learning Global Pvt. Ltd. 006-Upper Ground Floor, MGF Metropolis Mall",
                                    "addressLocality": "Gurugram",
                                    "addressRegion": "Haryana",
                                    "postalCode": "122002",
                                    "addressCountry": "IN"
                                },
                                "aggregateRating": {
                                    "@type": "AggregateRating",
                                    "ratingValue": "5",
                                    "reviewCount": data.list.length
                                },
                                "review": []
                            };

                        const getData = data.list;

                        for (let i = 0; i < getData.length; i++) {
                            const review = getData[i];

                            let dataObj = {
                                "@type": "Review",
                                "author": {
                                    "@type": "Person",
                                    "name": review.name
                                },
                                "datePublished": review.date,
                                "description": review.content,
                                "name": review.name,
                                "reviewRating": {
                                    "@type": "Rating",
                                    "bestRating": "5",
                                    "ratingValue": "5",
                                    "worstRating": "1"
                                }
                            };

                            structuredDataText.review.push(dataObj);
                        };

                        this.structuredData(structuredDataText);
                    } else {
                        let getData = data.list,
                            structuredDataText = {
                                "@context": "https://schema.org",
                                "@type": "WebPage",
                                "video": []
                            };

                        for (var i = 0; i < getData.length; i++) {
                            let dataObj = {
                                "@type": "VideoObject",
                                "name": getData[i].name,
                                "thumbnailUrl": "https://i.ytimg.com/vi_webp/"+ getData[i].videoURL +"/maxresdefault.webp",
                                "uploadDate": getData[i].uploadDate,
                                "description": getData[i].schemaDescription,
                                "contentUrl": window.location.href
                            }

                            structuredDataText.video.push(dataObj);
                        };

                        this.structuredData(structuredDataText);
                    };
                },
                videoRichSnippet() {
                    const data = this.$props.options;

                    if (data.videoURL !== undefined && data.videoURL !== "") {
                        const thumbnail = YUNOCommon.getFromString(data.videoURL, /embed\/(.*)/);

                        let structuredDataText = {
                            "@context": "https://schema.org",
                            "@type": "VideoObject",
                            "name": data.title,
                            "description": data.description,
                            "thumbnailUrl": "https://i.ytimg.com/vi_webp/"+ thumbnail +"/maxresdefault.webp",
                            "embedUrl": data.videoURL,
                            "uploadDate": data.videoUploadDate
                        };
    
                        this.structuredData(structuredDataText);    
                    };
                }
            }
        });
    };

    return {
        carouselCards: carouselCards
    };
})(jQuery);

