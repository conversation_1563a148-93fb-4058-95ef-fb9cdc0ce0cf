Vue.component('yuno-academy-hero-banner', {
    props: {
        data: {
            type: Object,
            required: true
        },
        backgroundColor: {
            type: String,
            default: "#00000005"
        },
        title: {
            type: String,
            default: "Title goes here"
        },
        categories: {
            type: Array,
            default: () => []
        },
        backgroundImage: {
            type: String,
            default: ""
        },
        description: {
            type: String,
            default: "Description goes here"
        },
        buttonText: {
            type: String,
            default: "Book a Demo Class"
        },
        buttonLink: {
            type: String,
            default: "#"
        },
    },
    template: `
        <section class="academyHeroBanner" :style="{backgroundColor: backgroundColor, backgroundImage: 'url(' + backgroundImage + ')'}">
            <div class="container">
                <div class="row">
                    <template v-if="data.loading">
                        <div class="col-12 col-md-12">
                            <h1 class="title"><b-skeleton width="200px" height="62px"></b-skeleton></h1>
                            <ul class="categories">
                                <li><b-skeleton width="150px" height="16px"></b-skeleton></li>
                            </ul>
                        </div>
                        <div class="col-12 col-md-12">
                            <p class="description"><b-skeleton height="100px"></b-skeleton></p>
                            <div class="ctaWrapper">
                                <b-skeleton width="150" height="32px"></b-skeleton>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="data.success && data.error === null">
                        <div class="col-12 col-md-12">
                            <h1 class="title">{{ title }}</h1>
                            <ul class="categories" v-if="categories">
                                <li class="label">Categories:</li>
                                <li v-for="(category, index) in categories">
                                    <a :href="category.link">{{ category.name }}</a>{{ index !== categories.length - 1 ? ',' : '' }}
                                </li>
                            </ul>
                        </div>
                        <div class="col-12 col-md-12">
                            <p class="description">{{ description }}</p>
                            <div class="ctaWrapper">
                                <b-button @click.prevent="bookADemo" tag="a" :href="buttonLink" class="yunoSecondaryCTA">{{ buttonText }}</b-button>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="data.success">
                        {{ data.errorData }}
                    </template>
                </div>
            </div>
            <b-modal 
                :active.sync="bookademo.modal" 
                :width="388" 
                :can-cancel="['escape', 'x']"
                :on-cancel="bookademoModalClose"
                class="yunoModal lightTheme">
                    <template v-if="bookademo.modal">
                        <div class="modalHeader">
                            <h3 class="modalTitle">
                                {{ bookademo.title }}
                            </h3>
                        </div>
                        <div class="modalBody">
                            <h2 class="modalCaption">{{ bookademo.subtitle }}</h2>
                            <div 
                                v-if="filters.loading" 
                                class="observerWrapper"
                            >
                                <div class="categoryWithImage">
                                    <div class="fieldWrapper">
                                        <b-skeleton width="180px" height="153px" :animated="true"></b-skeleton>
                                    </div>
                                    <div class="fieldWrapper">
                                        <b-skeleton width="180px" height="153px" :animated="true"></b-skeleton>
                                    </div>
                                </div>
                            </div>
                            <validation-observer 
                                tag="div"
                                v-if="filters.success" 
                                class="observerWrapper"
                                ref="bookademoobserver" 
                                v-slot="{ handleSubmit, invalid }">
                                <form @submit.prevent="handleSubmit(initBookADemo)">
                                    <validation-provider 
                                        tag="div" 
                                        class="categoryWithImage" 
                                        :rules="{required:true}" 
                                        v-slot="{ errors, classes }"
                                    >
                                        <template v-for="(option, i) in bookademo.data">
                                            <div class="fieldWrapper">
                                                <div class="inner">
                                                    <b-field :key="i" :style="categoryImg(option)">
                                                        <b-radio-button 
                                                            :class="classes"
                                                            v-model="bookademo.selected"
                                                            @input="initBookADemo()"
                                                            name="bookademo"
                                                            :native-value="option">
                                                            {{option.name}}
                                                        </b-radio-button>
                                                    </b-field>
                                                    <div class="catLabel">{{option.name}}</div>
                                                </div>
                                            </div>
                                        </template>
                                        <p class="error">{{errors[0]}}</p>
                                    </validation-provider>
                                    <div class="ctaWrapper alignLeft" v-if="false">
                                        <b-button native-type="submit" class="yunoSecondaryCTA">Submit</b-button>
                                    </div>
                                </form>
                            </validation-observer>
                        </div>
                    </template>
            </b-modal>
        </section>
    `,
    data() {
        return {
            bookademo: {
                modal: false,
                title: "Book a Demo Class",
                subtitle: "Choose Subject Category",
                selected: "",
                data: []
            },
        }
    },
    computed: {
        ...Vuex.mapState([
            'filters'
        ]),
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        categoryImg(data) {
            const imgURL = data.image.replace(/ /g, '%20');

            return {
                "background-image": `url(${imgURL})`
            };
        },
        bookademoModalClose() {
            this.bookademo.selected = "";
            this.bookademo.data = [];
        },
        initBookADemo() {
            window.location.href = this.bookademo.selected.url;
        },
        bookADemo() {
            this.bookademo.modal = true;

            let categories = JSON.parse(JSON.stringify(this.filters.data));

            categories.forEach(element => {
                if (element.slug === "ielts") {
                    element.url = "/for-ads/ielts/ielts-for-all-v3/"
                } else if (element.slug === "english speaking") {
                    element.url = "/for-ads/english-speaking/english-for-all-v3/"
                } else if (element.slug === "pte") {
                    element.url = "/for-ads/pte/pte-for-all-v3/"
                } else if (element.slug === "duolingo") {
                    element.url = "/for-ads/duolingo-for-all/"
                } else if (element.slug === "toefl") {
                    element.url = "/for-ads/toefl-for-all/"
                } else if (element.slug === "french") {
                    element.url = "/for-ads/french/french-for-all-v3/"
                };

            });

            this.bookademo.data = categories
        },
    }
});