const YUNOCategoriesAccordion = (function($) {
    
    const categoriesAccordion = function() {
        
        Vue.component('yuno-categories-accordion', {
            props: ["options"],
            template: `
                <div :class="[options.hasWrapperClass !== undefined ? options.hasWrapperClass : '']">
                    <b-collapse
                        class="categoryTaxonomy"
                        animation="slide"
                        v-for="(taxonomy, taxonomyIndex) of options.data"
                        :ref="'category' + taxonomy.id" 
                        :key="taxonomyIndex"
                        :open="options.default == taxonomyIndex"
                        @open="onOpenCategoryTaxonomy(taxonomyIndex, taxonomy.id)">
                        <div
                            class="collapseHeader field colorGrey"
                            :class="props.open ? 'menuDown' : 'menuUp'"
                            slot="trigger"
                            slot-scope="props">
                            <validation-provider tag="div" class="fieldWrapper" :rules="{required:true}" v-slot="{ errors, classes }">
                                <b-radio 
                                    :class="classes"
                                    :disabled="options.disabled !== undefined ? options.disabled : false"
                                    v-model="options.payload.category.id"
                                    @input="selectLevel1(taxonomy.id)"
                                    :native-value="taxonomy.id">
                                    {{taxonomy.name}}
                                </b-radio>
                                <p class="error">{{errors[0]}}</p>
                                <i class="fa" :class="props.open ? 'fa-caret-down' : 'fa-caret-up'" aria-hidden="true"></i>
                            </validation-provider>
                        </div>
                        <div class="collapseBody" v-if="taxonomy.sub_category !== undefined">
                            <template v-for="(sub2, sub2Index) in taxonomy.sub_category">
                                <template v-if="sub2.sub_category !== undefined">
                                    <b-collapse 
                                        animation="slide" 
                                        :open="false" 
                                        :ref="'subCategory' + sub2.id" 
                                        aria-id="subCategories3">
                                        <div class="trigger" slot="trigger" aria-controls="subCategories3" slot-scope="props">
                                            <i class="fa" :class="props.open ? 'fa-caret-down' : 'fa-caret-up'" aria-hidden="true"></i>
                                            <div class="field" :key="sub2Index">
                                                {{sub2.name}}
                                                <b-checkbox
                                                    v-if="false"
                                                    :disabled="options.disabled !== undefined ? options.disabled : false"
                                                    name="subCategory2"
                                                    :native-value="sub2.id"
                                                    v-model="options.payload.category.subCategory.id">
                                                    {{sub2.name}}
                                                </b-checkbox>
                                            </div>
                                        </div>
                                        <div class="sub2Content">
                                            <template v-for="(sub3, sub3Index) in sub2.sub_category">
                                                <div class="field" :key="sub3Index">
                                                    <b-checkbox
                                                        name="subCategory3"
                                                        :disabled="options.disabled !== undefined ? options.disabled : false"
                                                        :native-value="sub3"
                                                        v-model="options.payload.category.subCategory.subCategory.id">
                                                        {{sub3.name}}
                                                    </b-checkbox>
                                                </div>
                                            </template>
                                        </div>
                                    </b-collapse>  
                                </template>
                                <template v-else>
                                    <div class="field" :key="sub2Index">
                                        <b-checkbox
                                            name="subCategory2"
                                            :disabled="options.disabled !== undefined ? options.disabled : false"
                                            :native-value="sub2.id"
                                            v-model="options.payload.category.subCategory.id">
                                            {{sub2.name}}
                                        </b-checkbox>
                                    </div>
                                </template>
                            </template>
                        </div>
                        <div class="collapseBody" v-else>
                        <i class="fa fa-exclamation-circle" aria-hidden="true"></i> No sub-category found
                        </div>
                    </b-collapse>
                </div>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                
            },
            created() {
                
            },
            mounted() {
                
            },
            methods: {
                onOpenCategoryTaxonomy(index, id) {
                    Event.$emit('onOpenCategoryTaxonomy', index, id);
                },
                selectLevel1(level1ID) {
                    Event.$emit('selectLevel1', level1ID);
                }
            }
        });
    };

    return {
        categoriesAccordion: categoriesAccordion
    };
})(jQuery);

