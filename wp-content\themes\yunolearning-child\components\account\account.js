const YUNOAccount = (function($) {
    
    const account = function() {
        Vue.component('yuno-account', {
            props: ["data"],
            template: `
                <ul class="row settingList">
                    <li class="col-12 col-md-4 col-lg-4 settingLabel">Google Account</li>
                    <li class="col-12 col-md-8 col-lg-8 settingField">
                        {{userInfo.data.email}} <b-tag rounded><a @click="chooseAccountState()" :href="switchAccountURL">Switch Google account</a></b-tag>
                        <ul class="permissionList">
                            <li class="listTitle">Permissions:</li>
                            <li class="item" :class="{setMinHeight: googleContacts.loading}">
                                <div v-if="googleContacts.loading" class="smallLoader"></div>
                                <template v-else>
                                    <i class="fa" :class="googleContacts.error !== null ? 'fa-times-circle' : 'fa-check-circle'" aria-hidden="true"></i> Google contacts <b-tag rounded v-if="googleContacts.error !== null"><a @click="googlePermission()" :href="getGoogleContacts">Grant permission</a></b-tag>
                                </template>
                            </li>
                        </ul>
                        <div class="ctaWrapper">
                            <b-button tag="a"
                                href="https://myaccount.google.com/permissions"
                                target="_blank"
                                class="yunoPrimaryCTA wired">
                                Manage permissions in Google
                            </b-button>
                        </div>
                        <div class="ctaWrapper" v-if="false">
                            <b-button tag="a"
                                :href="getGoogleContacts"
                                @click="googlePermission()"
                                class="yunoPrimaryCTA wired">
                                Grant contacts permissions in Google
                            </b-button>
                        </div>
                    </li>
                    <li class="col-12 col-md-4 col-lg-4 settingLabel">Logout</li>
                    <li class="col-12 col-md-8 col-lg-8 settingField"><a href="/logout/">Log out of Yunolearning</a></li>
                    <li class="col-12 col-md-4 col-lg-4 settingLabel" v-if="false">Delete Account</li>
                    <li class="col-12 col-md-8 col-lg-8 settingField" v-if="false"><a href="#">Delete your account forever</a></li>
                </ul>
            `,
            data() {
                return {
                    switchAccountURL: "",
                }
            },
            computed: {
                ...Vuex.mapState([
                    'userInfo',
                    'googleContacts',
                    'header'
                ]),
                getGoogleContacts() {
                    return this.$store.getters.googleContacts
                }
            },
            created() {
                
            },
            mounted() {
                this.fetchContacts();
                this.manageSwitchAccount();
            },
            methods: {
                manageSwitchAccount() {
                    this.switchAccountURL = this.findItemBySlug(this.header.data, 'switch-account').url;
                },
                findItemBySlug(data, slugValue) {
                    for (const item of data) {
                        if (item.slug === slugValue) {
                            return item;
                        }
                        if (item.items) {
                            const found = this.findItemBySlug(item.items, slugValue);
                            if (found) {
                                return found;
                            }
                        }
                    }
                    return null;
                },
                fetchContacts() {
                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.googleContactsAPI(isLoggedIn),
                        module: "gotData",
                        store: "googleContacts",
                        callback: false
                    };

                    this.$store.dispatch('fetchData', options);
                },
                googlePermission() {
                    localStorage.setItem('userState', window.location.pathname);
                },
                chooseAccountState() {
                    localStorage.setItem('userState', window.location.pathname);
                    localStorage.setItem('isChooseAccountState', true);
                }
            }
        });
    };

    return {
        account: account
    };
})(jQuery);

