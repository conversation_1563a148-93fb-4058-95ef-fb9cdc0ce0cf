@import "variables";
@import "mixins";
@import "fonts";
@import "icons";

/* Common ----- START */
	html body {
		@include setFontFamily('Roboto', 400);
		color: $primaryCopyColor;

		.to-top-container {
			display: none;
		}
	}

	html {
		body {
			.zsiq_flt_rel {
				display: inline-block !important;
			}

			&.logged-in, &.page-template-invite {
				.zsiq_floatmain {
					display: none  !important;
				}
				.zsiq_flt_rel {
					display: none !important;
				}				
			}
		}
	}

	.fusion-privacy-bar {
		display: none;
	}

	html .switch {
		&:hover input[type=checkbox]:checked+.check, input[type=checkbox]:checked+.check {
			background: $primaryColor;

			&:hover {
				background: $primaryColor;	
			}
		}
	}

	.dialog {
		.modal-card-title {
			flex-shrink: 1;
			line-height: normal;
		}
	} 

	@keyframes slideDown {
		0% { transform: translateY(0); }
		100% { transform: translateY(-100%); }
	}
	
	@keyframes slideUp {
		0% { transform: translateY(-100%); }
		100% { transform: translateY(0); }
	}
	
	.notices {
		position: fixed;
		display: flex;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 2em;
		overflow: hidden;
		z-index: 1000;
		pointer-events: none;

		&.is-top {
			flex-direction: column;
		}

		.toast {
			flex-basis: auto;

			&.is-yuno-info {
				display: inline-flex;
				animation-duration: .15s;
				margin: .5em 0;
				text-align: center;
				box-shadow: rgba(0,0,0,0.12) 0 1px 4px;
				border-radius: 2em;
				padding: .75em 1.5em;
				pointer-events: auto;
				opacity: .92;
				background-color: #3298dc;
				color: #FFF;
			}

			&.is-top, &.is-bottom {
				align-self: center;
			}

			&.is-danger {
				background-color: red !important;
			}
		}
	}

	// html {
	// 	&.yunoLoaderEnabled {
	// 		overflow: hidden;
	// 	}
	// }

	body {
		&.yunoLoaderEnabled {
			overflow: hidden;
		}

		button.button, a.button {
			height: auto;
		}
	}

	.b-table, #app .yunoTabs .b-table {
		&.scrollable {
			.table-wrapper {
				overflow-x: auto;	
			}
		}
	}

	#app {
		.notificationBar {
			animation-duration: 0.5s;
			background-color: $tertiary;
			font-size: $headline6;
			display: flex;
			justify-content: center;
			align-items: center;
			line-height: normal;
			position: relative;

			&:hover {
				background-color: $bg;
			}

			a {
				flex: 0 0 calc(100% - 48px);
				padding: $gap15 0 $gap15 $gapLarger;
				line-height: normal;
				font-weight: 500;
				text-align: center;
				color: $onSurface;

				&:hover {
					color: $onSurface;
					text-decoration: none;
				}
			}

			.material-icons-outlined {
				cursor: pointer;
			}

			&.notVisibleInDOM {
				display: none;
			}
		}
		
		.notificationHide {
			animation-name: slideDown;
			transform: translateY(-100%);
		}
		
		.notificationShow {
			animation-name: slideUp;
			transform: translateY(0);
		}	

		.appPrompt {
			position: relative;
			z-index: 10;
			padding: $gapSmall 0;
			background: #e5e5e5;

			.button.yunoPrimaryCTA {
				@include setBGColor($primaryCopyColor, 0.3);
				@include setFontColor($primaryCopyColor, 1);
				border: 1px solid;
				@include setBorderColor($primaryCopyColor, 0.3);
				text-transform: uppercase;
			}

			.colGrid {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.closePrompt {
				padding-right: $gap15;
				padding-left: $gapSmaller;

				.fa {
					font-size: 20px;
					cursor: pointer;
				}
			}

			.appIcon {
				display: flex;
				align-items: center;
			}

			.appMedia {
				display: flex;
				align-items: center;
			}

			.appInfo {
				margin-left: $gap15;

				p {
					margin: 0;
				}

				.infoTitle {
					font-size: $fontSizeSmall;
					margin-bottom: $gapSmaller;
				}

				.infoCaption {
					font-size: $fontSizeSmaller;
					@include setFontColor($primaryCopyColor, 0.5);
				}
			}

			.iconWrap {
				$size: 50px;

				width: $size;
				height: $size;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 10px;
				background: $whiteBG;
				border: 1px solid;
				@include setBorderColor($primaryCopyColor, 0.3);
            	box-shadow: rgba(0,0,0,.117647) 0 0 40px;
				
				img {
					width: 100%;
					height: auto;
				}
			}
		}

		.unauthorizedLogin {
			a {
				display: inline-flex;
				background: #4285F4;
				border-radius: 4px;
				padding: 5px;
				color: $secondaryCopyColor;
				align-items: center;
				width: auto;
				transition: all 0.25s ease;
	
				.yuno-login-with-google-on-pages {
					display: block;
					font-weight: 500;
					padding: 0 $gapLarge;
				}
	
				&:hover {
					color: $secondaryCopyColor;
					text-decoration: none;
					background: $primaryColor;
				}
			}
		}

		@keyframes spinAround {
		    0% {
		        transform: rotate(0deg)
		    }

		    to {
		        transform: rotate(359deg)
		    }
		}

		.container {
			@media (min-width: 1024px) {
				max-width: 960px;
			}

			@media (min-width: 1216px) {
				max-width: 1140px;
			}

			@media (min-width: 1408px) {
				max-width: 1140px;
			}
		}

		// .yunoPageLoader {
			// @extend .pageLoaderSVG;
			// background-repeat: no-repeat;
			// background-position: center center;
		// }

		.yunoLoader {
			display: none;

			&.isActive {
				display: flex;
			}

			&.withOverlay {
				width: 100%;
				height: 100%;
				@include setBGColor($whiteBG, 0.8);
				display: flex;
				align-items: center;
				flex-direction: column;
				justify-content: center;
				overflow: hidden;
				position: fixed;
				z-index: 7777;
			}

			.yunoSpinner {
				width: 100px;
				height: 100px;
				position: static;
			}
		}

		.loaderWrapper {
			height: 50px;

			&.big {
				height: 100px;
			}
		}

		.smallLoader,
		.smallLoader:before,
		.smallLoader:after {
		    background: $primaryColor;
		    -webkit-animation: load1 1s infinite ease-in-out;
		    animation: load1 1s infinite ease-in-out;
		    width: 1em;
		    height: 4em;
		}

		.smallLoader {
		    color: $primaryColor;
		    text-indent: -9999em;
		    margin: 88px auto;
		    position: relative;
		    font-size: 8px;
		    -webkit-transform: translateZ(0);
		    -ms-transform: translateZ(0);
		    transform: translateZ(0);
		    -webkit-animation-delay: -0.16s;
		    animation-delay: -0.16s;

			&.withField {
				margin: 0 0 0 7px;
				font-size: 4px;
			}
		}

		.smallLoader:before,
		.smallLoader:after {
		    position: absolute;
		    top: 0;
		    content: '';
		}

		.smallLoader:before {
		    left: -1.5em;
		    -webkit-animation-delay: -0.32s;
		    animation-delay: -0.32s;
		}

		.smallLoader:after {
		    left: 1.5em;
		}

		@-webkit-keyframes load1 {

		    0%,
		    80%,
		    100% {
		        box-shadow: 0 0;
		        height: 4em;
		    }

		    40% {
		        box-shadow: 0 -2em;
		        height: 5em;
		    }
		}

		@keyframes load1 {

		    0%,
		    80%,
		    100% {
		        box-shadow: 0 0;
		        height: 4em;
		    }

		    40% {
		        box-shadow: 0 -2em;
		        height: 5em;
		    }
		}

		.imgFluid {
			width: 100%;
			height: auto;
		}

		&[v-cloak] {
			* { display: none; }
		}

		figure {
			margin: 0;
		}

		ul, li {
			margin: 0;
			padding: 0;
			list-style: none;
		}

		.container {
			&.noOverflow {
				overflow: visible;
			}
		}

		a {
			color: $secondaryColor;

			&:hover {
				color: $primaryV1;
			}

			&.button {
				-webkit-appearance: none;
			}
		}

		.primaryColor {
			color: $primaryColor
		}

		.fsLarger { 
			font-size: $fontSizeLarger;
		}

		.marginBtm16 {
			margin-bottom: $gapLarge;
		}

		.marginBtm30 {
			margin-bottom: $gapLargest;
		}

		.padBtm30 {
			padding-bottom: $gapLargest;
		}

		.padBtm60 {
			padding-bottom: $gapLargest * 2;
		}

		.commonTitle {
			color: $primaryCopyColor;
			font-size: $fontSizeLargest;
			margin: $gapLargest 0 0;	
		}

		.button {
			border-radius: 3px;
			padding: 7px $gap15;
			font-size: $subtitle2;
			line-height: normal;

			&.big {
				padding: $gapSmall $gapLarge;	
			}

			&.gapTopLargest { margin-top: $gapLargest }

			&.withIcon {
				> span {
					display: flex;
					align-items: center;

					.material-icons-outlined, .material-icons {
						margin-right: $gapSmaller;
					}
				}
			}

			.fa {
				padding-left: $gapSmaller;
			}

			&:focus {
				outline: none;
			}

			&.is-loading {
				position: relative;

				> span {
					color: transparent;
				}

				&:after {
					animation: spinAround .5s infinite linear;
				    border: 2px solid #dbdbdb;
				    border-radius: 290486px;
				    border-right-color: transparent;
				    border-top-color: transparent;
				    content: "";
				    display: block;
				    height: 1em;
				    width: 1em;
				    position: absolute;
				    left: calc(50% - .5em);
				    top: calc(50% - .5em);
				}
			}

			&.yunoPrimaryCTA,
			&.yunoSecondaryCTA {
				background: $primaryCopyColor;
				color: $secondaryCopyColor;
				display: inline-block;
				border: 1px solid transparent;
				font-weight: 500;

				&.noCTA {
					padding-left: 0;
					padding-right: 0;
					background: none;
					border: 0;
					color: $primary;
					font-size: $caption1;
					font-weight: 500;
					text-decoration: underline;

					&:hover {
						background: none;
					}
				}

				&.fluid {
					width: 100%;
					height: 40px;
					line-height: 24px;
				}

				&.fat {
					height: 40px;
					line-height: normal;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				&.withIcon {
					> span {
						display: flex;
						align-items: center;

						.material-icons-outlined, .material-icons {
							margin-right: $gapSmaller;
						}
					}
				}

				&.isDisabled {
					@include setBGColor($primaryCopyColor, 0.5);
					cursor: not-allowed;
				}

				&.is-loading {
					background: transparent;
					border-color: $primaryColor;

					&:after {
						border-color: $primaryColor;
						border-right-color: transparent;
				    	border-top-color: transparent;
					}
				}

				&.wired {
					background: $whiteBG;
					color: $onSurface;
					border: 1px solid $secondaryV1;
					transition: all 0.25s ease;
					font-weight: 500;

					&:hover {
						background: $bg;
						color: $onSurface;
						border-color: $onSurface;
					}

					&.is-loading {
						&:after {
							border-color: $onSurface;
							border-right-color: transparent;
					    	border-top-color: transparent;
						}
					}
				}

				&:hover {
					text-decoration: none;
				}

				&.medium {
					padding: 12px $gap15;
				}
			}

			&.noBorder {
				border: 0;
				padding-left: 0;
				padding-right: 0;
			}

			&.small {
				padding: $gapSmall - 2 $gapLarge;
			}

			&.iconOnly {
				border-radius: 50%;
				padding: 0;
				width: 35px;
				height: 35px;
				text-align: center;	

				&.anchor {
					display: flex;
				}

				.fa {
					position: relative;
					left: -2px;
				}
			}



			&.yunoSecondaryCTA {
				background: $primary;
				transition: all 0.25s ease;

				&.is-loading {
					background: transparent;
					border-color: $secondaryColor;

					&:after {
						border-color: $secondaryColor;
						border-right-color: transparent;
				    	border-top-color: transparent;
					}
				}

				&:hover {
					background-color: $primaryV1;
				}

				&.wired {
					color: $secondaryColor;
					border: 1px solid $secondaryColor;

					&:hover {
						background: $secondaryColor;
						color: $secondaryCopyColor;
						border-color: $secondaryColor;
					}

					&.is-loading {
						&:after {
							border-color: $secondaryColor;
							border-right-color: transparent;
					    	border-top-color: transparent;
						}

						&:hover {
							background: transparent;
						}
					}
				}
			}	
		}

		.b-radio.radio {
		    outline: none;
		    display: inline-flex;
		    align-items: center;
		    user-select: none ;

		    &:not(.button) {
		    	margin-right: .5em;
		    }

		    .control-label {
			    padding-left: calc(.75em - 1px);
			}

		    input[type=radio] {
			    position: absolute;
			    left: 0;
			    opacity: 0;
			    outline: none;
			    z-index: -1;

			    &+.check {
			    	display: flex;
				    flex-shrink: 0;
				    position: relative;
				    cursor: pointer;
				    width: 1.25em;
				    height: 1.25em;
				    transition: background .15s ease-out;
				    border-radius: 50%;
				    border: 2px solid $secondaryColor;

				    &:before {
				    	content: "";
					    display: flex;
					    position: absolute;
					    left: 50%;
					    margin-left: -.625em;
					    bottom: 50%;
					    margin-bottom: -.625em;
					    width: 1.25em;
					    height: 1.25em;
					    transition: transform .15s ease-out;
					    border-radius: 50%;
					    transform: scale(0);
					    background-color: $secondaryColor;
				    }
			    }

			    &:checked {
			    	&+.check {
			    		border-color: $secondaryColor;

			    		&:before {
				    		transform: scale(.5);	
				    	}
			    	}
			    }

			}
		}

		.colorGrey {
			.b-radio.radio {
			    input[type=radio] {
				    &+.check {
				    	border-color: $primaryCopyColor;

					    &:before {
					    	background-color: $primaryCopyColor;
					    }
				    }

				    &:checked {
				    	&+.check {
				    		border-color: $primaryCopyColor;
				    	}
				    }

				}
			}
		}

		.radioTiles {
			background: $whiteBG;
			border: 1px solid #CCCCCC;
			border-radius: 4px;
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			overflow: hidden;
			margin-top: $gap15;

			.control {
				border-bottom: 1px solid #CCC;
				position: relative;
				display: flex;
				align-items: center;
				flex: 0 0 100%;

				&:last-child {
					border-bottom: 0;
				}
			}

			.b-radio {
				font-size: $fontSizeSmaller;
				padding: $gapSmall - 2 $gapSmall;
				margin: 0; 
				cursor: pointer;
				border-radius: 0;
				width: 100%;

				&.is-primary {
					background: $primaryColor;
					color: $secondaryCopyColor;

				}
			}

			@media (min-width: 768px) {
				display: inline-flex;

				.control {
					flex: 0 0 auto;	
					border-right: 1px solid #CCC;
					border-bottom: 0;

					&:last-child {
						border-right: 0;
					}
				}

				.b-radio {
					width: auto;
				}
			}
		}

		.checkbox {
		    cursor: pointer;
		    display: inline-block;
		    line-height: 1.25;
		    position: relative
		}

		.b-checkbox.checkbox .control-label {
		    padding-left: calc(.75em - 1px)
		}

		.b-checkbox.checkbox.button {
		    display: flex
		}

		.b-checkbox.checkbox[disabled] {
		    opacity: .5
		}

			.b-checkbox.checkbox {
		    outline: none;
		    display: inline-flex;
		    align-items: center
		}

		.b-checkbox.checkbox:not(.button) {
		    margin-right: .5em
		}

		.b-checkbox.checkbox:not(.button)+.checkbox:last-child {
		    margin-right: 0
		}

		.b-checkbox.checkbox input[type=checkbox] {
		    position: absolute;
		    left: 0;
		    opacity: 0;
		    outline: none;
		    z-index: -1
		}

		.b-checkbox.checkbox input[type=checkbox]+.check {
		    width: 1.25em;
		    height: 1.25em;
		    flex-shrink: 0;
		    border-radius: 4px;
		    border: 2px solid #CCC;
		    transition: background .15s ease-out;
		    background: $whiteBG;
		}

		.b-checkbox.checkbox input[type=checkbox]:checked+.check {
		    background: $primaryColor url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1 1'%3E%3Cpath d='M.04.627L.146.52.43.804.323.91zm.177.177L.854.167.96.273.323.91z' fill='%23fff'/%3E%3C/svg%3E") no-repeat 50%;
		    border-color: $primaryColor
		}

		#yunoMain {
			min-height: 600px;

			&.instructorBody {
				background: #f5f5f5;
				padding-bottom: $gapLargest;
			}
		}

		.yunoSpinner {
		    display: block;
		    position: absolute;
		    left: 50%;
		    top: 50%;
		    width: 150px;
		    height: 150px;
		    margin: -75px 0 0 -75px;
		    border-radius: 50%;
		    border: 3px solid transparent;
		    border-top-color: $primaryColor;

		    -webkit-animation: spin 2s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
		    animation: spin 2s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */

		    &:before {
		    	content: "";
		        position: absolute;
		        top: 5px;
		        left: 5px;
		        right: 5px;
		        bottom: 5px;
		        border-radius: 50%;
		        border: 3px solid transparent;
		        border-top-color: $secondaryColor;

		        -webkit-animation: spin 3s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
		        animation: spin 3s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
		    }

		    &:after {
		    	content: "";
		        position: absolute;
		        top: 15px;
		        left: 15px;
		        right: 15px;
		        bottom: 15px;
		        border-radius: 50%;
		        border: 3px solid transparent;
		        border-top-color: #f9c922;

		        -webkit-animation: spin 1.5s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
		          animation: spin 1.5s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
		    }
		}

	    @-webkit-keyframes spin {
	        0%   { 
	            -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
	            -ms-transform: rotate(0deg);  /* IE 9 */
	            transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
	        }
	        100% {
	            -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
	            -ms-transform: rotate(360deg);  /* IE 9 */
	            transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
	        }
	    }
	    @keyframes spin {
	        0%   { 
	            -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
	            -ms-transform: rotate(0deg);  /* IE 9 */
	            transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
	        }
	        100% {
	            -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
	            -ms-transform: rotate(360deg);  /* IE 9 */
	            transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
	        }
	    }
	}

	.fade-out-top {
		-webkit-animation: fade-out-top 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
				animation: fade-out-top 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
	}

	@-webkit-keyframes fade-out-top {
		0% {
		-webkit-transform: translateY(0);
				transform: translateY(0);
		opacity: 1;
		}
		100% {
		-webkit-transform: translateY(-50px);
				transform: translateY(-50px);
		opacity: 0;
		}
  	}
	@keyframes fade-out-top {
		0% {
			-webkit-transform: translateY(0);
					transform: translateY(0);
			opacity: 1;
		}
		100% {
			-webkit-transform: translateY(-50px);
					transform: translateY(-50px);
			opacity: 0;
		}
  	}

	

  
  
/* Common ------ END */

/* Yuno Header ----- START */
	#app {
		.isSticky {
			position: sticky;
			top: 0;
			z-index: 10;

			&.hideHeader {
				.notificationBar {
					@extend .fade-out-top;
				}
			}

			&.notificationShow {
				transform: none;
				
				.yunoSubmenu {
					@media (min-width: 768px) {
						z-index: 9;	
						top: 126px;
					}
				}
			} 
		}
		

		.yunoHeader {
			position: sticky;
			top: 0;
			background: $whiteBG;
			box-shadow: rgba(0,0,0,.117647) 0 1px 3px;
			z-index: 10;

			&.scrollEnabled {
				position: static;
				@extend .fade-out-top;
			}

			&.noNav.logoCenter {
				.navbar {
					justify-content: center;
				}
			}

			&.noNav {
				.navbar {
					justify-content: space-between;
				}

				.logo {
					margin-left: $gap15;

					@media (min-width:768px) {
						margin-left: 0;
					}
				}
			}

			> .container {
				padding: 0;
			}

			.logo {
				margin-left: 54px;

				img {
					width: 106px;
					height: auto;
				}
			}
			.navbar {
				padding: 0;
				position: relative;
				align-items: center;

				.hasSearchBar {
					.searchBarWrapper {
						padding: $gapSmall;

						@media (min-width: 992px) {
							padding: 0;
							flex: 0 0 100%;
							display: block;
						}

						form {
							margin: 0;
						}
					}

					.searchFieldWrapper {
						position: relative;
						display: flex;
						align-items: flex-start;
				
						.searchField {
							flex: 0 0 100%;
						}

						.autocomplete {
							.control {
								border: 1px solid rgba(0, 0, 0, 0.12);
								padding-right: 34px;
								padding-left: 38px;
								border-radius: 4px;

								&::before {
									content: "\e8b6";
									@extend .material-icons-outlined;
									position: absolute;
									left: 8px;
									top: 8px;
									z-index: 4;
									@include setFontColor($primaryCopyColor, 0.2)
								}
							}	
						}
				
						.control {
							input[type="text"] {
								height: 40px;
								border: 0;
								
								padding-right: 0;
								padding-left: 0;
							}
							.icon.is-clickable {
								height: 40px;
								right: 0;
							}
						}
				
						.ctaWrapper {
							border: 1px solid rgba(0, 0, 0, 0.12);
							border-radius: 4px;
							padding: 0;
							flex: 0 0 56px;
							margin: 0;
							display: none;
						}
				
						.doSearch {
							background: rgba(0, 0, 0, 0.02);
							color: rgba(0, 0, 0, 0.38);
							border: 0;
							border-left: 1px solid rgba(0, 0, 0, 0.12);
							padding: 0;
							width: 100%;
							height: 38px;
							z-index: 3;
							position: relative;
							border-top-left-radius: 0;
							border-bottom-left-radius: 0;
						}

						.dropdown-content {
							padding: 0;
							max-height: 400px;

							div.dropdown-item {
								&:hover {
									background: none;
								}
							}

							.dropdown-item {
								padding: $gapSmall $gap15;

								.suggestion {
									text-wrap: wrap;

									.courseTitle {
										font-weight: 500;
										color: $onSurface;
									}

									.courseDetail {
										color: $onSurfaceVariant;
										font-size: $caption1;
										display: flex;

										.caption {
											margin-right: $gapSmall;

											&:after {
												content: "|";
												padding-left: $gapSmall;
												color: #0000001F;
											}
										}
									}

									&.courseBlock {
										figure {
											display: flex;
											gap: $gap15;

											.imageWrapper {
												flex: 0 0 100px;
												text-align: center;

												img {
													max-height: 56px;
													font-size: 0;
													border-radius: 4px;
												}
											}

											figcaption {
												// flex: 0 0 calc(100% - 115px);
												flex: 0 0 100%;
											}
										}
									}
								}

								&.is-hovered {
									color: white;

									.suggestion {
										.courseTitle {
											color: white;
										}
	
										.courseDetail {
											color: white;
											
											.caption {
												&:after {
													color: white;
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
			.navbar-toggler {
				position: absolute;
				left: 0;
				top: 12px;

				&:focus {
					outline: none;
				}
			}
			.navbar-toggler-icon {
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.yunoMainNav {
				border: 1px solid;
				@include setBorderColor($primaryColor, 0.1);

				> ul {
					> li {
						font-size: $fontSizeSmall;
						margin: 0;
						position: relative;
						width: 100%;
						border-bottom: 1px solid;
						font-weight: 500;
						@include setBorderColor($primaryColor, 0.1);

						> a {
							@include setFontColor($primaryCopyColor, 0.60);
							display: block;
							padding: $gapSmall $gapLarge;
						}

						&.hasCTA {
							padding: $gapSmall $gap15;

							.button {
								display: inline-block;
								color: #FFF;
								font-size: $fontSizeSmall;
							}
						}

						&.hasSearchBar {
							padding-left: $gap15;
							padding-right: $gap15;
							padding-top: $gap15;
							padding-bottom: $gap15;
							
							@media (min-width: 768px) {
								padding-left: 0;
								padding-right: 0;	
							}

							.searchBarWrapper {
								padding: 0;
							}
						}

						&.active, &.isCTA {
							border-bottom: 0;

							@include setBGColor($primaryCopyColor, 0.02);

							a {
								@include setFontColor($primaryCopyColor, 0.87);
							}

							&:after {
								content: "";
								width: 1px;
								height: 100%;
								@include setBGColor($primaryCopyColor, 0.87);
								position: absolute;
								left: 0;
								top: 0;
							}
						}

						&.isCTA {
							background: $secondaryColor;
							color: #FFF;
							border-radius: 4px;
							margin-left: $gap15;
							transition: all 0.25s ease;

							&:after {
								display: none;
							}
							
							a {
								color: #FFF;
								padding: $gapSmall $gapLargest;
							}

							&:hover {
								background: darken($secondaryColor, 10%);

								a {
									color: #FFF;
								}
							}
						}

						&.dropdown {
							display: block;

							.dropdownToggle {
								&:after {
									border: 0;
									@extend .material-icons-outlined;
									content: "\e5cf";
									vertical-align: 0;
									font-size: $fontSizeLarger;
									position: relative;
									top: 4px;
								}
							}
							
							&.show {
								.dropdown-toggle {
									&:after {
										transform: rotate(180deg);			
									}
								}
							}	
						}
						
						.dropdown-menu {
							border-color: #E6E6E6;
							border-radius: 4px;
							padding: 0;

							&.level2 {
								display: block;
							}

							.level3 {
								top: 0;
								left: auto;
								right: 100%;
								margin: -1px 1px 0 0;
							}

							.dropdown-item {
								font-size: $fontSizeSmall;
								color: $primaryCopyColor;
								padding: 0;

								&:last-child {
									border-bottom: 0;
								}

								&:hover {
									a {
										@include setFontColor($primaryCopyColor, 0.87);
									}

									.level3 {
										.dropdown-item {
											a {
												@include setFontColor($primaryCopyColor, 0.60);		
											}

											&:hover {
												a {
													@include setFontColor($primaryCopyColor, 0.87);
												}			
											}
										}
									}
								}

								&:active, &:focus {
									outline: none;
									background: none;	
								}
							}
						}

						.submenuLevel2 {
							li {
								&.dropdown {
									position: relative;

									&:after {
										border: 0;
										@extend .material-icons-outlined;
										content: "\e5cc";
										vertical-align: 0;
										font-size: $fontSizeLarger;
										position: absolute;
										right: 12px;
										top: 8px;
									}
								}
								a {
									display: block;
									padding: $gapSmaller $gapLargest $gapSmaller $gapLarge;
									@include setFontColor($primaryCopyColor, 0.87);
								}
							}
						}

						&:hover {
							a {
								text-decoration: none;
								@include setFontColor($primaryCopyColor, 0.87);
							}
						}

						&:last-child {
							border-bottom: 0;
						}
					}
				}
			}

			.yunoCallUs {
				display: none;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				font-size: $fontSizeSmall;

				&.noSpacer {
					&:before {
						display: none;
					}
				}

				a {
					background-color: $primary;
					color: white;
					font-weight: 500;
					display: flex;
					height: 40px;
					align-items: center;
					padding: 0 15px;
					border-radius: 4px;

					&:hover {
						text-decoration: none;
					}
				}
				

				&.preLogin {
					margin-left: $gap15;
				}

				.material-icons {
					font-size: 24px;
				}

				.caption, .value {
					display: none;
				}

				@media (min-width: 768px) {
					position: relative;
					display: none;

					.material-icons {
						font-size: $fontSizeSmall;
						position: relative;
						top: 2px;
					}

					.caption, .value {
						display: inline-block;
					}
				}

				@media (min-width: 992px) {
					position: relative;

					.fa {
						font-size: $fontSizeSmall;
					}

					.caption, .value {
						display: inline-block;
					}
				}
			}

			.yunoLogin {
				padding: 0 $gap15 0 0;
				display: flex;
				align-items: center;
				position: absolute;
				top: 13px;
				right: 0;

				> a {
					display: flex;
					align-items: center;
					width: auto;

					&.marginRight15 {
						margin-right: $gap15;

						@media (max-width: 320px) {
							margin-right: $gapSmaller;
						}
					}

					.yuno-login-with-google-on-pages {
						display: block;
						font-weight: 500;
						padding: $gapSmaller $gapSmall;
						background-color: transparent;
						border-radius: 4px;
						color: $secondaryCopyColor;
						border: 1px solid transparent;
						transition: all 0.25s ease;
						font-size: $subtitle2;
						@include setFontColor($primary, 1);

						&.grey.wired {
							background-color: transparent;
							@include setFontColor($primaryCopyColor, 0.60);
							position: relative;

							&::after {
								content: "|";
								position: absolute;
								right: -10px;
								top: 5px;
								@include setFontColor($primaryCopyColor, 0.12);
							}
						}
					}

					&:hover {
						text-decoration: none;

						.yuno-login-with-google-on-pages {
							background-color: transparent;
							&.grey.wired {
								
							}
						}
					}
				}

				@media (min-width: 768px) {
					padding: $gap15 0 $gap15 $gap15;	
					position: static;
				}
			}

			.yunoLoginDropdown {
				position: absolute;
				right: 15px;
				top: 10px;

				> a {
					display: flex;
					align-items: center;
					color: $primaryCopyColor;

					&:hover {
						text-decoration: none;
					}
				}

				.dropdown-toggle {
					&:after {
						border: 0;
						@extend .fa;
						content: "\f0d7";
						vertical-align: 0;
						font-size: $fontSizeLarger;
					}
				}
				
				&.show {
					.dropdown-toggle {
						&:after {
							transform: rotate(180deg);			
						}
					}
				}			

				.userProfile {
					display: flex;
					align-items: center;
				}

				.profilePic {
					$size: 32px;

					width: $size;
					height: $size; 
					@include setBGColor($primaryCopyColor, 0.2);
					border-radius: 50%;
					overflow: hidden;

					img {
						$size: 32px;

						width: $size;
			            height: $size;
					}

				}

				.dropdown-menu {
					background-color: white;
					border-color: #E6E6E6;
					border-radius: 4px;
					padding: 0;
					left: auto;
					right: 0;
					box-shadow: rgba(0,0,0,.117647) 0 0 10px;
					width: 300px;

					.menuHeader {
						display: flex;
						align-items: center;
						padding: $gap15;

						img {
							$size: 64px;

							width: $size;
							height: $size;
							border-radius: 50%;
							font-size: 0;
						}
						figcaption {
							flex: 0 0 calc(100% - 80px);
							margin-left: $gap15;

							.userName {
								font-size: $subtitle1;
								font-weight: 500;
								@include setFontColor($primaryCopyColor, 0.87);
								line-height: 24px;
							}

							.userEmail {
								font-size: $body2;
								font-weight: 400;
								@include setFontColor($primaryCopyColor, 0.6);
								line-height: 20px;
								word-break: break-all;
							}
						}
					}

					.userlinks {
						border-top: 1px solid rgba(0, 0, 0, 0.08);
						border-bottom: 1px solid rgba(0, 0, 0, 0.08);

						li {
							a {
								display: flex;
								align-items: center;
								font-size: $subtitle2;
								font-weight: 500;
								padding: $gapSmall $gap15;
								@include setFontColor($primaryCopyColor, 0.87);
							}

							.material-icons-outlined {
								margin-right: $gap15;
								@include setFontColor($primaryCopyColor, 0.6);
								font-size: 24px;
								font-weight: normal;
							}

							&.linksFooter {
								border-top: 1px solid rgba(0, 0, 0, 0.08);
							}
						}
					}

					.groupItem {
						border-bottom: 1px solid #E6E6E6;
						
						.dropdown-item {
							border-bottom: 0;
						}	
					}
				}
			}

			.additionalItems {
				padding: $gap15;

				.item {
					display: flex;
					align-items: flex-end;

					.field {
						flex: 0 0 calc(50% - 15px);
						margin: 0 $gap15 0 0;
					}

					a {
						flex: 0 0 50%;
						display: flex;
						align-items: center;
						margin-bottom: $gapSmaller;

						.caption {
							font-size: $caption1;
						}
					}
				}
			}

			// Small devices (landscape phones, 576px and up)
			@media (max-width: 576px) {
				
			}

			// Medium devices (tablets, 768px and up)
			@media (min-width: 768px) {
				
			}

			// Large devices (desktops, 992px and up)
			@media (min-width: 992px) {
				min-height: 72px;

				> .container {
					padding: 0 $gapLarge - 1;
				}
				.logo {
					margin: 0;
				}

				.yunoMainNav {
					border: 0;
					margin-left: $gap15;

					&.hasAdmin {
						overflow-x: auto;
						flex: 0 0 calc(100% - 186px);

						> ul {
							justify-content: flex-start;

							> li {
								flex: 0 0 146px;
								text-align: center;
							}
						}
					}

					&.hasSearch {
						> ul {
							justify-content: flex-start;
							
							> li {
								&:last-child:not(.hasSearchBar) {
									// margin-left: auto;
								}
							}
						}
					}

					> ul {
						justify-content: flex-end;
						> li {
							margin: 0;
							width: auto;
							border: 0;

							&.hasSearchBar {
								flex: 0 0 46%;
								margin: 0 $gap15;
							}

							> a {
								padding: 24px 24px;
							}

							&.active {
								&:after {
									top: auto;
									left: 0;
									bottom: 0;
									width: 100%;
									height: 1px;
								}
							}

							&.dropdown {
								position: relative;
								top: -1px;
							}

							.dropdown-menu {
								box-shadow: rgba(0,0,0,.117647) 0 0 10px;
								.dropdown-item {
									padding: 0;
								}
							}
						}
					}
				}

				.yunoLoginDropdown {
					position: static;
					margin-left: $gap15;
				}
			}

			// Extra large devices (large desktops, 1367px and up)
			@media (min-width: 1367px) {
				.yunoMainNav {
					> ul {
						
						> li {
							&.hasSearchBar {
								flex: 0 0 60%;
							}
						}
					}
				}
			}
		
		}
		
		.yunoSubmenu {
			position: absolute;
			top: 61px;
			left: 0;
			width: 100%;
			z-index: 11;
			padding: $gapLargest 0;
			background-color: #FAFAFA;
			border-bottom: 1px solid #e6e6e6;

			@media (min-width: 768px) {
				z-index: 9;	
				top: 72px;
			}

			.closeSubmenu {
				display: block;
				position: absolute;
				left: 19px;
				top: -21px;

				@media (min-width: 768px) {
					display: none;
				}
			}

			

			.submenuList {
				margin: 0 (-$gap15);
				display: flex;
				flex-wrap: wrap;
				height: 240px;
				overflow-y: auto;

				@media (min-width: 768px) {
					height: auto;
					overflow-y: auto;
				}
				
				li {
					padding: 0 $gap15;
					flex: 0 0 100%;

					@media (min-width: 768px) {
						flex: 0 0 33.3%;
					}

					a {
						display: block;
						padding: $gap15;

						&:hover {
							text-decoration: none;
							background-color: $whiteBG;

							.navLabel {
								color: $primary;
							}
						}

						figure {
							display: flex;

							.imgWrapper {
								flex: 0 0 100px;
								margin-right: $gap15;

								img {
									width: 100%;
									height: auto;
								}
							}

							.figcaption {
								flex: 0 0 calc(100% 115px);
							}
						}
					}

					.navLabel {
						font-size: $subtitle2;
						@include setFontColor($primaryCopyColor, 0.87);
						line-height: 24px;
						font-weight: 500;
						margin-bottom: $gapSmaller;
					}

					.navDes {
						font-size: $caption1;
						@include setFontColor($primaryCopyColor, 0.60);
						line-height: 16px;
						font-weight: 400;
						margin: 0;
					}
				}

				&.col2 {
					li {
						@media (min-width: 768px) {
							flex: 0 0 50%;
						}
					}
				}
			}
		}
	}	
/* Yuno Header ------ END */

	#site-content {
		height: 600px;
	}

/* Yuno Footer ----- START */
	#app {
		.yunoFooter {
			@include setBGColor($primaryCopyColor, 0.02);

			.link {
				font-size: $caption1;
				line-height: normal;
				text-decoration: underline;
				margin-top: $gapLargest;
				display: inline-block;
			}

			.whatsappSticky {
				position: fixed;
				z-index: 6;
				bottom: 0;
				right: 0;
				background-color: white;
				box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
				border-radius: 100px;
				margin: 0 $gap15 $gap15;

				a {
					font-size: $body2;
					@include setFontColor($primaryCopyColor, 0.87);
					display: flex;
					padding: 8px $gap15;
					line-height: normal;
					align-items: center;

					// span {
					// 	display: none;
					// }

					&::before {
						content: "\ea9c";
						@extend .material-icons-outlined;
						width: 24px;
						height: 24px;
						border-radius: 50%;
						background-color: #25D366;
						color: white;
						font-size: 14px;
						display: flex;
						justify-content: center;
						align-items: center;
						margin-right: $gapSmaller;
					}

					&:hover {
						text-decoration: none;
						color: $primary;
					}

					@media (min-width: 768px) {
						// span {
						// 	display: block;
						// }

						// &::before {
						// 	margin-right: $gapSmaller;
						// }
					}
				}
			}

			.spacer {
				border-top: 1px solid #E6E6E6;
				margin: $gapLarge 0;
				padding: $gapLarge 0;
			}

			&.noNav {
				> .container {
					padding-top: 0;
				}
				.footerBottom {
					border-top: 0;
				}
			}

			&.noBG {
				background: none;
				border: 0;
				@include setFontColor($primaryCopyColor, 0.4);

				.footerBottom {
					margin: 0;
					padding: 0;
				}
			}

			.col-12 {
				padding-bottom: $gapLargest;

				&:last-child {
					padding-bottom: 0;
				}
			}

			> .container {
				padding-top: $gapLargest;
				padding-bottom: $gapLargest;
			}

			.linkList {
				li {
					font-size: $fontSizeSmall;
					color: $primaryCopyColor;
					margin-bottom: $gap15;

					&:last-child {
						margin-bottom: 0;
					}

					&.listTitle {
						margin-bottom: $gapLarger - 4;
						text-transform: capitalize;

						h3 {
							font-size: $fontSizeLarge;
							font-weight: 400;	
						}

						&.noGap {
							margin: 0;
						}
					}

					&.android {
						a {
							display: inline-block;
						}
						
						img {
							width: 150px;
							height: auto;
						}
					}

					a {
						@include setFontColor($primaryCopyColor, 0.60);
						font-size: $caption1;

						&:hover {
							text-decoration: none;
							color: $primaryColor;
						}
					}

					.summary {
						display: block;
						@include setFontColor($primaryCopyColor, 0.5);
					}

					.enableDate {
						margin-top: $gapSmaller;
						display: block;
						@include setFontColor($primaryCopyColor, 0.5);
					}

					&.iconsBlock {
						display: flex;

						div {
							flex: 0 0 24px;
							margin-right: $gapSmall;

							&:last-child {
								margin-right: 0;
							}

							a {
								display: block;
								height: 24px;
								text-indent: -99999px;
								background: #FFF;
								border-radius: 50%;
								position: relative;
								@include setBGColor($primaryCopyColor, 0.38);

								&:before {
									font-size: 14px;
									@extend .fa;
									position: absolute;
									left: 0;
									top: 0;
									text-indent: 0;
									width: 24px;
									height: 24px;
									text-align: center;
									line-height: 24px;
								}
							}

							&.facebook {
								a {
									&::before {
										content: "\f09a";
										color: #FFF;
									}

									&:hover {
										background-color: #016DE5;
									}
								}
							}

							&.twitter {
								a {
									&::before {
										content: "\f099";
										color: #FFF;
									}
									&:hover {
										background-color: #1D9BF0;
									}
								}
							}

							&.linkedin {
								a {
									&::before {
										content: "\f0e1";
										color: #FFF;
									}

									&:hover {
										background-color: #0277B5;
									}
								}
							}

							&.instagram {
								a {
									&::before {
										content: "\f16d";
										color: #FFF;
									}

									&:hover {
										background: linear-gradient(45deg, #405de6, #5851db, #833ab4, #c13584, #e1306c, #fd1d1d);
										
									}
								}
							}

							&.youtube {
								a {
									&::before {
										content: "\f16a";
										color: #FFF;
									}

									&:hover {
										background-color: #FF0000;
									}
								}
							}
						}
					}
				}

				&.withBorder {
					border-bottom: 1px solid #E6E6E6;
					padding-bottom: $gapLarge;
					margin-bottom: $gapLarge;
				}

				&.withIcon {
					$size: 32px;
					$icnSize: 20px;

					li {
						a {
							display: inline-flex;
							align-items: center;
							@include setFontColor($primaryCopyColor, 0.87);
							font-size: $subtitle2;

							&:before {
								content: "";
								width: $size;
								height: $size;
								flex: 0 0 auto;
								@include setFontColor($primaryCopyColor, 0.6);
							}
						}

						&.wifiSpeed {
							a {
								&:before {
									content: "\e640";
									@extend .material-icons;
									display: flex;
									align-items: center;
								}
							}
						}

						&.zoomTest {
							a {
								&:before {
									content: "\e04b";
									@extend .material-icons-outlined;
									display: flex;
									align-items: center;
								}
							}
						}

						&.helpDesk {
							a {
								&:before {
									content: "\f0e2";
									@extend .material-icons;
									display: flex;
									align-items: center;
								}
							}
						}

						&.android {
							a {
								&:before {
									content: "\f17b";
									background-color: #93be23;
									@extend .fa;
									display: flex;
									align-items: center;
									justify-content: center;
									color: #FFF;
									font-size: 22px;
								}
							}
						}
					}
				}

				&.checkList {
					margin-bottom: $gapLarge;

					li {
						&:not(.listTitle) {
							display: flex;
							align-items: center;
							@include setFontColor($primaryCopyColor, 0.60);
							font-size: $caption1;

							&:before {
								content: "\e86c";
								padding-right: $gapSmall;
								@extend .material-icons;
								font-size: 16px;
								color: #669D4F;
							}
						}
					}		
				}
			}

			.footerBottom {
				border-top: 1px solid #E6E6E6;
				margin-top: $gapLargest;
				padding: $gapLarge 0 0;
				flex-direction: column;

				.copy {
					padding-bottom: $gapLarge;
					font-size: $fontSizeSmall;
				}

				.logo {
					img {
						width: 106px;
						height: auto;
					}
				}
			}

			@media (min-width: 768px) {
				.footerBottom {
					flex-direction: row;
				}
			}

			@media (min-width: 992px) {
				> .container {
					padding-top: $gapLargest * 2;
				}

				.footerBottom {
					flex-direction: row;
				}
			}
		}
	}
/* Yuno Footer ------ END */

/* Yuno Card ----- START */
	#app {
		.yunoCardSectionTitle {
			font-size: $fontSizeLargest;
			margin: 0;
			padding: $gapLargest 0 0;
		}

		.yunoCardWide {
			background: $whiteBG;
			border: 1px solid;
			@include setBorderColor($primaryCopyColor, 0.1);
			margin: $gapLargest 0 0;
			padding: $gap15;
			border-radius: 4px;

			&.withGap {
				margin-left: $gap15;
				margin-right: $gap15;
			}

			@media (min-width: 768px) {
				padding: $gapLargest;
			}

			.yunoCardTitle {
				font-size: $fontSizeLarger + 6;
				margin-bottom: $gapSmall;
				display: flex;
				align-items: center;
				justify-content: space-between;

				a {
					color: $primaryCopyColor;
				}

				.cardAction {
					font-size: $fontSizeLarger;
					font-weight: normal;
					color: $primaryColor;

					&:hover {
						color: $secondaryColor;
					}
				}
			}

			.metaList {
				display: flex;
				flex-wrap: wrap;

				li {
					flex: 0 0 100%;
					font-size: $fontSizeSmall;
					@include setFontColor($primaryCopyColor, 0.5);

					.linkItem {
						display: inline-block;
						padding-right: $gapSmaller;
						text-transform: capitalize;

						&:after {
							content: ",";
						}

						&:last-child {
							&:after {
								display: none;
							}
						}
					}
				}

				@media (min-width: 768px) {
					li {
						flex: 0 0 auto;
						margin-right: $gapSmall;

						&:after {
							content: "|";
							padding-left: $gapSmall;
						}

						&:last-child {
							&:after {
								display: none;
							}
						}
					}	
				}
			}

			.yunoCardBody {
				&.withFlexbox {
					display: flex;
					padding-top: $gap15;
					flex-wrap: wrap;

					.imgWrapper {
						flex: 0 0 100%;
						box-sizing: border-box;
						margin-bottom: $gap15;

						img {
							width: 100%;
							height: auto;
						}
					}

					.descriptionWrapper {
						flex: 0 0 100%;
						box-sizing: border-box;
					}

					@media (min-width: 768px) {
						.imgWrapper {
							flex: 0 0 30%;
							margin-bottom: 0;
						}

						.descriptionWrapper {
							flex: 0 0 70%;
							padding-left: $gap15;

							&.noMedia {
								flex: 0 0 100%;	
							}
						}
					}
				}
				.description {
					font-size: $fontSizeLarge;
					margin: 0;
				}
				p {
					&:last-child {
						margin-bottom: 0;
					}
				}
			}

			.yunoCardFooter {
				padding-top: $gapLargest;
			}
		}
	}	
/* Yuno Card ------ END */

#app {
	.groupElement {
		margin-bottom: $gap15;

		.field {
			margin-bottom: $gapSmaller;
		}

		.error {
			margin: 0;
			padding-top: 0;
			font-size: $fontSizeSmall;
			color: red;
		}

		.fieldLabel {
			margin: 0;
			padding: 0;
			font-size: $fontSizeLarge;
		}

		.helper {
			font-size: $fontSizeSmall;
			@include setFontColor($primaryCopyColor, 0.5);
			display: block;
			margin-bottom: $gap15;
		}

		&.checkList {
			.b-checkbox {
				&.invalid {
					input[type=checkbox]+.check {
						border-color: red;
					}
				}
			}
			.control-label {
				font-size: $fontSizeSmall;
			}
		}
	}

	.field {
		margin-bottom: $gap15;

		&.fieldLoading {
			min-height: 60px;
		}

		&:last-child {
			margin-bottom: 0;
		}

		.error {
			margin: 0;
			padding-top: $gapSmall;
			font-size: $fontSizeSmall;
			color: red;
		}

		.label {
			font-size: $subtitle2;
			margin: 0 0 $gapSmall;
			@include setFontColor($primaryCopyColor, 0.6);
		}

		.control {
			input {
				&[type="text"] {
					width: 100%;
					border: 1px solid #CCCCCC;
					padding: 0 $gap15;
					height: 36px;
					border-radius: 4px;	
					font-size: $fontSizeSmall;
					color: $primaryCopyColor;
					box-shadow:none;
				}

				&:focus {
					outline: none;
					box-shadow:none;
					border-color: #cccccc;
				}
			}

			textarea {
				width: 100%;
				border: 1px solid #CCCCCC;
				padding: $gap15;
				height: 100px;
				border-radius: 4px;	
				font-size: $fontSizeSmall;
				color: $primaryCopyColor;
				box-shadow:none;

				&:focus {
					outline: none;
					box-shadow:none;
				}
			}

			&.height250 {
				textarea {
					height: 250px;
				}
			}

			.counter {
				display: block;
				text-align: right;
				@include setFontColor($primaryCopyColor, 0.5);
			}

			&.invalid {
				input {
					&[type="text"] {
						border: 1px solid red;
					}
				}

				.select {
					select {
						border-color: red;
					}
				}

				.textarea {
					border-color: red;
				}
			}
		}

		.dropdown-content {
			box-shadow: none;
			border: 0;
			background: none;

			.dropdown-item {
				@include setFontColor($primaryCopyColor, 0.5);
				padding: $gapSmall $gap15;
				border-bottom: 1px solid $whiteBG;

				&.disabled {
					@include setBGColor($primaryCopyColor, 0.2);
					opacity: 0.5;
					cursor: not-allowed;
					border-bottom: 1px solid $whiteBG;
				}

				&.is-hovered {
					@include setBGColor($primaryCopyColor, 0.8);
					color: $secondaryCopyColor;
				}

				.userItem {
					display: flex;
					align-items: center;
					overflow: hidden;

					.userImg {
						$size: 24px;

						flex: 0 0 $size;
						height: $size;
						@include setBGColor($primaryCopyColor, 0.2);
						border-radius: 50%;
						overflow: hidden;
						border: 1px solid;
						@include setBorderColor($primaryCopyColor, 0.2);
						margin-right: $gapSmall;
						justify-content: center;
					    align-items: center;
					    display: flex;
					    font-size: $fontSizeLarger;

						img {
							$size: 22px;

							width: $size;
				            height: $size;
						}
					}

					.userName {
						flex: 0 0 calc(100% - 55px);
						text-overflow: ellipsis;
						overflow: hidden;
					}
				}

				&:active, &:focus {
					border: 0;
					background: none;
				}
			}
		}

		&.is-primary {
			.file-cta {
				background: $whiteBG;
				border: 1px solid $primaryColor;
				color: $primaryCopyColor;
			}
		}

		.upload {
			.icon {
				margin-right: $gapSmall;
				.mdi-upload {
					&:before {
						@extend .ylIcon;
						content: "upload";
					}
				}
			}
		}

		.select {
			&:not(.is-multiple):not(.is-loading) {
				&:after {
					border-color: $primaryColor;
				}
			}
		}

		.taginput {
			.taginput-container {
				border-color: #CCCCCC;
				padding: 0;
				flex-direction: column;
				display: flex;

				&.is-focused {
					box-shadow: none;
				}

				&.is-focusable {
					box-shadow: none;
				}

				&:focus, &:active {
					box-shadow: none;
					outline: none;
				}

				.autocomplete {
					width: 100%;
				}

				.tag {
					margin: $gapSmall $gap15 0;
					overflow: hidden;
					align-self: flex-start;
					max-width: calc(100% - 30px);

					> span {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}

				input {
					border: 0;
					margin: 0;
					padding: 0 $gap15;
				}
			}

			&.inlineTags {
				.taginput-container {
					flex-direction: row;

					.tag {
						margin-bottom: $gapSmall;
						margin-right: 0;
					}
				}
			}
		}

		.select select {
			border-color: #CCC;

			&:focus {
				box-shadow: none;
			}
		}

		.datepicker {
			.dropdown-item {
				&:focus, &:active {
					background: none;
				}
			}
			.icon {
				color: $primaryColor !important;

				.mdi-chevron-left, .mdi-chevron-right {
					&:after {
						content: "\f053";
						@extend .fa;
					}
				}

				.mdi-chevron-right {
					&:after {
						content: "\f054";
					}
				}	
			}
			
			.datepicker-cell {
				&.is-selectable {
					color: $primaryCopyColor;

					&.is-selected {
						background-color: $primaryColor;
						color: $secondaryCopyColor;
					}
				}

				&.is-today {
					border-color: $primaryColor;
				}
			}
			
		}

		.timepicker {
			.dropdown-item {
				&:active {
					background: none;
				}
			}
		}

		.autocomplete {
			.control {
				&.has-icons-right {
					input {
						padding-right: 36px;
					}

					.icon {
						.mdi-close-circle {
							&:after {
								content: "\f00d";
								@extend .fa;
							}
						}
					}
				}
			}
		}
	}
}



	.modal {
	    align-items: center;
	    display: none;
	    flex-direction: column;
	    justify-content: center;
	    overflow: hidden;
	    position: fixed;
	    z-index: 40;

	    &.yunoModal {
	    	.yunoLoader {
	    		height: 200px;
	    		position: relative;
	    	}
	    	.modal-close {
	    		position: absolute;
	    		right: 10px;
	    		top: 10px;
	    		background-color: $primaryCopyColor;
	    		border-radius: 50%;
	    		width: 32px;
	    		height: 32px;
	    		display: flex;
			    justify-content: center;
			    align-items: center;

	    		&:after, &:before {
	    			background-color: transparent;
	    		}

	    		&:before {
	    			display: none;
	    		}

	    		&:after {
			    	content: "\f00d";
			    	@extend .fa;
			    	color: $secondaryCopyColor;
			    	font-size: $fontSizeLarger;
			    	position: static;
				    width: auto;
				    height: auto;
				    transform: none;
			    }
	    	}

	    	.formWrapper, .wrapper {
	    		padding: $gapLarger;
	    		border-radius: 4px;
	    		background: #FCFCFC;
	    		border: 1px solid #eee;

				&.noBG {
					background: none;
					padding: 0;
					border-radius: 0;
					border: 0;
				}
	    	}

	    	.wrapper {
	    		p {
	    			&:last-child {
	    				margin-bottom: 0;
	    			}
	    		}
	    	}

	    	.modalHeader {
	    		.modalTitle {
	    			font-size: $fontSizeLarger + 2;
	    			font-weight: 500;
	    			margin-bottom: $gapLargest;
	    		}
	    	}

	    	.modalFooter {
	    		margin-top: $gapLargest;

	    		.button {
	    			min-width: 120px;
	    		}
	    	}

			&.inviteModal {
				.loaderWrapper {
					height: 50px;

					.smallLoader {
						margin-top: $gap15 !important;
						margin-bottom: $gap15 !important;
					}
				}
				.classFields {
					> li {
						font-size: $fontSizeLarge;
						line-height: normal;
						@include setFontColor($primaryCopyColor, 0.5);
						margin-bottom: $gap15;
			
						.listSubtitle {
							margin-bottom: $gapSmall;
							font-size: $fontSizeSmall;
						}
			
						.caption {
							font-weight: 500;
							color: $primaryCopyColor;
							display: block;
							margin-bottom: $gapSmaller
						}
			
						&:last-child {
							margin-bottom: 0;
						}
			
						.selectedLearners {
							margin-top: $gapSmall;
							max-height: 245px;
							overflow-y: auto;
			
							li {
								padding: $gapSmaller $gapSmall;
								font-weight: 400;
			
								.caption {
									font-weight: 400;
									margin: 0;
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
									font-size: $fontSizeSmall;
								}
							}
						}
			
						.clipboard {
							display: flex;
							justify-content: space-between;
							align-items: center;
			
							.control {
								flex: 0 0 85%;

								input {
									width: 100%;
									background-color: #fff;
									border-color: #dbdbdb;
									border-radius: 4px;
									color: #363636;
									border: 1px solid #dbdbdb;
									display: inline-flex;
									font-size: 1rem;
									height: 2.25em;
									justify-content: flex-start;
									line-height: 1.5;
									padding-bottom: calc(.375em - 1px);
									padding-left: calc(.625em - 1px);
									padding-right: calc(.625em - 1px);
									padding-top: calc(.375em - 1px);
									position: relative;
									vertical-align: top;
								}
							}
			
							.trigger {
								margin-left: $gap15;
								cursor: pointer;
								width: 36px;
								height: 36px;
								display: flex;
								justify-content: center;
								align-items: center;
							}
						}
					}
				}
			}

			&.loginSignupModal, &.lightTheme {
				.modal-content {
					padding: 0;
					border-radius: 4px;
				}
				.modalHeader {
					.modalTitle {
						@include setFontColor($primaryCopyColor, 0.87);
						font-size: $headline6;
						padding: 10px 40px 10px 20px;
						line-height: 24px;
						border-bottom: 1px solid rgba(0, 0, 0, 0.08);
						margin-bottom: 0;
					}
				}

				.modalBody {
					padding: 20px;

					.modalCaption {
						font-size: $subtitle1;
						@include setFontColor($primaryCopyColor, 0.87);
						text-align: left;
						padding-bottom: $gap15;
						font-weight: 500;
					}
				}

				.modal-close {
					background-color: $whiteBG;
					@include setFontColor($primaryCopyColor, 0.6);
					right: 20px;
					top: 7px;

					&::after {
						content: "\e5cd";
						@extend .material-icons-outlined;
						@include setFontColor($primaryCopyColor, 0.6);
					}
				}

				.observer {
					padding: 0 $gap15;

					.field {
						.label {
							font-size: $subtitle2 !important;
							@include setFontColor($primaryCopyColor, 0.6);
						}

						.error {
							display: none;
						}

						.control {
							input[type="text"] {
								height: 40px !important;
							}

							&.invalid {
								+ .error {
									display: block;
								}
							}
						}
						
					} 
				}

				.ctaWrapper {
					display: flex;
					justify-content: center;

					&.alignLeft {
						justify-content: flex-start;
					}
				}
				.googleLogin {
					border: 1px solid #1976D2;
					border-radius: 4px;
					width: 100%;
					padding: 9px $gap15 8px;
					background-color: white;
					font-size: $button;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #1976D2;

					&.width70 {
						width: 80%;
					}
	
					img {
						display: inline-block;
						width: 20px;
						height: 20px;
						margin-right: $gapSmall;
					}
				}

				.footerCaption {
					@include setFontColor($primaryCopyColor, 0.6);
					text-align: center;
                	font-size: $caption2;
					margin: 20px 0 0;
				}

				.helperCaption {
					margin: $gap15 0 0;
					font-size: $caption2;
					text-align: center;
					@include setFontColor($primaryCopyColor, 0.6);
					border-bottom: 1px solid rgba(0, 0, 0, 0.08);
					padding-bottom: $gap15;
					padding-top: $gap15;
				}
			}
	    }
	}

	#app {
		.modal {
			&.yunoModal {
				&.lightTheme {
					.categoryWithImage {
						display: flex;
						flex-wrap: wrap;
						margin: 0 (-$gap15) 0 0;
	
						.fieldWrapper {
							flex: 0 0 50%;
							padding: 0 $gap15 $gap15 0;
						}

						.field {
							background-size: cover;
						}
	
						.inner {
							border-radius: 4px;
							border: 1px solid $grey;
							background: #FFF;
							box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.10);
							padding: 8px;

							&:hover {
								border-color: $primary;
							}
						}
	
						.radio {
							width: 100%;
							height: 84px;
							margin: 0;
							font-size: 0; 
							background: transparent;
							border: 0;
						}

						.catLabel {
							text-align: center;
							font-size: $body2;
							@include setFontColor($primaryCopyColor, 0.87);
							font-weight: 500;
						}
					}
				}
			}	
		}
	}

	 

	.modal.is-active {
	    display: flex
	}

	.modal-background {
	    background-color: hsla(0, 0%, 4%, .86);
	    bottom: 0;
	    left: 0;
	    position: absolute;
	    right: 0;
	    top: 0;
	}

	.modal-card,
	.modal-content {
	    margin: 0 20px;
	    max-height: calc(100vh - 160px);
	    overflow: auto;
	    position: relative;
	    width: 100%;
	    color: $primaryCopyColor;
	    padding: $gapLargest;
	    border-radius: 10px;
	}

	@media print,
	screen and (min-width:769px) {

	    .modal-card,
	    .modal-content {
	        margin: 0 auto;
	        max-height: calc(100vh - 40px);
	        width: 640px
	    }
	}

	.modal-close {
	    background: none;
	    height: 40px;
	    position: fixed;
	    right: 20px;
	    top: 20px;
	    width: 40px;
	    border: 0;

	    &:focus {
	    	outline: none;
	    }

	    &:after {
	    	content: "\f00d";
	    	@extend .fa;
	    	color: $secondaryCopyColor;
	    	font-size: $fontSizeLarger;
	    }
	}

	.modal-card {
	    display: flex;
	    flex-direction: column;
	    max-height: calc(100vh - 40px);
		overflow: hidden;
		overflow-y: visible;
	    -ms-overflow-y: visible
	}

	.modal-card-foot,
	.modal-card-head {
	    align-items: center;
	    background-color: $whiteBG;
	    display: flex;
	    flex-shrink: 0;
	    justify-content: flex-start;
	    padding: $gapLargest;
	    position: relative
	}

	.modal-card-head {
	    border-top-left-radius: 10px;
	    border-top-right-radius: 10px
	}

	.modal-card-title {
	    color: $primaryCopyColor;
	    flex-grow: 1;
	    flex-shrink: 0;
	    font-size: $fontSizeLarger + 2;
	    line-height: 1;
	    margin: 0;
	    font-weight: 500;
	}

	.modal-card-foot {
	    border-bottom-left-radius: 10px;
	    border-bottom-right-radius: 10px;

	    .button {
			border-radius: 3px;
			padding: $gapSmall - 2 $gapLarge;

			.fa {
				padding-left: $gapSmaller;
			}

			&:focus {
				outline: none;
			}

			

			&.is-primary {
				background: $primaryColor;
				color: $secondaryCopyColor;
				display: inline-block;
				border: 1px solid transparent;
			}

			
		}
	}

	.modal-card-foot .button:not(:last-child) {
	    margin-right: .5em
	}

	.modal-card-body {
	    -webkit-overflow-scrolling: touch;
	    background-color: $whiteBG;
	    flex-grow: 1;
	    flex-shrink: 1;
	    overflow: auto;
	    padding: 0 $gapLargest;

	    .media {
	    	background: #FCFCFC;
	    	border-radius: 4px;
	    	padding: $gapLarger;
	    	border: 1px solid #EEE;
	    }

	    p {
	    	margin: 0;
	    }
	}

	html {
		.dialog .modal-card {
		    max-width: 460px;
		    width: auto
		}

		.dialog .modal-card .modal-card-head {
		    font-size: 1.25rem;
		    font-weight: 600;
		    background: $whiteBG;
		    border: 0;
		}

		.dialog .modal-card .modal-card-body .field {
		    margin-top: 16px
		}

		.dialog .modal-card .modal-card-body.is-titleless {
		    border-top-left-radius: 6px;
		    border-top-right-radius: 6px
		}

		.dialog .modal-card .modal-card-foot {
		    justify-content: flex-end;
		    background: $whiteBG;
		    border: 0;
		}

		.dialog .modal-card .modal-card-foot .button {
		    display: inline;
		    min-width: 5em;
		    background: $whiteBG;
			color: $primaryColor;
			border: 1px solid $primaryColor;
			height: auto;

			&.is-danger {
				border: 1px solid $secondaryColor;	
				background: $secondaryColor;
				color: $secondaryCopyColor;
			}

			&.is-primary {
				border: 1px solid $primaryColor;	
				background: $primaryColor;
				color: $secondaryCopyColor;
			}
		}	
	}

	



