@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";


#app {
	.yunoCarouselList {
		padding: $gapLargest 0;

		@media (min-width: 768px) {
			padding: $gapLargest * 2 0;
		}

		> .container {
			position: relative;
			overflow: hidden;
			padding-bottom: $gap15;
		}

		.sectionTitle {
			font-size: $fontSizeLargest;
			text-align: center;
			margin-bottom: $gapSmall;

			@media (min-width: 768px) {
				margin: 0 0 $gapLargest;
			}
		}

		.tns-outer {
			.tns-controls {
				display: flex;
				justify-content: flex-end;
			}

			.tns-liveregion {
				display: none;
			}

			.tns-ovh {
				margin: 0 -15px;
			}
		}

		.carouselList {
			display: flex;
			align-items: stretch;
		}

		.carouselListControls {
			display: flex;
			justify-content: center;
			margin-bottom: $gapSmall;

			button {
				border: 0;
				background: none;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 35px;
				height: 35px;
				margin: 0;
				padding: 0;

				&:focus {
					outline: none;
				}

				.fa {
					font-size: 18px;
				}
			}

			@media (min-width: 768px) {
				
				position: absolute;
				right: 10px;
				top: 0;
			}
		}

		.carouselCard {
			padding: 0 15px;

			.innerWrap {
				border: 1px solid $grey;
				position: relative;
				border-radius: 4px;
				padding-bottom: 56px;
				overflow: hidden;
				height: 100%;
			}

			&:hover {
				.innerWrap {
					box-shadow: rgba(0,0,0,.117647) 0 0 20px;	
				}
			}

			.cardImg {
				img {
					width: 100%;
					height: auto;
				}
			}

			.cardBody {
				padding: $gapLarge;
			}

			.cardFooter {
				padding: $gapLarge;
				position: absolute;
				left: 0;
				bottom: 0;
				width: 100%;

				a {
					color: $primaryColor;
					text-decoration: underline;
				}
			}

			.cardTitle {
				font-size: $fontSizeLarger;
				margin: 0 0 $gapSmall;
			}

			.cardPrice {
				font-size: $fontSizeLarge;
				margin: 0;
				color: $secondaryColor;
			}
		}
	}
}