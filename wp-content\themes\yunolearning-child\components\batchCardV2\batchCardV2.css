#app .batchCardV2 {
  border-radius: 4px;
  border: 1px solid #E6E6E6;
  background: #FFF;
  padding: 15px;
}

#app .batchCardV2 .fontColorDark, #app .batchCardV2 .hasArray .caption2.active {
  color: #201A19;
}

#app .batchCardV2 .fontColorDarkVariant {
  color: #534342;
}

#app .batchCardV2 .fontColorDarkVariant2 {
  color: rgba(0, 0, 0, 0.38);
}

#app .batchCardV2 .h3 {
  font-size: 28px;
  line-height: 32px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .batchCardV2 .h4 {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .batchCardV2 .caption1 {
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .batchCardV2 .caption2 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .batchCardV2 .caption3 {
  font-size: 14px;
  line-height: 18px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .batchCardV2 .cardHeader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

#app .batchCardV2 .batchDate {
  width: 75px;
  margin-right: 15px;
  border-radius: 4px;
  border: 1px solid #E6E6E6;
  background: #FFF;
  overflow: hidden;
  text-align: center;
}

#app .batchCardV2 .batchDate .month {
  background: #201A19;
  color: #FFF;
  padding: 4px 0;
}

#app .batchCardV2 .batchDate .date {
  margin-top: 10px;
}

#app .batchCardV2 .batchDate .day {
  margin-bottom: 10px;
}

#app .batchCardV2 .batchInfo {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 90px);
          flex: 0 0 calc(100% - 90px);
}

#app .batchCardV2 .timeAmount {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #E6E6E6;
  padding-bottom: 5px;
  margin-bottom: 5px;
}

#app .batchCardV2 .hasArray .caption2 {
  margin-right: 10px;
  color: #201A1933;
}

#app .batchCardV2 .colView {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 10px -15px 0;
}

#app .batchCardV2 .colView li {
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 5px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (min-width: 768px) {
  #app .batchCardV2 .colView li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-bottom: 0;
  }
}

#app .batchCardV2 .colView li .ctaLook {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 100px;
  background: #FFFBFF;
  padding: 5px 10px;
}

#app .batchCardV2 .colView li .ctaLook .material-icons-outlined {
  font-size: 18px;
  margin-right: 5px;
}

#app .batchCardV2 .colView li.hasPipe {
  position: relative;
}

@media (min-width: 768px) {
  #app .batchCardV2 .colView li.hasPipe::after {
    content: "";
    width: 1px;
    height: 40%;
    background: #E6E6E6;
    position: absolute;
    right: 0;
    top: 30%;
  }
  #app .batchCardV2 .colView li.hasPipe:last-child {
    padding-right: 0;
    margin-right: 0;
  }
  #app .batchCardV2 .colView li.hasPipe:last-child::after {
    display: none;
  }
}

#app .batchCardV2 .colView li.hasSeparator .caption {
  margin: 0;
  position: relative;
  padding-right: 10px;
  margin-right: 10px;
}

@media (min-width: 768px) {
  #app .batchCardV2 .colView li.hasSeparator .caption::after {
    content: "";
    width: 1px;
    height: 80%;
    background: #E6E6E6;
    position: absolute;
    right: 0;
    top: 5%;
  }
  #app .batchCardV2 .colView li.hasSeparator .caption:last-child {
    padding-right: 0;
    margin-right: 0;
  }
  #app .batchCardV2 .colView li.hasSeparator .caption:last-child::after {
    display: none;
  }
}

#app .batchCardV2 .cardFooter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: 30px;
}

#app .batchCardV2 .cardFooter .colView {
  margin: 5px 0 0;
}

#app .batchCardV2 .cardFooter .colView li:first-child {
  padding-left: 0;
}

#app .batchCardV2 .cardFooter .colView li .material-icons {
  color: #F9B600;
  font-size: 18px;
  margin-left: 5px;
}

#app .batchCardV2 .cardFooter .caption3 {
  text-transform: capitalize;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

#app .batchCardV2 .cardFooter .avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: 0;
  background-color: #E6E6E6;
}
/*# sourceMappingURL=batchCardV2.css.map */