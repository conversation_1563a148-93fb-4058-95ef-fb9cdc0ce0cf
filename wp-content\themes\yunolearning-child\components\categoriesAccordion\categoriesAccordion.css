#app .categoryTaxonomy {
  background: #FFF;
  border: 1px solid #E6E6E6;
  border-top: 0;
}

#app .categoryTaxonomy:first-child {
  border-top: 1px solid #E6E6E6;
}

#app .categoryTaxonomy .collapse-trigger {
  display: block;
  padding: 0;
}

#app .categoryTaxonomy .collapse-trigger .b-radio {
  margin: 0;
}

#app .categoryTaxonomy .collapseHeader {
  position: relative;
}

#app .categoryTaxonomy .collapseHeader.menuDown {
  -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0 4px 6px;
          box-shadow: rgba(0, 0, 0, 0.1) 0 4px 6px;
}

#app .categoryTaxonomy .collapseHeader .b-radio {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: 15px 15px;
}

#app .categoryTaxonomy .collapseHeader .b-radio + .error {
  display: none;
  margin: 0;
  padding: 0 0 15px 45px;
}

#app .categoryTaxonomy .collapseHeader .b-radio.invalid + .error {
  display: block;
}

#app .categoryTaxonomy .collapseHeader .fa {
  position: absolute;
  right: 15px;
  top: calc(50% - 8px);
}

#app .categoryTaxonomy .collapse-content {
  display: block;
  padding: 15px 30px;
}

#app .categoryTaxonomy .collapse {
  margin-bottom: 15px;
}

#app .categoryTaxonomy .collapse:last-child {
  margin-bottom: 0;
}

#app .categoryTaxonomy .collapse .collapse-trigger {
  position: relative;
  padding-left: 18px;
}

#app .categoryTaxonomy .collapse .collapse-trigger .fa {
  position: absolute;
  left: 0;
  top: 3px;
}

#app .categoryTaxonomy .collapse .collapse-content {
  padding-bottom: 0;
  position: relative;
}

#app .categoryTaxonomy .collapse .collapse-content:before {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #E6E6E6;
  position: absolute;
  left: 4px;
  top: 0;
}

#app .categoryTaxonomy .collapse .sub2Content .field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
}

#app .categoryTaxonomy .collapse .sub2Content .field:before {
  content: "";
  width: 100%;
  height: 1px;
  background-color: #E6E6E6;
  position: absolute;
  left: -25px;
  top: 10px;
  z-index: 1;
}

#app .categoryTaxonomy .collapse .sub2Content .field:after {
  content: "";
  background: #FFF;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
}

#app .categoryTaxonomy .collapse .sub2Content .field:last-child {
  margin-bottom: 0;
}

#app .categoryTaxonomy .collapse .sub2Content .field .b-checkbox {
  position: relative;
  z-index: 3;
}

#app .categoryTaxonomy .collapse .trigger .field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .categoryTaxonomy .collapse .trigger .b-checkbox {
  margin: 0;
}
/*# sourceMappingURL=categoriesAccordion.css.map */