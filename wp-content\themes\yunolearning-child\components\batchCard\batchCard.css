#app .batchCard .innerWrapper {
  border: 1px solid #E6E6E6;
  border-radius: 4px;
  height: 100%;
  overflow: hidden;
}

#app .batchCard .innerWrapper .noLPad {
  padding-left: 0;
}

#app .batchCard .innerWrapper .noRPad {
  padding-right: 0;
}

#app .batchCard .batchTitle {
  color: #000;
  font-size: 18px;
  padding: 15px 0;
  margin: 0;
}

#app .batchCard .batchTitle a {
  color: #000;
}

#app .batchCard .batchStart {
  text-align: center;
  padding-top: 15px;
}

#app .batchCard .batchStart .date {
  font-size: 46px;
  font-weight: 600;
  line-height: 46px;
}

#app .batchCard .batchStart .month {
  color: rgba(0, 0, 0, 0.5);
  font-size: 24px;
  text-transform: uppercase;
  margin-bottom: 10px;
  line-height: 24px;
}

#app .batchCard .batchStart .year {
  background-color: rgba(0, 0, 0, 0.1);
  font-size: 14px;
  border-radius: 3px;
  display: inline-block;
  padding: 3px 8px;
}

#app .batchCard .startsIn {
  font-size: 14px;
  padding: 15px 0 0 15px;
}

@media (min-width: 992px) {
  #app .batchCard .startsIn {
    padding: 15px 0 0;
  }
}

#app .batchCard .batchInsight {
  font-size: 14px;
  padding-top: 15px;
}

#app .batchCard .batchInsight.demo {
  padding: 0 0 15px;
}

#app .batchCard .batchInsight li {
  position: relative;
  padding-left: 60px;
  margin-bottom: 5px;
  font-size: 14px;
}

#app .batchCard .batchInsight li.fatSize {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 10px;
}

#app .batchCard .batchInsight li .batchLabel {
  color: rgba(0, 0, 0, 0.5);
  position: absolute;
  left: 0;
  top: 0;
}

#app .batchCard .instructorDetails {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 5px 15px;
  margin-top: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .batchCard .instructorDetails .img {
  width: 32px;
  height: 32px;
  overflow: hidden;
  border-radius: 50%;
}

#app .batchCard .instructorDetails .img img {
  width: 32px;
  height: 32px;
}

#app .batchCard .instructorDetails .instructorInfo {
  font-size: 12px;
  padding-left: 5px;
}

#app .batchCard .instructorDetails .instructorInfo .instructorLabel {
  color: #abacad;
  display: block;
}

#app .batchCard .instructorDetails .instructorInfo .name a {
  color: #000;
}

#app .batchCard .batchCardFooter {
  padding: 15px 0;
  margin: 15px 15px 0;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  border-top: 1px solid #E6E6E6;
}

#app .batchCard .batchCardFooter .price {
  font-size: 24px;
  font-weight: 500;
}

#app .batchCard .daysOfWeek {
  padding: 10px 24px 0;
}

#app .batchCard .daysOfWeek .daysOfWeekRow {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding-top: 10px;
}

#app .batchCard .daysOfWeek .daysOfWeekRow .label {
  margin-bottom: 5px;
  color: rgba(0, 0, 0, 0.5);
}

#app .batchCard .daysOfWeek .daysOfWeekRow .tag {
  border-radius: 20px;
  font-size: 12px;
  padding: 4px 10px;
  color: #FFF;
  margin-right: 5px;
  margin-bottom: 5px;
  background-color: rgba(0, 0, 0, 0.3);
}

#app .batchCard .daysOfWeek .daysOfWeekRow .tag.active {
  background-color: #a62027;
}

#app .batchCard .daysOfWeek .daysOfWeekRow .tag:last-child {
  margin-right: 0;
}

@media (min-width: 768px) {
  #app .batchCard .daysOfWeek {
    padding: 0;
  }
}

#app .batchCard .forCourseDetail .enrollment {
  display: none;
}

#app .batchCard:hover .forCourseDetail .enrollment {
  display: block;
}

#app .batchCard.demo .batchStart {
  padding-bottom: 15px;
}

#app .batchCard.demo .enrollCtaWrapper {
  padding: 0 0 15px;
}
/*# sourceMappingURL=batchCard.css.map */