@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
	.yunoChannelCourse {
		padding: $gapLargest 0;

		a.primaryColor {
			text-decoration: underline;
		}

		.sectionTitle {
			font-size: $fontSizeLargest;
			font-weight: 600;

			a {
				color: $primaryCopyColor;
			}

			@media (min-width: 768px) {
				font-size: $fontSizeLargest + 16;
			}
		}

		.chooseOpt {
			margin-top: $gap15;
		}

		.sectionSubTitle {
			font-size: $fontSizeLarger;
			font-weight: 400;
			margin: 0;

			.subEle {
				display: inline-block;
				
				&:before {
					content: "|";	
					@include setFontColor($primaryCopyColor, 0.1);
					margin: 0 $gapSmall 0 $gapSmall;
				}

				&.noSubtitle {
					&:before {
						display: none;
					}	
				}
			}
		}

		.sectionMeta {
			margin: $gapLarge 0 $gapLargest;
			flex-wrap: wrap;
			align-items: center;

			li {
				margin-right: $gapLarge;
			}

			.price { 
				font-size: $fontSizeLargest;
			}

			.price, .ctaWrapper {
				flex: 0 0 auto;
				margin: 0 $gapLarge 0 0;
			}
		}

		.sectionMedia {
			img {
				width: 100%;
				height: auto;
			}

			.yunoYoutube {
		        width: 100%;
		        height: 357px;
		    }
		}

		.sectionDescription {
			margin-top: $gapLargest;

			.descriptionTitle {
				font-weight: 400;
				font-size: $fontSizeLarger + 6;
				margin-bottom: $gapLarge;
			}

			p {
				font-size: $fontSizeLarge;
				margin-bottom: 0;
			}
		}
	}
}