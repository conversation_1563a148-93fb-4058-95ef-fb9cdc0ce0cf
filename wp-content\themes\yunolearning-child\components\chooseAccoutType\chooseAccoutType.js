Vue.component('yuno-choose-account-type', {
    template: `
        <b-modal 
            :active.sync="chooseAccountTypeModal.modal" 
            :width="920" 
            :can-cancel="['escape', 'x']"
            @close="close" 
            class="yunoModal lightTheme chooseAccountType">
                <template v-if="chooseAccountTypeModal.modal">
                    <div class="modalBody">
                        <h2 class="h1">{{ details.title }}</h2>
                        <p class="caption1">{{ details.subtitle }}</p>
                        <div class="accountTypes">
                            <div v-for="(type, index) in details.accountTypes" :key="index" class="accountType">
                                <div class="innerWrapper">
                                    <h3 class="h2">{{ type.title }}</h3>
                                    <ul>
                                        <li v-for="(feature, index) in type.features" :key="index">
                                            <span class="material-icons" :class="[feature.isAllowed ? 'isGreen' : 'isRed']">
                                                {{ feature.isAllowed ? 'done' : 'close' }}
                                            </span>
                                            <p>{{ feature.label }}</p>
                                        </li>
                                    </ul>
                                </div>
                                <div class="ctaWrapper">
                                    <template v-if="type.cta.disabled">
                                        <b-tooltip label="Coming Soon!"
                                            type="is-dark"
                                            position="is-top">
                                            <b-button
                                                tag="a"
                                                href="/login/?type=signup"
                                                disabled="type.cta.disabled"
                                                @click="initLoginPage(type.cta)"
                                                class="yunoSecondaryCTA button fat"
                                            >
                                                {{ type.cta.label }}
                                            </b-button>
                                        </b-tooltip>
                                    </template>
                                    <template v-else>
                                        <b-button
                                            tag="a"
                                            :href="type.cta.url"
                                            :target="type.cta.target"
                                            :disabled="type.cta.disabled"
                                            @click="initLoginPage(type.cta)"
                                            class="yunoSecondaryCTA button fat"
                                        >
                                            {{ type.cta.label }}
                                        </b-button>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
        </b-modal>
    `,
    data() {
        return {
            details: {
                title: "Choose the type of account",
                subtitle: "You can sign up as an individual instructor or an academy owner. See the difference below.",
                accountTypes: [
                    {
                        title: "Academy Owners",
                        features: [
                            {
                                label: "Can create & share study material and practice tests",
                                isAllowed: true
                            },
                            {
                                label: "Can publish courses and batches",
                                isAllowed: true
                            },
                            {
                                label: "Can invite a team of instructors to teach",
                                isAllowed: true
                            },
                            {
                                label: "Can collect reviews and ratings on their academy profile",
                                isAllowed: true
                            },
                            {
                                label: "Can promote courses, enroll learners & collect payments",
                                isAllowed: true
                            },
                        ],
                        cta: {
                            label: "Learn More",
                            action: "academyOwner",
                            url: "/create-academy/",
                            target: "_self",
                            disabled: false
                        }
                    },
                    {
                        title: "Independent Instructors",
                        features: [
                            {
                                label: "Can create & share study material and practice tests",
                                isAllowed: true
                            },
                            {
                                label: "Cannot publish courses or create batches",
                                isAllowed: false
                            },
                            {
                                label: "Can schedule classes and teach online, but as an instructor of an academy",
                                isAllowed: true
                            },
                            {
                                label: "Can collect reviews and ratings on their instructor profile",
                                isAllowed: true
                            },
                            {
                                label: "Since they cannot publish courses, they can't enroll learners & collect payments",
                                isAllowed: false
                            },
                        ],
                        cta: {
                            label: "Learn More",
                            action: "instructor",
                            url: "/become-an-instructor/",
                            target: "_self",
                            disabled: false
                        }
                    }
                ]
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'chooseAccountTypeModal'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        close() {
            sessionStorage.removeItem("landingPage");
        },
        initLoginPage(cta) {
            if (!this.user.isLoggedin) {
                const roleMap = {
                    academyOwner: 'org-admin',
                    instructor: 'instructor'
                };
        
                const page = {
                    url: "",
                    pageTitle: "",
                    category: "",
                    role: roleMap[cta.action] || "",
                };
        
                sessionStorage.setItem('landingPage', JSON.stringify(page));
            }
        }
    }
});