@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";
.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.ylIcon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'yuno-icon' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    
    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
    font-family: 'Material Icons Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
  
  .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

.dark87 {
    @include setFontColor($primaryCopyColor, 0.87);
}

.dark60 {
    @include setFontColor($primaryCopyColor, 0.6);
}

#app {
    .checkedItemsCards {
        padding: $gapLargest 0;
        @extend .dark87;
        
        @media (min-width: 768px) {
            padding: $gapLargest * 2 0 0;
        }

        .alignC {
            text-align: center
        }

        .largestTitle {
            @include setFont($headline3, 40px, 500, $gapSmall);
        }

        .largerTitle {
            @include setFont($headline5, 28px, 700, 0);
        }

        .largeTitle {
            @include setFont($headline6, 28px, 700, 0);

            @media (min-width: 768px) {
                @include setFont($headline5, 28px, 700, 0);
            }
        }

        .smallCaption {
            @include setFont($subtitle1, 24px, 500, 0);
        }

        .smallerBody {
            @include setFont($body2, 20px, 400, 0);
            @extend .dark60;
        }

        h2.largestTitle {
            text-align: center;
        }

        .yunoTabsV2 {
            margin-top: $gapLarger;
    
            .collapse:not(.show) {
                display: block;
            }
    
            .modal-background {
                background-color: rgba(10,10,10,0.5);
            }
    
            &.noTopGap {
                margin-top: 0;
            }
    
            .tab-item {
                min-height: 200px;
            }
    
            &.stickyEnabled {
                > .tabs {
                    position: sticky;
                    top: 0;
                    z-index: 11;
                    @media (min-width: 768px) {
                        top: 75px;
                        z-index: 9;    
                    }
                }
            }
    
            .tabs {
                background: $whiteBG;
                position: relative;
    
                ul {
                    @include setBorderColor($primaryCopyColor, 0.08);
                    justify-content: center;
    
                    li {
                        font-size: $subtitle2;
    
                        a {
                            @include setFontColor($primaryCopyColor, 0.38);
                            padding: 20px 20px;
                            display: block;
                            border-bottom: 1px solid transparent;
        
                            &:hover {
                                text-decoration: none;
                            }
    
                            @media (min-width: 768px) {
                                padding: $gap15 20px;
                            }
                        }
        
                        &.is-active {
                            a {
                                color: $primary;
                                @include setBorderColor($primary, 1);
                                font-weight: 400;
                                background: transparent
                            }
                        }
                    }
                }
            }
    
            &.sizeMedium {
                .tabs {
                    ul {
                        li {
                            font-size: $caption2;
                            a {
                                padding: 10px 20px;
                            }
                        }
                    }
                }
            }
            
            .tab-content {
                padding: $gap15 0;
            }
    
            .statsWrapper {
                justify-content: center;
            }
    
            .statsCard {
                background: $whiteBG;
                box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
                border-radius: 4px;
                padding: $gap15;
    
                p {
                    font-size: $headline4;
                    @extend .dark87;
                    text-align: center;
                }
    
                h3 {
                    @extend .dark60;
                    font-size: $overline;
                    text-align: center;
                    text-transform: uppercase;
                    font-weight: 500;
                }
            }
            
            .aboutUser {
                padding: $gapLargest 0;
    
                h2 {
                    font-size: $headline5;
                    line-height: 28px;
                    margin-bottom: $gap15;
                    @extend .dark87;
                }
    
                p {
                    font-size: $body2;
                    line-height: 20px;
                    @extend .dark60;
                }
            }
    
            .filtersWrapper {
                padding: 0 $gap15 $gap15;
                display: flex;
                flex-wrap: wrap;
                margin: 0 (-$gap15) $gap15;
        
                @media (min-width: 768px) {
                    padding: 0 0 $gapSmaller;
                }
        
                &.loading {
                    margin-top: $gapLarger;
                    .b-skeleton {
                        flex: 0 0 100px;
                        padding: 0 $gap15 $gap15;
                        margin: 0;
                    }
                }
        
                .fa-filter {
                    font-size: $fontSizeLarger + 6;
                    padding-right: $gap15;
                }
        
                .button {
                    &.is-primary {
                        background-color: $whiteBG;
                        color: $primaryCopyColor;
                        font-size: $body2;
                        @extend .dark87;
                        @include setBorderColor($primaryCopyColor, 0.12);
                        font-size: $fontSizeSmall;
                        border-radius: 4px;
                        padding: 8px $gap15 9px;
        
                        &:active, &:focus {
                            box-shadow: none;
                        }
                    }
        
                    .icon {
                        margin: 0 0 0 $gapSmaller;
                        position: relative;
                        top: 1px;
        
                        .mdi {
                            &.mdi-menu-down, &.mdi-menu-up {
                                &:after {
                                    content: "\e5c5";
                                    @extend .material-icons;
                                }
                            }
        
                            &.mdi-menu-up {
                                &:after {
                                    content: "\e5c7";
                                }
                            }
                        }
                    }
                    
                }
        
                .filterMenu {
                    flex: 0 0 100%;
                    margin-bottom: $gap15;
                    margin-left: 0;
        
                    &.active {
                        button.filter {
                            @include setBorderColor($primary, 1);
                            @include setBGColor($primary, 0.04);
                            color: $primary;
        
                            .icon {
                                color: $primary;
                            }
                        }
                    }
        
                    &.is-active {
                        button {
                            @include setBorderColor($primaryCopyColor, 0.87);
                        }
                    }
        
                    &.multiSelect:not(.class_days_time) {
                        .dropdown-menu {
                            padding: 0;
                        }
        
                        .dropdown-content {
                            padding: 0;
        
                            
                            a:not(.dropdownTitle) {
                                padding: 6px 15px 6px 40px;
                                position: relative;
        
                                &:before {
                                    content: "unCheck";
                                    @extend .ylIcon;
                                    position: absolute;
                                    left: 15px;
                                    top: 10px;
                                }
        
                                &.is-active {
                                    &:before {
                                        content: "checked";
                                    }
                                }
                            }
                        }
        
                        .ctaWrapper {
                            display: flex;
                            justify-content: space-between;
                            margin: $gapSmall $gap15 0;
                            padding: $gap15 0;
                            border-top: 1px solid;
                            @include setBorderColor($primaryCopyColor, 0.12);
        
                            .yunoSecondaryCTA {
                                border-color: $primary;
                            }
                        }
        
                        &.class_time {
                            .dropdown-content {
                                display: flex;
                                padding: $gapSmall $gap15 $gapSmall $gapSmall;
                                flex-wrap: wrap;
        
                                @media (min-width: 768px) {
                                    width: 440px;
                                }
        
                                .dropdown-item {
                                    flex: 0 0 25%;
                                    display: flex;
                                    flex-direction: column;
                                    padding: $gapSmall $gap15;
                                    align-items: center;
                                    @extend .dark87;
                                    border: 1px solid transparent;
        
                                    &.is-active {
                                        @include setBorderColor($primary, 1);
                                        @include setBGColor($primary, 0.04);    
                                    }
        
                                    &:before {
                                        font-family: 'Material Icons' !important;
                                        @extend .material-icons;
                                        position: static;
                                    }
        
                                    &.morning {
                                        &:before {
                                            content: "\e1c6";
                                        }
                                    }
        
                                    &.afternoon {
                                        &:before {
                                            content: "\e518";
                                        }
                                    }
        
                                    &.evening {
                                        &:before {
                                            content: "\e1c6";
                                        }
                                    }
        
                                    &.night {
                                        &:before {
                                            content: "\e51c";
                                        }
                                    }
        
                                    .itemCaption {
                                        padding: $gapSmaller 0;
                                        font-size: $subtitle1;
                                        font-weight: 500;
                                    }
        
                                    .itemLabel {
                                        @include setFont($overline, normal, 400, 0);
                                        letter-spacing: 1.5px;
                                        @extend .dark60;
                                        text-transform: uppercase;
                                    }
        
                                }
                            }
        
                            .ctaWrapper {
                                flex: 0 0 100%;
                                margin: 0;
                                padding-bottom: $gapSmaller;
                            }
                        }
        
                        &.class_days {
                            .dropdown-content {
                                display: flex;
                                padding: $gapSmaller $gap15 $gapSmall $gapSmall;
                                flex-wrap: wrap;
        
                                @media (min-width: 768px) {
                                    width: 440px;
                                }
        
                                .dropdown-item {
                                    flex: 0 0 59px;
                                    padding: $gapSmall $gap15 0 0;
                                    box-sizing: border-box;
        
                                    &:hover {
                                        background: none;
                                    }
        
                                    &:before {
                                        display: none;
                                    }
        
                                    .itemLabel {
                                        display: block;
                                        background: rgba(0, 0, 0, 0.02);
                                        border-radius: 100px;
                                        @include setFont($overline, 16px, 500, 0);
                                        text-transform: uppercase;
                                        text-align: center;
                                        border: 1px solid transparent;
                                        padding: 6px $gapSmaller;
        
                                        &:hover {
                                            @include setBorderColor($primaryCopyColor, 0.60);
                                        }
                                    }
        
                                    &.is-active {
                                        .itemLabel {
                                            @include setBorderColor($primary, 1);
                                            @include setBGColor($primary, 0.04);    
                                            color: $primary;
                                        }
                                    }
                                }
                            }
        
                            .ctaWrapper {
                                flex: 0 0 100%;
                                margin: $gap15 0 0;
                                padding-bottom: $gapSmaller;
                            }
                        }
                    }
        
                    &.class_days_time {
                        .ctaWrapper {
                            display: flex;
                            justify-content: space-between;
                            margin: $gapSmall $gapSmall 0;
                            padding-top: $gapSmall;
                            border-top: 1px solid;
                            @include setBorderColor($primaryCopyColor, 0.12);
                            
                            .button {
                                border-color: $primary;
                            }
                        }
                    }
        
                    .filterSet {
                        .listCaption {
                            @include setFont($subtitle2, 24px, 500, 0);
                            @extend .dark87;
                            padding: 0 $gapSmall;
                        }
        
                        &.class_days {
                            .wrapper {
                                display: flex;
                                padding: $gapSmaller $gap15 $gapSmall $gapSmall;
                                flex-wrap: wrap;
        
                                @media (min-width: 768px) {
                                    width: 440px;
                                }
                            }
        
                            .dropdown-item {
                                flex: 0 0 59px;
                                padding: $gapSmall $gap15 0 0;
                                box-sizing: border-box;
        
                                &:hover {
                                    background: none;
                                }
        
                                &:before {
                                    display: none;
                                }
        
                                .itemLabel {
                                    display: block;
                                    background: rgba(0, 0, 0, 0.02);
                                    border-radius: 100px;
                                    @include setFont($overline, 16px, 500, 0);
                                    text-transform: uppercase;
                                    text-align: center;
                                    border: 1px solid transparent;
                                    padding: 6px $gapSmaller;
        
                                    &:hover {
                                        @include setBorderColor($primaryCopyColor, 0.60);
                                    }
                                }
        
                                &.is-active {
                                    .itemLabel {
                                        @include setBorderColor($primary, 1);
                                        @include setBGColor($primary, 0.04);    
                                        color: $primary;
                                    }
                                }
                            }
                        }
        
                        &.class_time {
                            .wrapper {
                                display: flex;
                                padding: $gapSmall $gap15 $gapSmall $gapSmall;
                                flex-wrap: wrap;
        
                                @media (min-width: 768px) {
                                    width: 440px;
                                }
        
                                .dropdown-item {
                                    flex: 0 0 25%;
                                    display: flex;
                                    flex-direction: column;
                                    padding: $gapSmall $gap15;
                                    align-items: center;
                                    @extend .dark87;
                                    border: 1px solid transparent;
        
                                    &.is-active {
                                        @include setBorderColor($primary, 1);
                                        @include setBGColor($primary, 0.04);    
                                    }
        
                                    &:before {
                                        font-family: 'Material Icons' !important;
                                        @extend .material-icons;
                                        position: static;
                                    }
        
                                    &.morning {
                                        &:before {
                                            content: "\e1c6";
                                        }
                                    }
        
                                    &.afternoon {
                                        &:before {
                                            content: "\e518";
                                        }
                                    }
        
                                    &.evening {
                                        &:before {
                                            content: "\e1c6";
                                        }
                                    }
        
                                    &.night {
                                        &:before {
                                            content: "\e51c";
                                        }
                                    }
        
                                    .itemCaption {
                                        padding: $gapSmaller 0;
                                        font-size: $subtitle1;
                                        font-weight: 500;
                                    }
        
                                    .itemLabel {
                                        @include setFont($overline, normal, 400, 0);
                                        letter-spacing: 1.5px;
                                        @extend .dark60;
                                        text-transform: uppercase;
                                    }
        
                                }
                            }
        
                            .ctaWrapper {
                                border: 0;
                                margin-bottom: 0;
                                padding-bottom: 0;
                            }
                        }
                    }
        
                    .dropdown-trigger {
                        width: 100%;
                    }
        
                    @media (min-width: 768px) {
                        flex: 0 0 auto;
                        margin-bottom: 0;
                        margin: $gapSmall 0 0 $gap15; 
                    }
        
                    button {
                        width: 100%;
                        justify-content: space-between;
        
                        @media (min-width: 768px) {
                            width: auto;
                            justify-content: center;
                        }
        
                        > span {
                            text-transform: capitalize;
                        }
                    }
                    .dropdown-content {
                        box-shadow: none;
                        border:0;
                        max-height: 300px;
                        overflow: hidden;
                        overflow-y: auto;
        
                        a {
                            @include setFontColor($primaryCopyColor, 0.5);
        
                            &.is-active {
                                background: none;
                                @include setFontColor($primaryCopyColor, 1);
                            }
        
                            &:active, &:focus {
                                background: none;
                                outline: none;
                            }
                        }
                    }
        
                    
                }
        
                
            }
        
            .clearFilters {
                @extend .dark87;
                display: flex;
                align-items: flex-start;
                margin-bottom: 0;
                margin-top: 0;
                flex-wrap: wrap;
        
                .tags {
                    margin: $gapSmall $gapSmall 0 0;
                    flex-wrap: nowrap;
        
                    .tag {
                        margin-bottom: 0;
                        border: 1px solid rgba(0, 0, 0, 0.08);
                        border-right: 0;
                        height: 28px;
                        background: rgba(0, 0, 0, 0.02);
                    }
        
                    .is-delete {
                        border-left: 0;
                        border-right: 1px solid rgba(0, 0, 0, 0.08);
                        @extend .dark87;
        
                        &::after {
                            display: none;
                        }
        
                        &::before {
                            content: "\e5cd";
                            @extend .material-icons;
                            left: 0;
                            top: 6px;
                            transform: none;
                            background: none;
                            font-size: 14px;
                        }
        
                        &:hover {
                            background: rgba(0, 0, 0, 0.02);
                            color: $primary;
                        }
                    }
        
                    &:not(:last-child) {
        
                    }
                }
        
                .multiChips {
                    display: flex;
                    margin: $gapSmall $gapSmall 0 0;
        
                    .tags:not(.placeholder) {
                        margin: 0;
        
                        .tag {
                            border-left: 0;
                            border-top-left-radius: 0;
                            border-bottom-left-radius: 0;
                            text-transform: capitalize;
                        }
        
                        .is-delete {
                            border-right: 0;
                            border-top-right-radius: 0;
                            border-bottom-right-radius: 0;
                        }
        
                        &:first-child {
                            
                        }
        
                        &:last-child {
                            .is-delete {
                                border-right: 1px;
                                border-top-right-radius: 290486px;
                                border-bottom-right-radius: 290486px;
                            }
                        }
                    }
        
                    .tags {
                        &.placeholder {
                            margin: 0;
        
                            .tag {
                                padding-right: 0;
                            }
        
                            .is-delete {
                                display: none;
                            }
                        }
                    }
                }
            }
    
            
    
            
        }

        .note {
            @include setFont($caption1, 16px, 400, 0);
            margin-top: $gapLargest;
            text-align: center;
            @extend .dark60;

            a {
                display: inline-flex;
                align-items: center;
            }

            .material-icons-outlined {
                font-size: 18px;
                margin-left: $gapSmaller;
            }

            @media (min-width: 768px) {
                width: 70%;
                margin: 0 auto;
                margin-top: $gapLargest;
            }
        }

        .grid {
            display: flex;
            margin: $gapLargest (- $gap15) 0;
            flex-wrap: wrap;
            align-items: stretch;

            &.alignCenter {
                justify-content: center;
            }

            .item {
                padding: 0 $gap15;
                flex: 0 0 100%;
                margin-bottom: $gapLargest;

                @media (min-width: 768px) {
                    flex: 0 0 33.3%;
                    margin-bottom: 0;
                }

                .wrapper {
                    border: 1px solid rgba(0, 0, 0, 0.08);
                    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
                    border-radius: 4px;
                    height: 100%;
                }

                .largerTitle {
                    background: rgba(0, 0, 0, 0.87);
                    color: white;
                    padding: 20px;
                    border-radius: 4px 4px 0 0;

                    small {
                        font-size: $subtitle2;
                        line-height: 20px;
                        font-weight: 500;
                        display: block;
                        margin-top: $gapSmaller;
                    }
                }

                .caption1 {
                    @include setFont($headline6, 24px, 500);
                    padding: 20px 20px 0;
                }

                .price {
                    padding: 0 20px 20px; 
                    @include setFont($headline5, 28px, 500);

                    span {
                        font-size: $subtitle2;
                    }
                }
            }

            .checkedList {
                margin: 20px;
                padding-bottom: 20px;
                border-bottom: 1px solid $grey;

                li {
                    margin-bottom: $gap15;
                    display: flex;
                    align-items: center;

                    &::before {
                        content: "\e876";
                        @extend .material-icons-outlined;
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        background: rgba(0, 0, 0, 0.02);
                        color: $onSurfaceVariant;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: $gap15;
                        font-size: 20px;
                    }

                    h4 {
                        @include setFont($body1, 24px, 400, 0);

                        small {
                            @include setFont($caption1, 16px, 400, 0);    
                            @extend .dark60;
                            display: block;
                        }
                    }

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
}