@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
    .modal.yunoModal.lightTheme {
        &.chooseAccountType {
            .fontColorDark {
                color: $onSurface;
            }
            
            .fontColorDarkVariant {
                color: $onSurfaceVariant;
            }
            
            .fontColorDarkVariant2 {
                @include setFontColor($primaryCopyColor, 0.38);
            }
        
            .h1 {
                @include setFont($headline4, 40px, 400);
                text-align: center;
            
                @media (min-width: 768px) {
                    @include setFont($headline2, 52px, 400);    
                }
            }
        
            .h2 {
                @include setFont($headline6, normal, 500);
            }
        
            .body1 {
                @include setFont($body1, 24px);
            }
        
            .caption1 {
                @include setFont($body2, 20px);
                text-align: center;
            }

            .modalBody {
                @media (min-width: 768px) {
                    padding-top: 40px;
                    padding-bottom: 40px;
                }
            }
        
            .accountTypes {
                display: flex;
                flex-wrap: wrap;
                margin: $gapLargest 0 0;
                justify-content: center;
        
                @media (min-width: 768px) {
                    margin: $gapLargest * 2 0 0;
                    gap: 14%;
                }
        
                .accountType {
                    flex: 0 0 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;

                    @media (min-width: 768px) {
                        flex: 0 0 40%;    
                    }

                    h3 {
                        margin-bottom: $gapLarger;
                    }

                    ul {
                        li {
                            display: flex;
                            margin-bottom: $gap15;
                            @extend .body1;

                            .material-icons {
                                margin-right: $gapSmall;

                                &.isGreen {
                                    color: #02CF3B;
                                }

                                &.isRed {
                                    color: #BA1A1A;
                                }
                            }
                        }
                    }

                    &:first-child {
                        margin-bottom: $gapLargest;

                        @media (min-width: 768px) {
                            position: relative;
                            margin-bottom: 0;

                            &::after {
                                content: "";
                                position: absolute;
                                top: 0;
                                right: -17%;
                                background-color:rgba(0, 0, 0, 0.08);;
                                width: 1px;
                                height: 100%;
                            }
                        }
                    }

                    
                }
            }
        
            .ctaWrapper {
                justify-content: flex-start;
                margin-top: $gap15;

                .button {
                    width: 100%;
                }
            }
        }
    }

}