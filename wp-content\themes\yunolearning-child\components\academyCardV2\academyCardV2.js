Vue.component("yuno-academy-card-v2", {
    props: {
        data: {
            type: Object,
            required: false,
        },
        isSkeleton: {
            type: Boolean,
            default: false,
        },
        cta: {
            type: Object,
            required: false,
        },
    },
    template: `
        <div class="sectionWrapper hasBorder gap15 academyCard makeGrid isWrap">
            <template v-if="isSkeleton">
                <figure class="mediaImg m-right-large-times-2 width120 m-bottom-small-times-3-mobile">
                    <b-skeleton height="120px" width="120px"></b-skeleton>
                </figure>
                <div class="academyContent calc140">
                    <h2 class="headline6 onSurface m-bottom-small-times-1">
                        <b-skeleton active width="200px"></b-skeleton>
                    </h2>
                    <ul class="makeGrid withPipe m-top-small-times-1">
                        <li class="subtitle2 noBold onSurfaceVariant makeGrid">
                            <b-skeleton active width="80px"></b-skeleton>
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant makeGrid">
                            <b-skeleton active width="80px"></b-skeleton>
                        </li>
                    </ul>
                    <ul class="makeGrid withPipe m-top-small-times-1">
                        <li class="subtitle2 noBold onSurfaceVariant makeGrid">
                            <b-skeleton active width="80px"></b-skeleton>
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant makeGrid">
                            <b-skeleton active width="80px"></b-skeleton>
                        </li>
                    </ul>
                </div>
            </template>
            <template v-else>
                <figure class="mediaImg m-right-large-times-2 width120 m-bottom-small-times-3-mobile">
                    <img 
                        :src="data.logo_url.url === '' ? 'https://placehold.co/120' : data.logo_url.url" 
                        :alt="data.name" class="radiusBorder"
                    >
                </figure>
                <div class="academyContent calc140">
                    <h2 class="headline6 onSurface m-bottom-small-times-1">{{ data.name }}</h2>
                    <div class="makeGrid" v-if="data.org.name !== ''">
                        <div class="roundImg16 m-right-small-times-1 makeGrid">
                            <img 
                                :src="data.fav_icon_url.url === '' ? 'https://placehold.co/16' : data.fav_icon_url.url" 
                                :alt="data.org.name" 
                                width="16" 
                                height="16"
                            >
                        </div>
                        <p class="subtitle2 noBold onSurfaceVariant">{{ data.org.name }}</p>
                    </div>
                    <ul class="makeGrid withPipe m-top-small-times-1">
                        <li class="subtitle2 noBold onSurfaceVariant">
                            {{ data.active_learners }} active enrollments
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant">
                            {{ data.past_learners }} past
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant">
                            1027 learners
                        </li>
                    </ul>
                    <ul class="makeGrid withPipe m-top-small-times-1">
                        <li class="subtitle2 noBold onSurfaceVariant">
                            Signed up: {{ data.created_time.time }}
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant">
                            Courses: {{ data.courses }}
                        </li>
                    </ul>
                    <div class="makeGrid vAlignCenter m-top-small-times-1">
                        <div class="usersList">
                            <span v-for="(learner, j) in 6" :key="j" v-if="j <= 5">
                                <img src="https://placehold.co/24" alt="sss" width="24" height="24">
                            </span>
                        </div>
                        <p class="caption1 noBold onSurfaceVariant"> {{ data.mapped_instructors_count }} {{ data.mapped_instructors_count > 1 ? "instructors" : "instructor" }}</p>
                    </div>
                </div>
                <div class="ctaWrapper">
                    <b-button tag="a" :href="cta.url" :target="cta.target"
                        class="yunoSecondaryCTA button">
                        {{ cta.label }}
                    </b-button>
                </div>
            </template>
        </div>
    `,
    data() {
        return {};
    },
    computed: {
        ...Vuex.mapState(["user"]),
    },
    async created() { },
    destroyed() { },
    mounted() { },
    methods: {},
});
