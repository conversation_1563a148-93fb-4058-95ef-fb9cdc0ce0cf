#app .dark87, #app .yunoCategories .sectionTitle, #app .yunoCategories .categoryCard .cardBody .cardTitle {
  color: rgba(0, 0, 0, 0.87);
}

#app .dark60, #app .yunoCategories .categoryCard .cardBody .cardDescription {
  color: rgba(0, 0, 0, 0.6);
}

#app .yunoCategories {
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .yunoCategories {
    padding: 60px 0 30px;
  }
}

#app .yunoCategories .sectionTitle {
  font-size: 32px;
  text-align: center;
  margin: 0;
}

#app .yunoCategories .categoryWrapper {
  padding-top: 30px;
}

#app .yunoCategories .categoryWrapper .col-12 {
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .yunoCategories .categoryWrapper .col-12 {
    margin-bottom: 30px;
  }
}

@media (min-width: 992px) {
  #app .yunoCategories .categoryWrapper .col-12 {
    margin-bottom: 0;
  }
}

#app .yunoCategories .categoryCard {
  background: #FFF;
  border-radius: 4px;
  overflow: hidden;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.07);
  padding: 24px;
  height: 100%;
}

#app .yunoCategories .categoryCard .cardImg img {
  width: 100%;
  height: auto;
}

#app .yunoCategories .categoryCard .cardBody {
  margin: 0;
  background: #FFF;
  padding-top: 15px;
}

#app .yunoCategories .categoryCard .cardBody .cardTitle {
  font-size: 16px;
  text-transform: capitalize;
  line-height: 24px;
}

#app .yunoCategories .categoryCard .cardBody .cardDescription {
  font-size: 14px;
  margin: 0;
  min-height: 120px;
}

#app .yunoCategories .categoryCard .cardFooter {
  padding-top: 0;
}

#app .yunoCategories .categoryCard .cardFooter .button {
  overflow: hidden;
  position: relative;
}

#app .yunoCategories .categoryCard .cardFooter .button .makeIndent {
  display: inline-block;
  text-indent: -99999px;
  position: absolute;
  left: 0;
  top: 0;
}
/*# sourceMappingURL=categories.css.map */