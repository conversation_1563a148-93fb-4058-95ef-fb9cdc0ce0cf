Vue.component("yuno-classCard-drawer", {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  template: `
    <div class="card-content">
        <div class="cardContentWrapper">
            <div class="videoWrapper" v-if="data?.status == 'past'" v-html="" ></div>
            <div class="classStatus" v-if="data.temporal_status">
                <span class="dot"></span>
                <span>LIVE CLASS</span>
            </div>
            <span class="classTitle headline5">This is the title of the class</span>
            <div class="d-flex align-items-center">
                <span class="onSurfaceVariant subtitle2 noBold">May 6,</span>
                <span class="onSurfaceVariant subtitle2 noBold">2024 - 7:00 (EST)</span>
                <span class="separator">|</span>
                <span class="onSurfaceVariant subtitle2 noBold">60 Minutes</span>
                <span class="separator">|</span>
                <div class="d-flex">
                    <div class="d-flex pr-3" v-if="data?.status == 'past'">
                       <span class="material-icons starIcon pr-1" >star</span>
                       <span class="onSurfaceVariant subtitle2 noBold">3 of 5</span>
                    </div>
                </div>
            </div>
            <div class="classDetails">
                <span class="onSurfaceVariant subtitle2">Batch: Name</span>
                <span class="separator">|</span>
                <span class="onSurfaceVariant subtitle2">ID: {{ data.id }}</span> 
            </div>
            <div class="learnerAttendence" v-if="data?.status == 'past' && userRole.data == 'Learner'">
               <template >
                    <div class="d-flex">
                        <span class="onSurfaceVariant pr-1">
                        Attendance: 60 minutes
                        </span>
                        <span class="onSurfaceVariant">
                        ( 100% )
                        </span>
                    </div>
                    <b-progress
                        type="is-info" 
                        :value="100"
                        style="flex-grow: 1;"
                    >
                    </b-progress>
                </template>
            </div>
            <template v-if="userRole.data == 'Learner'">
                <div class="userProfile" >
                    <span>
                        <img :src="data.instructor.user.image_url ">
                    </span>
                    <div class="userDescription">
                        <span class="onSurfaceVariant subtitle3">{{ data.instructor.user.role[0] }}</span>
                        <span class="onSurface subtitle2 underline">{{ data.instructor.user.full_name }}</span>
                    </div>
                </div>            
            </template>
            <template v-else-if="userRole.data == 'Instructor'">
                <ul class="learnerList pl-3">
                    <template>
                        <li v-for="i in 5">
                            <figure class="learnerMapped">
                                <img :src="data?.instructor.user.image_url" alt="Learner Image" />
                            </figure>
                        </li>
                    </template>
                    <span class="onSurfaceVariant subtitle2 noBold pl-2">
                     14 students enrolled
                    </span>
                    <template v-if="data?.status == 'past'">
                        <span class="onSurfaceVariant subtitle2 noBold pl-2">(60%)</span>
                        <b-progress
                            type="is-info"
                            :value="60"
                            style="flex-grow: 1;padding-left: 10px;"
                        >
                        </b-progress>
                    </template>
                </ul>
            </template>
        </div>
        <div class="buttonWrapper pt-4" v-if="data?.temporal_status">
            <div class="cta">
                <b-button 
                    tag="a"
                    class="button yunoPrimaryCTA"
                    :disabled="!data.virtual_classroom.meeting_url || data.virtual_classroom.meeting_url.trim() === ''"
                    :href="data.virtual_classroom.meeting_url"
                >
                    Launch Class
                </b-button>
            </div>
        </div>
        <div class="pt-4 d-flex flex-column pb-5" style="border-bottom: 1px solid #E6E6E6;">
            <span class="onSurfaceVariant overline pb-2">COURSE</span>
            <span class="subtitle1 onSurface">This is the title of the course to which the class belongs    </span>
            <div>
                <span></span>
                <span class="favIcon onSurface">Academy Name</span>
            </div>
        </div>
        <div class="dropdownWrapper" v-if="userRole.data == 'Instructor' && data?.status !== 'past'">
            <b-field >
                <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                    <b-select 
                        :class="classes"
                        placeholder="Open guest link"
                        expanded>
                        <option value="guest_link">
                            guest_link
                        </option>
                    </b-select>
                    <p class="error">{{errors[0]}}</p>
                </validation-provider>
            </b-field>
        </div>
        <div class="pt-4" v-if="userRole.data == 'Learner' && data?.status == 'past'">
            <span class="onSurface subtitle1 ">Class Reviews</span>
            <div class="classReview d-flex pt-2" v-for="i in 4" :key="i">
                <span>
                    <img :src="data.instructor.user.image_url">
                </span>
                <div class="learnerReviewWrapper">
                    <span class="onSurface subtitle2">Dharmendra Kumar</span>
                    <span class="onSurfaceVariant overline">May 24, 2024</span>
                    <div>
                        <span class="material-icons starIcon">star</span>
                        <span class="material-icons starIcon">star</span>
                        <span class="material-icons starIcon">star</span>
                        <span class="material-icons starIcon">star</span>
                        <span class="material-icons starIcon">star</span>
                    </div>
                    <div class="onSurfaceVariant subtitle2 noBold">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</div>
                </div>
            </div>
        </div>
        <div class="pt-4" v-else-if="userRole.data == 'Instructor' && data?.status == 'past'">
            <div class="d-flex justify-content-between align-items-baseline">
                <span class="onSurface subtitle2 ">Attendance</span>
                <div class="d-flex" style="gap:10px">
                    <b-field>                                     
                        <b-select 
                            placeholder="All attendance" size="is-small">
                            <option value="">All attendance</option>
                            <option value="3">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                        </b-select>
                    </b-field>
                    <b-field>                                     
                        <b-select 
                            placeholder="All rated" size="is-small">
                            <option value="">All rated</option>
                            <option value="3">1</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                        </b-select>
                    </b-field>
                </div>
            </div>
            <div class="instructorLearners">       
                <ul>
                    <li v-for="i in 5" :key="i">
                        <!-- Profile Section -->
                        <div class="learnerProfile">
                            <img :src="data.instructor.user.image_url" alt="Profile Picture" />
                            <span class="onSurfaceVariant subtitle2 noBold">Pooja bajaj</span>
                        </div>
                        <div class="learnerAttendance">
                            <span class="onSurfaceVariant subtitle2 noBold">36 minutes (59.8%)</span>
                            <b-progress
                                type="is-danger"
                                :value="59"
                            >
                            </b-progress>
                        </div>
                        <div class="learnerRating">
                            <span class="material-icons subtitle3 pb-1" style="color:#FFD700">star</span>
                            <span class="onSurfaceVariant subtitle2 noBold">1 of 5</span>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        </div>
    </div>
    `,
  data() {
    return {
      defaultImage:
        this.$store.state.themeURL + "/assets/images/instructor profile.png",
    };
  },
  computed: {
    ...Vuex.mapState(["userInfo", "userRole"]),
    formattedStartDate() {
      const startDate = new Date(this.data.scheduled.start.time);
      return startDate.toLocaleDateString("en-US", {
        day: "2-digit",
        month: "long",
        year: "numeric",
      });
    },
    formattedStartTime() {
      const startTime = new Date(this.data.scheduled.start.time);
      return startTime.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    },
    limitedEnrollments() {
      return this.data?.enrollments?.slice(0, 5) || [];
    },
  },
  methods: {
    closeSidebar() {
      this.$emit("closeSidebar");
    },
  },
});
