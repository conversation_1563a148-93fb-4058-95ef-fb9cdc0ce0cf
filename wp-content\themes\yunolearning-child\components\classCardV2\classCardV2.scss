$pureRed: #ff0000;
$brightCrimson: #ef4444;
$darkGray: #5f6368;
$semiTransp: rgba(0, 0, 0, 0.6);
$lightGray: #b0b0b0;
$bgtransp: transparent;
$bgPeach : #ffe5cc;
$bgOffWhite : #fafafa;
@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
  .cardSection {
    .card-content {
      border: 1px solid $grey;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 15px 25px;
      cursor: pointer;
      &:hover {
        border-color: $primary;
      }
      .cardContentWrapper {
        display: flex;
        flex-direction: column;
        gap: 5px;
        .classStatus {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 10px;
          font-weight: 500;
          line-height: 14px;
          letter-spacing: 1.5px;
          text-align: left;
          text-underline-position: from-font;
          -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
          color: $pureRed;
          .dot {
            border: 1px solid;
            border-radius: 50%;
            height: 10px;
            width: 10px;
            background:$pureRed;
          }
        }
        .learnerAttendence {
          display: flex;
          align-items: center;
          gap: 7px;
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          letter-spacing: 0.25px;
          .bgLightPink {
            background:$primaryVariant;
            color: $brightCrimson;
          }
        }
        .classType {
          font-size: 10px;
          font-weight: 500;
          line-height: 14px;
          letter-spacing: 1.5px;
          text-align: center;
          width: 80px;
          padding: 3px;
          border-radius: 4px;
        }
        .classType.bgLightBlue {
          background: #cce5ff;
        }
        .classType.lightPeach {
          background-color: $bgPeach ;
        }
        .classType.bgLightGreen {
          background: #d4edda;
          color: $onSurfaceVariant;
        }
        .classTitle {
          font-size: 16px;
          font-weight: 500;
          line-height: 20px;
          letter-spacing: 0.15px;
          color: $onSurface;
        }
        .underline {
          text-decoration-line: underline;
          text-decoration-style: solid;
        }
        .classDetails {
          display: flex;
          align-items: center;
          gap: 12px;
          span {
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
            letter-spacing: 0.25px;
          }
          .recordingDuration {
            display: flex;
            align-items: center;
            gap: 4px;
            .playIcon {
              border: 2px solid #5f6368;
              border-radius: 50%;
              color: $darkGray;
              width: 18px;
              height: 18px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
        .userProfile {
          display: flex;
          gap: 5px;
          align-items: center;
          span {
            img {
              border-radius: 50%;
              width: 34px;
              height: 35px;
            }
          }
          .userDescription {
            display: flex;
            flex-direction: column;
            gap: 1px;
          }
        }
        .progress-wrapper {
          .progress {
            width: 137px;
            height: 7px;
          }
        }
        .learnerList {
          display: flex;
          align-items: center;
          li {
            margin-left: -12px;
            img {
              width: 24px;
              height: 24px;
              border-radius: 50%;
            }
          }
        }
      }
      .buttonWrapper {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 20px;
        padding-top: 10px;
        a {
          width: 100%;
          &.secondaryCTA {
            padding: 10px 20px;
            border-radius: 4px;
            border: 1px solid #d0c4c2;
            background-color: #fff;
            line-height: normal;
            font-size: 16px;
            font-weight: 500;
            color: #201a19;

            &:hover {
              text-decoration: none !important;
              border-color: #a81e22;
            }
          }
        }
        .academyLabel {
          text-align: center;
        }
      }
      .starIcon {
        color: #f9b600;
        font-size: 16px;
      }
    }
    .scheduleModal {
      .yunoModal {
        .modal-content {
          height: 490px !important;
          border: 1px solid $grey !important;
        }

        .modal-background {
          background-color: $bgtransp !important;
        }
        .modalTitle {
          padding: 40px 20px 0 20px;
        }

        .modalBody {
          display: flex;
          flex-grow: 1;
          @media (max-width: 768px) {
            flex-direction: column;
            gap: 20px;
          }

          .classInfoWrapper {
            width: 70%;
            .classInfo {
              display: flex;
              flex-direction: column;
              gap: 6px;
              border-bottom: 1px solid $grey;
              width: 300px;

              .flexDiv {
                display: flex;
                align-items: center;
              }
              .learnerList {
                display: flex;
                align-items: center;
                padding-left: 10px;
                li {
                  margin-left: -12px;
                  img {
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                  }
                }
              }
              .learnerEnrolled {
                display: flex;
                align-items: baseline;
                gap: 6px;
              }
            }
            .courseInfo {
              display: flex;
              flex-direction: column;
              gap: 6px;

              .academyFavIcon {
                img {
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                }
              }
            }
          }
          .addLearners {
            width: 30%;

            .totalLearners {
              border-bottom: 2px solid $grey;
            }
          }
        }
        .modalFooter {
          padding-bottom: 20px;
          .ctaWrapper {
            gap: 10px;

            .primaryCTA {
              padding: 7px 10px;
              border-radius: 4px;
              border: 1px solid #a81e22;
              background-color: #a81e22;
              line-height: normal;
              font-size: 16px;
              font-weight: 500;
              color: #fff;

              &:hover {
                text-decoration: none;
                background-color: $primaryV1;
              }
            }
            .secondaryCTA {
              padding: 7px 10px;
              border-radius: 4px;
              border: 1px solid #d0c4c2;
              background-color: #fff;
              line-height: normal;
              font-size: 16px;
              font-weight: 500;
              min-width: 80px !important;
              color: #201a19;

              &:hover {
                text-decoration: none !important;
                border-color: #a81e22;
              }
            }
          }
        }
      }
    }
    .yunoSnackbar {
      .snackbar {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 15px;
        background: white;
        width: 400px;
        overflow-y: auto;
      }
      .vue-star-rating[data-v-fde73a0c] {
        justify-content: center !important;
      }
      .yunoInput {
        .textarea {
          &:not([rows]) {
            max-height: 6em !important;
            min-height: 4em !important;
          }
        }
      }
    }
  }
  .yunoSnackbar {
    .snackbar {
      .closeSnackbar {
        .material-icons-outlined {
          font-size: 18px;
        }
      }
    }
    .titleLarge {
      font-size: 24px;
      line-height: normal;
      font-weight: 400;
      margin-bottom: 5px;
    }
    .titleSmall {
      font-size: 16px;
      line-height: normal;
      font-weight: 500;
      margin-bottom: 5px;
    }
    .subtitleSmall {
      font-size: 14px;
      line-height: normal;
      font-weight: 400;
      margin-bottom: 15px;
    }
    .noticeSmall {
      margin-bottom: 10px;
    }
    .noticeTitle {
      font-size: 10px;
      line-height: normal;
      font-weight: 400;
      margin-bottom: 0;
      width: 100%;
      text-transform: uppercase;
      letter-spacing: 1.5px;
    }
    .mappedInstructor {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 15px;
      .imgWrapper {
        flex: 0 0 50px;
        margin-right: 10px;
        img {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          font-size: 0;
          background-color: $whiteBG;
        }
      }
      figcaption {
        flex: 0 0 calc(100% - 60px);
      }
      .insName {
        font-size: 16px;
        line-height: 20px;
        font-weight: 500;
        margin-bottom: 5px;
      }
      .studentCount {
        flex: 0 0 100%;
        font-size: 12px;
        line-height: 16px;
        font-weight: 400;
        margin-bottom: 0;
        margin-top: 10px;
      }
    }
    .mappedInstructor.gapBtm15 {
      margin-bottom: 15px;
    }
    .ctaWrapper {
      margin-top: 15px;
      display: flex;
      .button {
        margin-right: 10px;
      }
    }
    .formWrapper {
      width: 100%;
      position: relative;
      background: #fafafa;
      padding: 10px;
      .innerWrapper {
        background-color: $bgOffWhite ;
        padding: 10px;
        margin-bottom: 15px;
        .groupElement {
          margin: 0;
        }
      }
      .vue-star-rating {
        padding-left: 15px;
      }
      .field.noGap {
        margin: 0;
      }
      .alert {
        height: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .material-icons-outlined {
          font-size: 40px;
          margin-bottom: 10px;
        }
      }
      .ctaWrapper {
        justify-content: right;
        .button {
          margin-right: 0;
        }
      }
      .ctaWrapper.loading {
        position: absolute;
        right: 5px;
        bottom: 54px;
        .button {
          border: 0;
        }
      }
      .checkList {
        .fieldLabel {
          font-size: 14px;
          margin: 0 0 10px;
          color: $semiTransp;
          font-weight: 600;
        }
        .checkboxList {
          display: flex;
          flex-wrap: wrap;
          .field {
            margin-right: 10px;
          }
          .b-checkbox {
            border-radius: 20px;
            padding: 5px 10px;
            height: auto;
            font-size: 12px;
          }
        }
      }
    }
    .formWrapper.noBG {
      background: none;
      padding: 0;
    }
    .formWrapper.gapTop15 {
      padding-top: 15px;
    }
    .starLabel {
      display: flex;
      margin-left: 12px;
      margin-top: 8px;
      li {
        font-size: 12px;
        line-height: normal;
        font-weight: 400;
        margin-bottom: 0;
        flex: 0 0 67px;
        text-align: center;
      }
      li.active {
        visibility: visible;
      }
      li.notActive {
        visibility: hidden;
      }
    }
    .checkboxList {
      .field {
        display: inline-block !important;
        margin-bottom: 2px !important;
        margin-right: 6px !important;
      }
    }
  }
}
.b-sidebar.extraWide {
  .sidebar-content {
    width: 550px !important;
  }
}
.b-sidebar.card-slidebar {
  .sidebar-content {
    width: 390px;
  }
  .sidebar-content.is-light {
    background-color: white !important;
  }
  .card-content {
    padding-top: 70px;
    .headline5 {
      font-size: 24px;
      line-height: 28px;
      font-weight: 500;
      margin-bottom: 0;
    }
    .subtitle1 {
      font-size: 16px;
      line-height: 20px;
      font-weight: 500;
      margin-bottom: 0;

      &.noBold {
        font-weight: 400;
      }
    }
    .underline {
      text-decoration-line: underline;
      text-decoration-style: solid;
    }
    .subtitle3 {
      font-size: 12px;
      line-height: 16px;
      font-weight: 400;
      margin-bottom: 0;
    }
    .subtitle2 {
      font-size: 14px;
      line-height: 18px;
      font-weight: 500;
      margin-bottom: 0;

      &.noBold {
        font-weight: 400;
      }
    }
    .subtitle3 {
      font-size: 16px;
      line-height: 18px;
      font-weight: 500;
      margin-bottom: 0;

      &.noBold {
        font-weight: 400;
      }
    }

    .overline {
      font-size: 12px;
      line-height: 14px;
      font-weight: 500;
      margin-bottom: 0;
    }
    .cardContentWrapper {
      display: flex;
      flex-direction: column;
      gap: 7px;
      .videoWrapper {
        position: relative;
        padding-bottom: 45.7%;
        overflow: hidden;
        max-width: 100%;
        margin: 0 auto 30px;
        iframe {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }
        object {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }
        embed {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }
      }
      .videoWrapper.loading {
        padding-bottom: 0;
      }
      .classStatus {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 10px;
        font-weight: 500;
        line-height: 14px;
        letter-spacing: 1.5px;
        text-align: left;
        text-underline-position: from-font;
        -webkit-text-decoration-skip-ink: none;
        text-decoration-skip-ink: none;
        color: $pureRed;
        .dot {
          border: 1px solid;
          border-radius: 50%;
          height: 10px;
          width: 10px;
          background:$pureRed;
        }
      }
      .progress-wrapper {
        .progress {
          width: 137px;
          height: 7px;
        }
      }
      .learnerAttendence {
        display: flex;
        align-items: center;
        gap: 7px;
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        letter-spacing: 0.25px;
        .progress-wrapper {
          .progress {
            width: 137px;
            height: 7px;
          }
        }
        .bgLightPink {
          background: $primaryVariant;
          color: $brightCrimson;
        }
      }
      .classType {
        font-size: 10px;
        font-weight: 500;
        line-height: 14px;
        letter-spacing: 1.5px;
        text-align: center;
        width: 80px;
        padding: 3px;
        border-radius: 4px;
        background: #cce5ff;
      }
      .classTitle {
        font-size: 24px;
        line-height: 28px;
        font-weight: 500;
        margin-bottom: 0;
        color: $onSurface;
      }
      .classDetails {
        display: flex;
        span {
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          letter-spacing: 0.25px;
        }
      }
      .closeSidebar {
        margin-left: -8px;
        cursor: pointer;
      }
    }
    .button.yunoPrimaryCTA {
      padding: 10px 20px;
      border-radius: 4px;
      border: 1px solid #a81e22;
      background-color: $primary;
      line-height: normal;
      font-size: 16px;
      font-weight: 500;
      color: $secondaryCopyColor;
      &:hover {
        text-decoration: none;
        background-color: $primaryV1;
      }
    }
    .buttonWrapper {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 20px;
      padding-top: 10px;
      width: 150px;
      a {
        width: 100%;
        &.secondaryCTA {
          padding: 10px 20px;
          border-radius: 4px;
          border: 1px solid #d0c4c2;
          background-color: #fff;
          line-height: normal;
          font-size: 16px;
          font-weight: 500;
          color: #201a19;

          &:hover {
            text-decoration: none !important;
            border-color: #a81e22;
          }
        }
      }
    }
    .classReview {
      gap: 10px;
      img {
        border-radius: 50%;
        width: 70px;
      }
      .learnerReviewWrapper {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
    }
    .userProfile {
      display: flex;
      gap: 10px;
      align-items: center;
      img {
        width: 40px;
        border-radius: 50%;
      }
      img.userImage {
        width: 82px !important;
      }
      .userDescription {
        display: flex;
        flex-direction: column;
        gap: 3px;
      }
    }
    .learnerList {
      display: flex;
      align-items: baseline;
      li {
        margin-left: -12px;
        img {
          width: 24px;
          height: 24px;
          border-radius: 50%;
        }
      }
    }
    .dropdownWrapper {
      .select {
        select {
          option {
            color: #534342;
          }
        }
      }
    }
    .starIcon {
      color: #f9b600;
      font-size: 16px;
    }
    .instructorLearners {
      ul {
        margin: 0;
        padding: 0;
        list-style: none;

        li {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 1rem;
          border: 1px solid $grey;
          padding: 10px 10px;

          .learnerProfile {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            img {
              width: 30px;
              height: 30px;
              border-radius: 50%;
            }
          }

          .learnerAttendance {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .progress {
              width: 100px;
              height: 7px;
            }
          }
          .learnerRating {
            display: flex;
            align-items: center;
            gap: 0.3rem;
          }
        }
      }
    }
  }
  .separator {
    margin: 0 8px;
    color: $lightGray;
  }
}
.yunoSnackbar {
  .classCheckbox {
    display: inline-block;
    .button {
      padding: 0 8px !important;
      border-radius: 20px !important;
      font-size: 11px !important;
    }
  }
}
.separator {
  margin: 0 8px;
  color: $lightGray;
}
@media (max-width: 768px) {
  #app {
    .cardSection {
      .card-content {
        .buttonWrapper {
          .academyLabel {
            text-align: start;
          }
        }
      }
    }
  }
}
@media (max-width: 767px) {
  #app {
    .cardSection {
      .card-content {
        flex-direction: column !important;
        .cardContentWrapper {
          .learnerAttendence {
            flex-direction: column;
            align-items: baseline;
          }
          .classDetails {
            flex-direction: column;
            gap: 5px !important;
            align-items: baseline;
          }
        }
        .buttonWrapper {
          a {
            width: 100%;

            &.secondaryCTA {
              padding: 10px 20px;
              border-radius: 4px;
              border: 1px solid #d0c4c2;
              background-color: #fff;
              line-height: normal;
              font-size: 16px;
              font-weight: 500;
              color: #201a19;

              &:hover {
                text-decoration: none !important;
                border-color: #a81e22;
              }
            }
          }
        }
      }
    }
  }
  .b-sidebar.card-slidebar {
    .sidebar-content {
      width: 100%;
    }
    .card-content {
      .cardContentWrapper {
        .learnerAttendence {
          flex-direction: column;
          align-items: baseline;
        }
      }
      .buttonWrapper {
        a {
          width: 100%;
        }
      }
    }
  }
}
