@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
    .carouselTiles {
        padding: $gapLargest 0;
        @include setBGColor($primaryColor, 0.05);

        &.noTopGap {
            margin-top: 0;
        }

        &.noBottomGap {
            margin-bottom: 0;
        }

        @media (min-width: 768px) {
            padding: $gapLargest * 2 0;
            margin: $gapLargest * 2 0;

            &.noTopGap {
                margin-top: 0;
            }
        }

        .carouselTilesTitle {
            font-size: $fontSizeLargest;
            font-weight: 600;
            margin-bottom: $gapLargest;
            text-align: center;
        }

        .tns-outer {
            .tns-liveregion {
                display: none;
            }
            button[data-action="stop"], button[data-action="start"] {
                display: none;
            }
        }
        
        .tns-inner {
            overflow: hidden;
            margin: 0 (-$gap15);
        }

        .carouselTilesControls {
            display: none;
            justify-content: center;
            padding: $gapLargest 0 0;

            @media (min-width: 768px) {
                display: flex;
            }

            .prev, .next {
                border: 0;
                background-color: $primaryColor;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;

                .fa {
                    color: $secondaryCopyColor;
                }

                &:disabled {
                    @include setBGColor($primaryColor, 0.2);
                }
            }

            .prev {
                margin-right: $gap15;
            }
        }

        .meetInstructors {
            display: flex;

            .slide {
                padding: 0 $gap15;
            }

            .cardItem {
                background-color: $whiteBG;
                border-radius: 4px;
                box-sizing: border-box;
                padding: $gap15;

                a {
                    display: flex;
                    min-height: 170px;

                    &:hover {
                        text-decoration: none;

                        h3 {
                            color: $secondaryColor;
                        }
                    }
                }
            }

            .cardImg {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
                width: 100%;

                img {
                    border-radius: 50%;
                    overflow: hidden;
                    height: 90px;
                    width: 90px;
                    border: 2px solid #FFF;
                    box-shadow: rgba(0,0,0,.117647) 0 0 40px;
                }

                figcaption {
                    width: 100%;
                }

                .ratingWrapper {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 74%;
                    margin: 0 auto;

                    &.alignC {
                        justify-content: center;
                    }

                    .total {
                        color: $primaryColor;
                    }

                    .vue-star-rating {
                        position: relative;
                        top: -2px;
                    }

                    @media (min-width: 768px) {
                        width: 100%;
                    }
                }

                h3 {
                    font-size: $fontSizeLarger;
                    color: $primaryColor;
                    text-align: center;
                }
            }
        }
    }
}