const validationMsg = {
    "messages": {
        "required": "Mobile number is required",
        "numeric": "Numbers only",
        "min": "Minium 10 numbers required",
        "max": "Maxium 10 numbers required",
        "is": "Required",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

Sentry.init({
    dsn: "https://<EMAIL>/5690147",
    integrations: [new Sentry.Integrations.BrowserTracing()],
    release: "yuno-unbounce-page",
    tracesSampleRate: 1.0,
    tracingOptions: {
        trackComponents: true,
    },
    logErrors: true
});

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yunolp-sign-in', {
    props: ["category", "productcode", "leadstatus", "contenttype", "contentid", "ctalabel", "ctabg", "ctahasicon", "ctafontsize", "ctatextcolor", "hasiframe"],
    template: `
        <validation-observer ref="lpSignInObserver" v-slot="{handleSubmit, invalid}">
            <form id="lpSignInForm" class="lpForm" @submit.prevent="handleSubmit(initForm)">
                <validation-provider :customMessages="{ required: message.required }"  :rules="{required:true, numeric: true, min: 10, max: 10}" v-slot="{ errors, classes }">
                    <div class="field">
                        <label v-if="false" for="userMobile">Mobile</label>
                        <input inputmode="numeric" pattern="[0-9]*" id="userMobile" placeholder="Enter your mobile number" :class="classes" type="text" v-model="signIn.mobile">
                        <p class="error">{{errors[0]}}</p>
                    </div>
                    <button class="googleLogin" 
                        :class="[ctabg !== undefined && ctabg !== '' ? ctabg : '', !ctahasicon ? 'noIcon' : '', ctafontsize !== undefined && ctafontsize !== '' ? ctafontsize : '', ctatextcolor !== undefined && ctatextcolor !== '' ? ctatextcolor : '']"
                        type="submit">
                        <template v-if="ctalabel !== undefined && ctalabel !== ''">
                            {{ ctalabel }}
                        </template>
                        <template v-else>
                            Sign up with Google
                        </template>
                    </button>
                </validation-provider>
            </form>    
        </validation-observer>
    `,
    data() {
        return {
            message: {
                required: "Mobile number is required"
            },
            signIn: {
                mobile: "",
                categoryURL: "",
                productCode: "",
                leadStatus: "",
                variant: "",
                utmSource: "",
                utmCampaign: "",
                utmMedium: "",
                adGroupID: "",
                adContent: "",
                utmTerm: "",
                gclid: "",
                content: {
                    type: "",
                    id: ""
                }
            }
        }
    },
    computed: {
        
    },
    mounted() {
        
    },
    methods: {
        setPayload() {
            let payload = this.signIn,
                getPageTitle = (document.title).replace(/\s+/g, '-').toLowerCase();

            payload.categoryURL = `/${this.$props.category}`;
            payload.productCode = this.$props.productcode;
            payload.leadStatus = this.$props.leadstatus;
            payload.variant = `${window.ub.page.variantId}-${getPageTitle}`;
            payload.utmSource = YUNOCommon.getQueryParameter("utm_source");
            payload.utmCampaign = YUNOCommon.getQueryParameter("utm_campaign");
            payload.utmMedium = YUNOCommon.getQueryParameter("utm_medium");
            payload.adGroupID = YUNOCommon.getQueryParameter("adgroupid");
            payload.adContent = YUNOCommon.getQueryParameter("ad_content");
            payload.utmTerm = YUNOCommon.getQueryParameter("utm_term");
            payload.gclid = YUNOCommon.getQueryParameter("gclid");
            payload.content.type = this.$props.contenttype !== undefined ? this.$props.contenttype : "";
            payload.content.id = this.$props.contentid !== undefined ? this.$props.contentid : "";
        },
        signInURL(state, environment) {
            // let url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id=************-pdgbpet2n7tebqbodvtuot016ccj97bq.apps.googleusercontent.com&redirect_uri=https://www.yunolearning.com/auth/&state="+ state +"&scope=email%20profile&approval_prompt=auto&include_granted_scopes=true"
            
            let url = "";

            switch (environment) {
                case "dev":
                    url = "https://accounts.google.com/o/oauth2/auth/oauthchooseaccount?response_type=code&access_type=offline&client_id=***********-cquruv3ca8e2upq7bflldlpl8rq5cl5d.apps.googleusercontent.com&redirect_uri=https%3A%2F%2Fcharlesdickens.wpengine.com%2Fauth&state="+ state +"&scope=email%20profile&approval_prompt=force&flowName=GeneralOAuthFlow"
                    break;
            
                case "stage":
                    url = "https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id=***********-cquruv3ca8e2upq7bflldlpl8rq5cl5d.apps.googleusercontent.com&redirect_uri=https://yunoenglish.wpengine.com/auth&state="+ state +"&scope=email%20profile&approval_prompt=force&flowName=GeneralOAuthFlow";
                    break;
                
                case "prod":
                    url = "https://accounts.google.com/o/oauth2/auth/oauthchooseaccount?response_type=code&access_type=offline&client_id=************-pdgbpet2n7tebqbodvtuot016ccj97bq.apps.googleusercontent.com&redirect_uri=https%3A%2F%2Fwww.yunolearning.com%2Fielts%2Fsignup&state="+ state +"&scope=email%20profile&approval_prompt=force&flowName=GeneralOAuthFlow";
                    break;
            }

            return url;
        },
        initForm() {
            this.setPayload();
            const toStringify = JSON.stringify(this.signIn);

            setTimeout(() => { 
                if (this.$props.hasiframe) {
                    window.parent.location.href = this.signInURL(encodeURI(toStringify), "stage");
                } else {
                    window.location.href = this.signInURL(encodeURI(toStringify), "stage"); 
                };
                
            }, 50); 
        }
    }
});

const app = new Vue({
    el: '#yunoLPSignIn',
    data() {
        return {
            
        }
    },
    async created() {
        
    },
    computed: {
        
    },
    mounted() {
        
    },
    methods: {
        
    }
});