const YUNOChooseOptions = (function($) {
    
    const chooseOptions = function() {
        Vue.component('yuno-choose-options', {
            props: ["options"],
            template: `
                <section class="chooseOptions">
                    <div class="container">
                        <div class="row">
                            <div class="col-md-8 offset-md-2">
                                <h1 class="sectionTitle">{{options.title}}</h1>
                                <p class="sectionSubtitle">{{options.description}}</p>
                            </div>
                        </div>
                        <div class="row optionsWrapper">
                            <div 
                                v-for="(options, index) in options.list"
                                :key="index"
                                class="col-12 col-md-3">
                                <figure class="optionCard">
                                    <a :href="options.url">
                                        <img class="optionImg" :src="options.imgURL" :alt="options.title">
                                        <figcaption>
                                            <h2 class="optionCaption">{{options.title}}</h2>
                                        </figcaption>
                                    </a>
                                </figure>
                            </div>
                        </div>
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                
            },
            created() {
                
            },
            mounted() {
                
            },
            methods: {
                structuredData(structuredDataObj) {
                    const script = document.createElement('script');
                    
                    script.setAttribute('type', 'application/ld+json');

                    let inlineScript = document.createTextNode(JSON.stringify(structuredDataObj));

                    script.appendChild(inlineScript); 
                    document.head.appendChild(script);
                },
                richSnippet() {
                    const data = this.$props.options;
                }
            }
        });
    };

    return {
        chooseOptions: chooseOptions
    };
})(jQuery);

