/*
 vuex v3.5.1
 (c) 2020 Evan You
 @license MIT
*/
(function(z,A){"object"===typeof exports&&"undefined"!==typeof module?module.exports=A():"function"===typeof define&&define.amd?define(A):(z=z||self,z.Vuex=A())})(this,function(){function z(a){function b(){var d=this.$options;d.store?this.$store="function"===typeof d.store?d.store():d.store:d.parent&&d.parent.$store&&(this.$store=d.parent.$store)}if(2<=Number(a.version.split(".")[0]))a.mixin({beforeCreate:b});else{var c=a.prototype._init;a.prototype._init=function(d){void 0===d&&(d={});d.init=d.init?
[b].concat(d.init):b;c.call(this,d)}}}function A(a){y&&(a._devtoolHook=y,y.emit("vuex:init",a),y.on("vuex:travel-to-state",function(b){a.replaceState(b)}),a.subscribe(function(b,c){y.emit("vuex:mutation",b,c)},{prepend:!0}),a.subscribeAction(function(b,c){y.emit("vuex:action",b,c)},{prepend:!0}))}function ca(a,b){return a.filter(b)[0]}function J(a,b){void 0===b&&(b=[]);if(null===a||"object"!==typeof a)return a;var c=ca(b,function(e){return e.original===a});if(c)return c.copy;var d=Array.isArray(a)?
[]:{};b.push({original:a,copy:d});Object.keys(a).forEach(function(e){d[e]=J(a[e],b)});return d}function w(a,b){Object.keys(a).forEach(function(c){return b(a[c],c)})}function t(a,b){if(!a)throw Error("[vuex] "+b);}function da(a,b){return function(){return a(b)}}function P(a,b,c){Q(a,c);b.update(c);if(c.modules)for(var d in c.modules){if(!b.getChild(d)){console.warn("[vuex] trying to add a new module '"+d+"' on hot reloading, manual reload is needed");break}P(a.concat(d),b.getChild(d),c.modules[d])}}
function Q(a,b){Object.keys(R).forEach(function(c){if(b[c]){var d=R[c];w(b[c],function(e,g){var h=d.assert(e),f=c+" should be "+d.expected+' but "'+c+"."+g+'"';0<a.length&&(f+=' in module "'+a.join(".")+'"');f+=" is "+JSON.stringify(e)+".";t(h,f)})}})}function S(a,b,c){0>b.indexOf(a)&&(c&&c.prepend?b.unshift(a):b.push(a));return function(){var d=b.indexOf(a);-1<d&&b.splice(d,1)}}function T(a,b){a._actions=Object.create(null);a._mutations=Object.create(null);a._wrappedGetters=Object.create(null);a._modulesNamespaceMap=
Object.create(null);var c=a.state;D(a,c,[],a._modules.root,!0);K(a,c,b)}function K(a,b,c){var d=a._vm;a.getters={};a._makeLocalGettersCache=Object.create(null);var e={};w(a._wrappedGetters,function(h,f){e[f]=da(h,a);Object.defineProperty(a.getters,f,{get:function(){return a._vm[f]},enumerable:!0})});var g=r.config.silent;r.config.silent=!0;a._vm=new r({data:{$$state:b},computed:e});r.config.silent=g;a.strict&&ea(a);d&&(c&&a._withCommit(function(){d._data.$$state=null}),r.nextTick(function(){return d.$destroy()}))}
function D(a,b,c,d,e){var g=!c.length,h=a._modules.getNamespace(c);d.namespaced&&(a._modulesNamespaceMap[h]&&console.error("[vuex] duplicate namespace "+h+" for the namespaced module "+c.join("/")),a._modulesNamespaceMap[h]=d);if(!g&&!e){var f=L(b,c.slice(0,-1)),k=c[c.length-1];a._withCommit(function(){k in f&&console.warn('[vuex] state field "'+k+'" was overridden by a module with the same name at "'+c.join(".")+'"');r.set(f,k,d.state)})}var n=d.context=fa(a,h,c);d.forEachMutation(function(l,m){ha(a,
h+m,l,n)});d.forEachAction(function(l,m){ia(a,l.root?m:h+m,l.handler||l,n)});d.forEachGetter(function(l,m){ja(a,h+m,l,n)});d.forEachChild(function(l,m){D(a,b,c.concat(m),l,e)})}function fa(a,b,c){var d=""===b,e={dispatch:d?a.dispatch:function(g,h,f){g=E(g,h,f);h=g.payload;f=g.options;var k=g.type;if(!f||!f.root)if(k=b+k,!a._actions[k]){console.error("[vuex] unknown local action type: "+g.type+", global type: "+k);return}return a.dispatch(k,h)},commit:d?a.commit:function(g,h,f){g=E(g,h,f);h=g.payload;
f=g.options;var k=g.type;if(!f||!f.root)if(k=b+k,!a._mutations[k]){console.error("[vuex] unknown local mutation type: "+g.type+", global type: "+k);return}a.commit(k,h,f)}};Object.defineProperties(e,{getters:{get:d?function(){return a.getters}:function(){return ka(a,b)}},state:{get:function(){return L(a.state,c)}}});return e}function ka(a,b){if(!a._makeLocalGettersCache[b]){var c={},d=b.length;Object.keys(a.getters).forEach(function(e){if(e.slice(0,d)===b){var g=e.slice(d);Object.defineProperty(c,
g,{get:function(){return a.getters[e]},enumerable:!0})}});a._makeLocalGettersCache[b]=c}return a._makeLocalGettersCache[b]}function ha(a,b,c,d){(a._mutations[b]||(a._mutations[b]=[])).push(function(e){c.call(a,d.state,e)})}function ia(a,b,c,d){(a._actions[b]||(a._actions[b]=[])).push(function(e){(e=c.call(a,{dispatch:d.dispatch,commit:d.commit,getters:d.getters,state:d.state,rootGetters:a.getters,rootState:a.state},e))&&"function"===typeof e.then||(e=Promise.resolve(e));return a._devtoolHook?e["catch"](function(g){a._devtoolHook.emit("vuex:error",
g);throw g;}):e})}function ja(a,b,c,d){a._wrappedGetters[b]?console.error("[vuex] duplicate getter key: "+b):a._wrappedGetters[b]=function(e){return c(d.state,d.getters,e.state,e.getters)}}function ea(a){a._vm.$watch(function(){return this._data.$$state},function(){t(a._committing,"do not mutate vuex store state outside mutation handlers.")},{deep:!0,sync:!0})}function L(a,b){return b.reduce(function(c,d){return c[d]},a)}function E(a,b,c){null!==a&&"object"===typeof a&&a.type&&(c=b,b=a,a=a.type);
t("string"===typeof a,"expects string as the type, but found "+typeof a+".");return{type:a,payload:b,options:c}}function U(a){r&&a===r?console.error("[vuex] already installed. Vue.use(Vuex) should be called only once."):(r=a,z(r))}function F(a){return B(a)?Array.isArray(a)?a.map(function(b){return{key:b,val:b}}):Object.keys(a).map(function(b){return{key:b,val:a[b]}}):[]}function B(a){return Array.isArray(a)||null!==a&&"object"===typeof a}function G(a){return function(b,c){"string"!==typeof b?(c=b,
b=""):"/"!==b.charAt(b.length-1)&&(b+="/");return a(b,c)}}function H(a,b,c){(a=a._modulesNamespaceMap[c])||console.error("[vuex] module namespace not found in "+b+"(): "+c);return a}function V(a,b,c){c=c?a.groupCollapsed:a.group;try{c.call(a,b)}catch(d){a.log(b)}}function W(a){try{a.groupEnd()}catch(b){a.log("\u2014\u2014 log end \u2014\u2014")}}function X(){var a=new Date;return" @ "+I(a.getHours(),2)+":"+I(a.getMinutes(),2)+":"+I(a.getSeconds(),2)+"."+I(a.getMilliseconds(),3)}function I(a,b){return Array(b-
a.toString().length+1).join("0")+a}var y=("undefined"!==typeof window?window:"undefined"!==typeof global?global:{}).__VUE_DEVTOOLS_GLOBAL_HOOK__,u=function(a,b){this.runtime=b;this._children=Object.create(null);this._rawModule=a;var c=a.state;this.state=("function"===typeof c?c():c)||{}},p={namespaced:{configurable:!0}};p.namespaced.get=function(){return!!this._rawModule.namespaced};u.prototype.addChild=function(a,b){this._children[a]=b};u.prototype.removeChild=function(a){delete this._children[a]};
u.prototype.getChild=function(a){return this._children[a]};u.prototype.hasChild=function(a){return a in this._children};u.prototype.update=function(a){this._rawModule.namespaced=a.namespaced;a.actions&&(this._rawModule.actions=a.actions);a.mutations&&(this._rawModule.mutations=a.mutations);a.getters&&(this._rawModule.getters=a.getters)};u.prototype.forEachChild=function(a){w(this._children,a)};u.prototype.forEachGetter=function(a){this._rawModule.getters&&w(this._rawModule.getters,a)};u.prototype.forEachAction=
function(a){this._rawModule.actions&&w(this._rawModule.actions,a)};u.prototype.forEachMutation=function(a){this._rawModule.mutations&&w(this._rawModule.mutations,a)};Object.defineProperties(u.prototype,p);var x=function(a){this.register([],a,!1)};x.prototype.get=function(a){return a.reduce(function(b,c){return b.getChild(c)},this.root)};x.prototype.getNamespace=function(a){var b=this.root;return a.reduce(function(c,d){b=b.getChild(d);return c+(b.namespaced?d+"/":"")},"")};x.prototype.update=function(a){P([],
this.root,a)};x.prototype.register=function(a,b,c){var d=this;void 0===c&&(c=!0);Q(a,b);var e=new u(b,c);0===a.length?this.root=e:this.get(a.slice(0,-1)).addChild(a[a.length-1],e);b.modules&&w(b.modules,function(g,h){d.register(a.concat(h),g,c)})};x.prototype.unregister=function(a){var b=this.get(a.slice(0,-1));a=a[a.length-1];var c=b.getChild(a);c?c.runtime&&b.removeChild(a):console.warn("[vuex] trying to unregister module '"+a+"', which is not registered")};x.prototype.isRegistered=function(a){return this.get(a.slice(0,
-1)).hasChild(a[a.length-1])};p={assert:function(a){return"function"===typeof a},expected:"function"};var R={getters:p,mutations:p,actions:{assert:function(a){return"function"===typeof a||"object"===typeof a&&"function"===typeof a.handler},expected:'function or object with "handler" function'}},r;p=function c(b){var d=this;void 0===b&&(b={});!r&&"undefined"!==typeof window&&window.Vue&&U(window.Vue);t(r,"must call Vue.use(Vuex) before creating a store instance.");t("undefined"!==typeof Promise,"vuex requires a Promise polyfill in this browser.");
t(this instanceof c,"store must be called with the new operator.");var e=b.plugins;void 0===e&&(e=[]);var g=b.strict;void 0===g&&(g=!1);this._committing=!1;this._actions=Object.create(null);this._actionSubscribers=[];this._mutations=Object.create(null);this._wrappedGetters=Object.create(null);this._modules=new x(b);this._modulesNamespaceMap=Object.create(null);this._subscribers=[];this._watcherVM=new r;this._makeLocalGettersCache=Object.create(null);var h=this,f=this.dispatch,k=this.commit;this.dispatch=
function(n,l){return f.call(h,n,l)};this.commit=function(n,l,m){return k.call(h,n,l,m)};this.strict=g;g=this._modules.root.state;D(this,g,[],this._modules.root);K(this,g);e.forEach(function(n){return n(d)});(void 0!==b.devtools?b.devtools:r.config.devtools)&&A(this)};var M={state:{configurable:!0}};M.state.get=function(){return this._vm._data.$$state};M.state.set=function(b){t(!1,"use store.replaceState() to explicit replace store state.")};p.prototype.commit=function(b,c,d){var e=this;c=E(b,c,d);
b=c.type;var g=c.payload;c=c.options;var h={type:b,payload:g},f=this._mutations[b];f?(this._withCommit(function(){f.forEach(function(k){k(g)})}),this._subscribers.slice().forEach(function(k){return k(h,e.state)}),c&&c.silent&&console.warn("[vuex] mutation type: "+b+". Silent option has been removed. Use the filter functionality in the vue-devtools")):console.error("[vuex] unknown mutation type: "+b)};p.prototype.dispatch=function(b,c){var d=this,e=E(b,c),g=e.type,h=e.payload,f={type:g,payload:h};
if(e=this._actions[g]){try{this._actionSubscribers.slice().filter(function(n){return n.before}).forEach(function(n){return n.before(f,d.state)})}catch(n){console.warn("[vuex] error in before action subscribers: "),console.error(n)}var k=1<e.length?Promise.all(e.map(function(n){return n(h)})):e[0](h);return new Promise(function(n,l){k.then(function(m){try{d._actionSubscribers.filter(function(q){return q.after}).forEach(function(q){return q.after(f,d.state)})}catch(q){console.warn("[vuex] error in after action subscribers: "),
console.error(q)}n(m)},function(m){try{d._actionSubscribers.filter(function(q){return q.error}).forEach(function(q){return q.error(f,d.state,m)})}catch(q){console.warn("[vuex] error in error action subscribers: "),console.error(q)}l(m)})})}console.error("[vuex] unknown action type: "+g)};p.prototype.subscribe=function(b,c){return S(b,this._subscribers,c)};p.prototype.subscribeAction=function(b,c){return S("function"===typeof b?{before:b}:b,this._actionSubscribers,c)};p.prototype.watch=function(b,
c,d){var e=this;t("function"===typeof b,"store.watch only accepts a function.");return this._watcherVM.$watch(function(){return b(e.state,e.getters)},c,d)};p.prototype.replaceState=function(b){var c=this;this._withCommit(function(){c._vm._data.$$state=b})};p.prototype.registerModule=function(b,c,d){void 0===d&&(d={});"string"===typeof b&&(b=[b]);t(Array.isArray(b),"module path must be a string or an Array.");t(0<b.length,"cannot register the root module by using registerModule.");this._modules.register(b,
c);D(this,this.state,b,this._modules.get(b),d.preserveState);K(this,this.state)};p.prototype.unregisterModule=function(b){var c=this;"string"===typeof b&&(b=[b]);t(Array.isArray(b),"module path must be a string or an Array.");this._modules.unregister(b);this._withCommit(function(){var d=L(c.state,b.slice(0,-1));r["delete"](d,b[b.length-1])});T(this)};p.prototype.hasModule=function(b){"string"===typeof b&&(b=[b]);t(Array.isArray(b),"module path must be a string or an Array.");return this._modules.isRegistered(b)};
p.prototype.hotUpdate=function(b){this._modules.update(b);T(this,!0)};p.prototype._withCommit=function(b){var c=this._committing;this._committing=!0;b();this._committing=c};Object.defineProperties(p.prototype,M);var Y=G(function(b,c){var d={};B(c)||console.error("[vuex] mapState: mapper parameter must be either an Array or an Object");F(c).forEach(function(e){var g=e.key,h=e.val;d[g]=function(){var f=this.$store.state,k=this.$store.getters;if(b){k=H(this.$store,"mapState",b);if(!k)return;f=k.context.state;
k=k.context.getters}return"function"===typeof h?h.call(this,f,k):f[h]};d[g].vuex=!0});return d}),Z=G(function(b,c){var d={};B(c)||console.error("[vuex] mapMutations: mapper parameter must be either an Array or an Object");F(c).forEach(function(e){var g=e.val;d[e.key]=function(){for(var h=[],f=arguments.length;f--;)h[f]=arguments[f];f=this.$store.commit;if(b){f=H(this.$store,"mapMutations",b);if(!f)return;f=f.context.commit}return"function"===typeof g?g.apply(this,[f].concat(h)):f.apply(this.$store,
[g].concat(h))}});return d}),aa=G(function(b,c){var d={};B(c)||console.error("[vuex] mapGetters: mapper parameter must be either an Array or an Object");F(c).forEach(function(e){var g=e.key,h=e.val;h=b+h;d[g]=function(){if(!b||H(this.$store,"mapGetters",b)){if(h in this.$store.getters)return this.$store.getters[h];console.error("[vuex] unknown getter: "+h)}};d[g].vuex=!0});return d}),ba=G(function(b,c){var d={};B(c)||console.error("[vuex] mapActions: mapper parameter must be either an Array or an Object");
F(c).forEach(function(e){var g=e.val;d[e.key]=function(){for(var h=[],f=arguments.length;f--;)h[f]=arguments[f];f=this.$store.dispatch;if(b){f=H(this.$store,"mapActions",b);if(!f)return;f=f.context.dispatch}return"function"===typeof g?g.apply(this,[f].concat(h)):f.apply(this.$store,[g].concat(h))}});return d});return{Store:p,install:U,version:"3.5.1",mapState:Y,mapMutations:Z,mapGetters:aa,mapActions:ba,createNamespacedHelpers:function(b){return{mapState:Y.bind(null,b),mapGetters:aa.bind(null,b),
mapMutations:Z.bind(null,b),mapActions:ba.bind(null,b)}},createLogger:function(b){void 0===b&&(b={});var c=b.collapsed;void 0===c&&(c=!0);var d=b.filter;void 0===d&&(d=function(m,q,v){return!0});var e=b.transformer;void 0===e&&(e=function(m){return m});var g=b.mutationTransformer;void 0===g&&(g=function(m){return m});var h=b.actionFilter;void 0===h&&(h=function(m,q){return!0});var f=b.actionTransformer;void 0===f&&(f=function(m){return m});var k=b.logMutations;void 0===k&&(k=!0);var n=b.logActions;
void 0===n&&(n=!0);var l=b.logger;void 0===l&&(l=console);return function(m){var q=J(m.state);"undefined"!==typeof l&&(k&&m.subscribe(function(v,N){var C=J(N);if(d(v,q,C)){var O=X(),la=g(v);V(l,"mutation "+v.type+O,c);l.log("%c prev state","color: #9E9E9E; font-weight: bold",e(q));l.log("%c mutation","color: #03A9F4; font-weight: bold",la);l.log("%c next state","color: #4CAF50; font-weight: bold",e(C));W(l)}q=C}),n&&m.subscribeAction(function(v,N){if(h(v,N)){var C=X(),O=f(v);V(l,"action "+v.type+
C,c);l.log("%c action","color: #03A9F4; font-weight: bold",O);W(l)}}))}}}});