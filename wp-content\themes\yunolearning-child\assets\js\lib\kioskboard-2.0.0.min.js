/* KioskBoard - Virtual Keyboard (https://github.com/furcan/KioskBoard) - Version: 2.0.0 - Author: <PERSON><PERSON><PERSON> MT (https://github.com/furcan) - Copyright 2021 KioskBoard - Virtual Keyboard, MIT Licence (https://opensource.org/licenses/MIT) */

(function(a,b){"function"==typeof define&&define.amd?define([],function(){return b(a)}):"object"==typeof module&&"object"==typeof module.exports?module.exports=b(a):a.KioskBoard=b(a)})("undefined"==typeof global?"undefined"==typeof window?this:window:global,function(a){'use strict';if("undefined"!=typeof a||"undefined"!=typeof a.document){var b,c,d=function(){return null},e=function(){if(null!==d()&&!a.document.getElementById("KioskBoardInternalCSS")){var b=a.document.createElement("style");b.id="KioskBoardInternalCSS",b.innerHTML=d(),a.document.head.appendChild(b)}},f={keysArrayOfObjects:null,keysJsonUrl:null,keysSpecialCharsArrayOfStrings:null,keysNumpadArrayOfNumbers:null,language:"en",theme:"light",capsLockActive:!0,allowRealKeyboard:!1,allowMobileKeyboard:!1,cssAnimations:!0,cssAnimationsDuration:360,cssAnimationsStyle:"slide",keysAllowSpacebar:!0,keysSpacebarText:"Space",keysFontFamily:"sans-serif",keysFontSize:"22px",keysFontWeight:"normal",keysIconSize:"25px",autoScroll:!0},g="https://github.com/furcan/KioskBoard",h={0:"!",1:"'",2:"^",3:"#",4:"+",5:"$",6:"%",7:"\xBD",8:"&",9:"/",10:"{",11:"}",12:"(",13:")",14:"[",15:"]",16:"=",17:"*",18:"?",19:"\\",20:"-",21:"_",22:"|",23:"@",24:"\u20AC",25:"\u20BA",26:"~",27:"\xE6",28:"\xDF",29:"<",30:">",31:",",32:";",33:".",34:":",35:"`"},i={0:"7",1:"8",2:"9",3:"4",4:"5",5:"6",6:"1",7:"2",8:"3",9:"0"},j={0:"1",1:"2",2:"3",3:"4",4:"5",5:"6",6:"7",7:"8",8:"9",9:"0"},k={All:"all",Keyboard:"keyboard",Numpad:"numpad"},l=function(){var a={},b=!1,c=0;"[object Boolean]"===Object.prototype.toString.call(arguments[0])&&(b=arguments[0],c++);for(var d=function(c){for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=b&&"[object Object]"===Object.prototype.toString.call(c[d])?l(a[d],c[d]):c[d])};c<arguments.length;c++)d(arguments[c]);return a},m=function(a){if(Array.isArray(a)&&0<a.length){var b=a[0];if("object"==typeof b&&!Array.isArray(b))for(var c in b)if(Object.prototype.hasOwnProperty.call(b,c))return!0}return!1},n=function(a){return console.error("%c KioskBoard (Error) ","padding:2px;border-radius:20px;color:#fff;background:#f44336","\n"+a)},o=function(a){return console.log("%c KioskBoard (Info) ","padding:2px;border-radius:20px;color:#fff;background:#00bcd4","\n"+a)},p=function(a,b){a||(a=25),b||(b="#707070");var c="&nbsp;<svg id=\"KioskBoardIconBackspace\" xmlns=\"http://www.w3.org/2000/svg\" width=\""+a+"\" height=\""+a+"\" viewBox=\"0 0 612 612\" style=\"width:"+a+";height:"+a+";fill:"+b+";\"><path d=\"M561,76.5H178.5c-17.85,0-30.6,7.65-40.8,22.95L0,306l137.7,206.55c10.2,12.75,22.95,22.95,40.8,22.95H561c28.05,0,51-22.95,51-51v-357C612,99.45,589.05,76.5,561,76.5z M484.5,397.8l-35.7,35.7L357,341.7l-91.8,91.8l-35.7-35.7l91.8-91.8l-91.8-91.8l35.7-35.7l91.8,91.8l91.8-91.8l35.7,35.7L392.7,306L484.5,397.8z\"/></svg>";return c},q=function(a,b){a||(a=25),b||(b="#707070");var c="&nbsp;<svg id=\"KioskBoardIconCapslock\" xmlns=\"http://www.w3.org/2000/svg\" width=\""+a+"\" height=\""+a+"\" style=\"width:"+a+";height:"+a+";fill:"+b+";shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd\" viewBox=\"0 0 200 200\"><path d=\"M61.8 148.97l76.4 0c6,0 10.91,4.9 10.91,10.9l0 27.24c0,5.99 -4.91,10.89 -10.91,10.89l-76.4 0c-6,0 -10.91,-4.9 -10.91,-10.89l0 -27.24c0,-6 4.91,-10.9 10.91,-10.9zm105.7 -60.38l-18.39 0 0 37.36c0,5.99 -4.91,10.89 -10.91,10.89l-76.4 0c-6,0 -10.91,-4.9 -10.91,-10.89l0 -37.36 -18.39 0c-2.65,0 -4.91,-1.47 -5.97,-3.89 -1.07,-2.42 -0.63,-5.08 1.16,-7.02l67.5 -73.57c1.28,-1.39 2.91,-2.11 4.81,-2.11 1.9,0 3.53,0.72 4.81,2.11l67.5 73.57c1.79,1.94 2.23,4.6 1.16,7.02 -1.06,2.42 -3.32,3.89 -5.97,3.89z\"/></svg>";return c},r=function(a,b,c){a||(a=50),b||(a=25),c||(c="#707070");var d="&nbsp;<svg id=\"KioskBoardIconSpecialCharacters\" xmlns=\"http://www.w3.org/2000/svg\" width=\""+a+"\" height=\""+b+"\" style=\"width:"+a+";height:"+b+";fill:"+c+";shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd\" viewBox=\"0 0 300 150\"><path d=\"M34.19 79.43l1.99 -10.86 10.8 0 -1.96 10.86 -10.83 0zm264.98 -17.22l0 -9.63c0,-1.23 -1,-2.23 -2.24,-2.23l-74.48 0c-1.24,0 -2.24,1 -2.24,2.23l0 9.63c0,1.23 1,2.23 2.24,2.23l74.48 0c1.24,0 2.24,-1 2.24,-2.23zm0 35.22l0 -9.62c0,-1.23 -1,-2.23 -2.24,-2.23l-74.48 0c-1.24,0 -2.24,1 -2.24,2.23l0 9.62c0,1.23 1,2.24 2.24,2.24l74.48 0c1.24,0 2.24,-1.01 2.24,-2.24zm-153.98 -61.91l9.63 0c1.23,0 2.23,1.01 2.23,2.25l0 30.19 30.19 0c1.25,0 2.25,1.01 2.25,2.23l0 9.63c0,1.23 -1,2.23 -2.25,2.23l-30.19 0 0 30.19c0,1.25 -1,2.25 -2.23,2.25l-9.63 0c-1.23,0 -2.23,-1 -2.23,-2.25l0 -30.19 -30.19 0c-1.24,0 -2.25,-1 -2.25,-2.23l0 -9.63c0,-1.22 1.01,-2.23 2.25,-2.23l30.19 0 0 -30.19c0,-1.24 1,-2.25 2.23,-2.25zm-67.7 33.05c1.28,0 2.31,-1.03 2.31,-2.31l0 -9.2c0,-1.27 -1.03,-2.31 -2.31,-2.31l-13.93 0 2.95 -16.51c0.12,-0.68 -0.07,-1.37 -0.51,-1.89 -0.44,-0.53 -1.09,-0.83 -1.77,-0.83l-9.36 -0.01c0,0 0,0 0,0 -1.12,0 -2.08,0.8 -2.28,1.9l-3.12 17.34 -10.74 0 3.03 -16.49c0.12,-0.67 -0.06,-1.37 -0.5,-1.89 -0.44,-0.53 -1.09,-0.84 -1.77,-0.84l-9.48 -0.01c0,0 0,0 0,0 -1.12,0 -2.08,0.8 -2.28,1.9l-3.16 17.33 -21.43 0c-1.28,0 -2.31,1.04 -2.31,2.32l0 9.19c0,1.28 1.03,2.31 2.31,2.31l18.91 0 -1.98 10.86 -16.93 0c-1.28,0 -2.31,1.04 -2.31,2.31l0 9.2c0,1.28 1.03,2.31 2.31,2.31l14.41 0 -3.35 18.36c-0.12,0.67 0.06,1.37 0.5,1.89 0.44,0.53 1.09,0.84 1.78,0.84l9.36 0c1.12,0 2.08,-0.8 2.28,-1.9l3.53 -19.19 10.88 0 -3.31 18.42c-0.13,0.67 0.06,1.36 0.49,1.89 0.44,0.52 1.08,0.83 1.76,0.84l9.49 0.09c0,0 0.01,0 0.02,0 1.12,0 2.08,-0.81 2.28,-1.91l3.44 -19.33 20.79 0c1.28,0 2.31,-1.03 2.31,-2.31l0 -9.2c0,-1.27 -1.03,-2.31 -2.31,-2.31l-18.32 0 1.93 -10.86 16.39 0z\"/></svg>";return d},s=function(a,b){a||(a=18),b||(b="#707070");var c="<svg id=\"KioskBoardIconClose\" width=\""+a+"\" height=\""+a+"\" style=\"width:"+a+";height:"+a+";fill:"+b+";\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 348.333 348.334\"><path d=\"M336.559,68.611L231.016,174.165l105.543,105.549c15.699,15.705,15.699,41.145,0,56.85c-7.844,7.844-18.128,11.769-28.407,11.769c-10.296,0-20.581-3.919-28.419-11.769L174.167,231.003L68.609,336.563c-7.843,7.844-18.128,11.769-28.416,11.769c-10.285,0-20.563-3.919-28.413-11.769c-15.699-15.698-15.699-41.139,0-56.85l105.54-105.549L11.774,68.611c-15.699-15.699-15.699-41.145,0-56.844c15.696-15.687,41.127-15.687,56.829,0l105.563,105.554\tL279.721,11.767c15.705-15.687,41.139-15.687,56.832,0C352.258,27.466,352.258,52.912,336.559,68.611z\"/></svg>";return c};(function(){function b(b,c){c=c||{bubbles:!1,cancelable:!1,detail:void 0};var d=a.document.createEvent("CustomEvent");return d.initCustomEvent(b,c.bubbles,c.cancelable,c.detail),d}return"function"!=typeof a.Event&&void(b.prototype=a.Event.prototype,a.Event=b)})();var t=function(a,b){if(a.target===b)return!0;var c=b.querySelectorAll("*");if(c&&0<c.length)for(var d,e=0;e<c.length;e++)if(d=c[e],a.target===d)return!0;return!1},u={init:function(a){a="object"!=typeof a||Array.isArray(a)?{}:a,c=l(!0,f,a),e()},run:function(d,e){var v=[],w=["input","textarea"],x=-1<w.indexOf((d.nodeName||"").toLocaleLowerCase("en"));if(x)v.push(d);else{var y="string"==typeof d&&0<d.length;if(!y)return n("\""+d+"\" is not a valid selector."),!1;if(v=a.document.querySelectorAll(d),1>v.length)return n("You called the KioskBoard with the \""+d+"\" selector, but there is no such element on the document."),!1}if("object"==typeof e&&0<Object.keys(e).length)c?c=l(!0,f,e):u.init(e);else if(!c)return n("You have to initialize the KioskBoard first. \n\nVisit to learn more: "+g),!1;var z=c,A=z.keysArrayOfObjects,B=!1;if(m(A)&&(B=!0,b=A),!B){var C="string"==typeof z.keysJsonUrl&&0<z.keysJsonUrl.length;if(!C)return n("You have to set the path of KioskBoard Keys JSON file to \"keysJsonUrl\" option. \n\nVisit to learn more: "+g),!1}for(var D=function(b,c){var d=[c],f=null!==c.getAttribute("readonly"),g=!0===z.allowMobileKeyboard,l=function(c){var f=c.currentTarget,l=0,m=[],n=[k.All,k.Keyboard,k.Numpad],o=(f.dataset.kioskboardType||"").toLocaleLowerCase("en"),u=-1<n.indexOf(o)?o:k.All,v="true"===(f.dataset.kioskboardSpecialcharacters||"").toLocaleLowerCase("en"),w="string"==typeof z.language&&0<z.language.length?z.language.toLocaleLowerCase("en"):"en";g||(f.setAttribute("readonly","readonly"),f.blur());var x=(f.value||"").length;l=f.selectionStart||x,m=f.value.split("");var y="",A="string"==typeof z.keysFontFamily&&0<z.keysFontFamily.length?z.keysFontFamily:"sans-serif",B="string"==typeof z.keysFontSize&&0<z.keysFontSize.length?z.keysFontSize:"22px",C="string"==typeof z.keysFontWeight&&0<z.keysFontWeight.length?z.keysFontWeight:"normal",D=!0===z.capsLockActive,E="string"==typeof z.keysIconSize&&0<z.keysIconSize.length?z.keysIconSize:"25px",F=!0===z.keysAllowSpacebar,G=F?" ":"",H="string"==typeof z.keysSpacebarText&&0<z.keysSpacebarText.length?z.keysSpacebarText:"Space",I="<span style=\"font-family:"+A+",sans-serif;font-weight:"+C+";font-size:"+B+";\" class=\"kioskboard-key kioskboard-key-space "+(F?"spacebar-allowed":"spacebar-denied")+"\" data-value=\""+G+"\">"+H+"</span>",J="<span style=\"font-family:"+A+",sans-serif;font-weight:"+C+";font-size:"+B+";\" class=\"kioskboard-key-capslock "+(D?"capslock-active":"")+"\">"+q(E,"#707070")+"</span>",K="<span style=\"font-family:"+A+",sans-serif;font-weight:"+C+";font-size:"+B+";\" class=\"kioskboard-key-backspace\">"+p(E,"#707070")+"</span>",L="",M="";if(v){var N=parseInt(E)||25;L="<span style=\"font-family:"+A+",sans-serif;font-weight:"+C+";font-size:"+B+";\" class=\"kioskboard-key-specialcharacter\">"+r(2*N+"px",N+"px","#707070")+"</span>";var O=z.keysSpecialCharsArrayOfStrings;for(var P in Array.isArray(O)&&0<O.length&&(h=O.reduce(function(a,b,c){return a[c]=b,a},{})),h)if(Object.prototype.hasOwnProperty.call(h,P)){var Q=h[P],R="<span style=\"font-family:"+A+",sans-serif;font-weight:"+C+";font-size:"+B+";\" class=\"kioskboard-key\" data-index=\""+P.toString()+"\" data-value=\""+Q.toString()+"\">"+Q.toString()+"</span>";M+=R}}if(u===k.Numpad){var S=z.keysNumpadArrayOfNumbers;Array.isArray(S)&&10===S.length&&(i=S.reduce(function(a,b,c){return a[c]=b,a},{}));var T="";for(var U in i)if(Object.prototype.hasOwnProperty.call(i,U)){var V=U,W=i[U],X="<span style=\"font-family:"+A+",sans-serif;font-weight:"+C+";font-size:"+B+";\" class=\"kioskboard-key kioskboard-key-"+W.toString()+" "+("9"===V?"kioskboard-key-last":"")+"\" data-index=\""+V.toString()+"\" data-value=\""+W.toString()+"\">"+W.toString()+"</span>";T+=X}y+="<div class=\"kioskboard-row kioskboard-row-numpad\">"+T+K+"</div>"}if(u===k.Keyboard||u===k.All){if(u===k.All){var Y="";for(var Z in j)if(Object.prototype.hasOwnProperty.call(j,Z)){var $=j[Z],_="<span style=\"font-family:"+A+",sans-serif;font-weight:"+C+";font-size:"+B+";\" class=\"kioskboard-key kioskboard-key-"+$.toString()+"\" data-index=\""+Z.toString()+"\" data-value=\""+$.toString()+"\">"+$.toString()+"</span>";Y+=_}y+="<div class=\"kioskboard-row kioskboard-row-top\">"+Y+"</div>"}for(var aa=0;aa<b.length;aa++){var e=b[aa],ba="";for(var ca in e)if(Object.prototype.hasOwnProperty.call(e,ca)){var da=e[ca],ea="<span style=\"font-family:"+A+",sans-serif;font-weight:"+C+";font-size:"+B+";\" class=\"kioskboard-key kioskboard-key-"+da.toString().toLocaleLowerCase(w)+"\" data-index=\""+ca.toString()+"\" data-value=\""+da.toString()+"\">"+da.toString()+"</span>";ba+=ea}y+="<div class=\"kioskboard-row kioskboard-row-dynamic\">"+ba+"</div>"}if(y+="<div class=\"kioskboard-row kioskboard-row-bottom "+(v?"kioskboard-with-specialcharacter":"")+"\">"+J+L+I+K+"</div>",v){var fa="<span class=\"kioskboard-specialcharacter-close\">"+s("18px","#707070")+"</span>",ga="<div class=\"kioskboard-specialcharacters-content\">"+M+"</div>";y+="<div class=\"kioskboard-row kioskboard-row-specialcharacters\">"+fa+ga+"</div>"}}var ha=function(b){var c=a.document.createElement("div");return c.className="kioskboard-wrapper",c.innerHTML=b.trim(),c}(y),ia=!0===z.cssAnimations,ja="no-animation",ka="no-animation",la=0;ia&&(ja="kioskboard-with-animation",ka="kioskboard-fade",la="number"==typeof z.cssAnimationsDuration?z.cssAnimationsDuration:360,"slide"===z.cssAnimationsStyle&&(ka="kioskboard-slide"));var ma="string"==typeof z.theme&&0<z.theme.length?z.theme.trim():"light",na=a.document.createElement("div");na.id="KioskBoard-VirtualKeyboard",na.classList.add("kioskboard-theme-"+ma),na.classList.add(ja),na.classList.add(ka),na.classList.add(D?"kioskboard-touppercase":"kioskboard-tolowercase"),na.lang=w,na.style.webkitLocale="\""+w+"\"",na.style.animationDuration=ia?la+"ms":"",na.appendChild(ha);var oa=new Event("change",{bubbles:!0,cancelable:!0});f.addEventListener("keypress",function(a){var b=!0===z.allowRealKeyboard;return b?void(m=a.currentTarget.value.split("")):(a.stopPropagation(),a.preventDefault(),a.returnValue=!1,a.cancelBubble=!0,!1)},!1);var pa=function(b){var c=a.document.getElementById(na.id).getElementsByClassName("kioskboard-key");if(c&&0<c.length)for(var d,e=0;e<c.length;e++)d=c[e],d.addEventListener("click",function(a){a.preventDefault();var c=1*(b.getAttribute("maxlength")||""),d=1*(b.getAttribute("max")||""),e=(b.value||"").length||0;if(0<c&&e>=c)return!1;if(0<d&&e>=d)return!1;b.focus();var f=a.currentTarget.dataset.value||"";f=D?f.toLocaleUpperCase(w):f.toLocaleLowerCase(w);for(var g=f.split(""),h=0;h<g.length;h++)l=b.selectionStart||(b.value||"").length,m.splice(l,0,g[h]),b.value=m.join(""),"number"!==b.type&&b.setSelectionRange(l+1,l+1),b.dispatchEvent(oa)},!1);var f=a.document.getElementById(na.id).getElementsByClassName("kioskboard-key-capslock")[0];f&&f.addEventListener("click",function(a){a.preventDefault(),b.focus(),a.currentTarget.classList.contains("capslock-active")?(a.currentTarget.classList.remove("capslock-active"),na.classList.add("kioskboard-tolowercase"),na.classList.remove("kioskboard-touppercase"),D=!1):(a.currentTarget.classList.add("capslock-active"),na.classList.remove("kioskboard-tolowercase"),na.classList.add("kioskboard-touppercase"),D=!0)},!1);var g=a.document.getElementById(na.id).getElementsByClassName("kioskboard-key-backspace")[0];g&&g.addEventListener("click",function(a){a.preventDefault(),l=b.selectionStart||(b.value||"").length,b.focus(),m.splice(l-1,1),b.value=m.join(""),"number"!==b.type&&b.setSelectionRange(l-1,l-1),b.dispatchEvent(oa)},!1);var h=a.document.getElementById(na.id).getElementsByClassName("kioskboard-key-specialcharacter")[0],i=a.document.getElementById(na.id).getElementsByClassName("kioskboard-row-specialcharacters")[0];h&&i&&h.addEventListener("click",function(a){a.preventDefault(),b.focus(),a.currentTarget.classList.contains("specialcharacter-active")?(a.currentTarget.classList.remove("specialcharacter-active"),i.classList.remove("kioskboard-specialcharacter-show")):(a.currentTarget.classList.add("specialcharacter-active"),i.classList.add("kioskboard-specialcharacter-show"))},!1);var j=a.document.getElementById(na.id).getElementsByClassName("kioskboard-specialcharacter-close")[0];j&&h&&i&&j.addEventListener("click",function(a){a.preventDefault(),b.focus(),h.classList.remove("specialcharacter-active"),i.classList.remove("kioskboard-specialcharacter-show")},!1)},qa=a.document.getElementById(na.id);if(!qa){a.document.body.appendChild(na),qa=a.document.getElementById(na.id);var ra=Math.round(a.innerHeight),sa=Math.round(a.document.body.clientHeight),ta=Math.round(a.document.getElementById(na.id).offsetHeight);if(ta>Math.round(2*(ra/3))){var ua=a.document.getElementById(na.id).getElementsByClassName("kioskboard-wrapper")[0];ua.style.maxHeight=Math.round(4*(ra/5))+"px",ua.style.overflowX="hidden",ua.style.overflowY="auto",ua.classList.add("kioskboard-overflow")}var va=f.getBoundingClientRect().top||0,wa=a.document.documentElement.scrollTop||0,xa=Math.round(va+wa)-50;if(sa<=xa+ta){var ya=a.document.getElementById("KioskboardBodyPadding");ya&&null!==ya.parentNode&&ya.parentNode.removeChild(ya);var za=a.document.createRange();za.selectNode(a.document.head);var Aa=za.createContextualFragment("<style id=\"KioskboardBodyPadding\">.kioskboard-body-padding {padding-bottom:"+ta+"px !important;}</style>");a.document.head.appendChild(Aa),a.document.body.classList.add("kioskboard-body-padding")}var Ba=!0===z.autoScroll,Ca=!0===z.cssAnimations?"smooth":"auto",Da=!0===z.cssAnimations&&"number"==typeof z.cssAnimationsDuration?z.cssAnimationsDuration:0;if(Ba){var Ea=navigator.userAgent.toLocaleLowerCase("en");if(-1>=Ea.indexOf("edge")&&-1>=Ea.indexOf(".net4"))var Fa=setTimeout(function(){a.scrollTo({top:xa,left:0,behavior:Ca}),clearTimeout(Fa)},Da);else a.document.documentElement.scrollTop=xa}pa(f);var Ga=function(b){if(b.target!==f&&!t(b,qa)){na.classList.add(ka+"-remove");var c=setTimeout(function(){null!==qa.parentNode&&(qa.parentNode.removeChild(qa),a.document.body.classList.remove("kioskboard-body-padding"),a.document.removeEventListener("click",Ga)),clearTimeout(c)},la)}if(-1<d.indexOf(f))var e=setTimeout(function(){b.target.blur(),b.target.focus(),clearTimeout(e)},la)};a.document.addEventListener("click",Ga)}};c.addEventListener("focus",l);c.addEventListener("focusout",function(a){g||f||a.currentTarget.removeAttribute("readonly")})},E=function(c,d){var e=(a.location||{}).protocol;if(-1>=["http:","data:","chrome:","chrome-extension:","https:"].indexOf(e))return n("The Browser has blocked this request by CORS policy."),!1;if(!b){var f=new XMLHttpRequest;f.open("GET",c,!0),f.setRequestHeader("Content-type","application/json; charset=utf-8"),f.send(),f.onreadystatechange=function(){if(4===f.readyState)if(200===f.status){var a=f.responseText||[];if("string"==typeof a&&0<a.length){var c=JSON.parse(a);if(m(c))b=c,D(c,d);else return n("Array of objects of the keys are not valid. \n\nVisit to learn more: "+g),!1}}else return n("XMLHttpRequest has been failed. Please check your URL path or protocol."),!1}}},F=0;F<v.length;F++){var G=v[F],H=((G||{}).tagName||"").toLocaleLowerCase("en");-1<w.indexOf(H)?b?D(b,G):E(z.keysJsonUrl,G):o("You have to call the \"KioskBoard\" with an ID/ClassName of an Input or a TextArea element. Your element's tag name is: \""+H+"\". \n\nYou can visit the Documentation page to learn more. \n\nVisit: "+g)}}};return u}});