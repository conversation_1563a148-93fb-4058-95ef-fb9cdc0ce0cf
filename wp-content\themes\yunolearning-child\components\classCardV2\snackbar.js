Vue.component("star-rating", VueStarRating.default);
Vue.component("yuno-snackbar", {
  props: ["data", "options"],
  template: `
    <div v-if="isSnackbarActive" class="notices is-bottom yunoSnackbar">
      	<div class="snackbar is-success is-bottom slide-in-bottom">
			<div style="display: flex; align-items: baseline; justify-content: space-between; width: 100%; padding-bottom: 15px">
				<h2 class="headline6 onSurface">Class Feedback</h2>
				<a href="#" class="closeSnackbar" @click.stop.prevent="closeSnackbar">
					<span class="material-icons-outlined">close</span>
				</a>
			</div>
      <div class="subtitle2 onSurfaceVariant pb-3" v-if="data.course.title !== undefined">{{ data.course.title }}</div>
			<yuno-form 
				@initForm="onFormSubmit(false, true, true)"
				@setRating="onSetRating"
				@starLabel="onStarLabel"
				@starLabelVisibility="onStarLabelVisibility"
				:options="{'loading': isFormLoading, 'hasIssues': hasIssues, 'isEdit': false, 'ratingLabels': ratingLabels , 'isForm': isForm}"
				:data="payload">
			</yuno-form>
		</div>
    </div>`,

  data() {
    return {
      isSnackbarActive: false,
      fetchedIssues: false,
      selectedIssues: [],
      isForm: false,
      hasIssues: false,
      isFormLoading: false,
      ratingLabels: [
        { rating: 1, label: "Very poor", isActive: false },
        { rating: 2, label: "Not so good", isActive: false },
        { rating: 3, label: "Average", isActive: false },
        { rating: 4, label: "Quite good", isActive: false },
        { rating: 5, label: "Excellent", isActive: false },
      ],
      payload: {
        id: 1,
        user_id: 14736,
        instructor_id: 0,
        taxonomy_id: 0,
        feedback_ids: [],
        review_source: "YUNO",
        review_type: "star",
        review: "",
        post_id: "",
        rating: 0,
        feedback_type: "",
      },
    };
  },

  computed: {
    ...Vuex.mapState(["topIssuesCited", "createResource", "updateLink"]),
  },

  methods: {
    onRate() {
      this.isForm = true;
    },

    toggleIssueSelection(issueId) {
      const index = this.selectedIssues.indexOf(issueId);
      if (index !== -1) {
        this.selectedIssues.splice(index, 1);
      } else if (this.selectedIssues.length < 5) {
        this.selectedIssues.push(issueId);
      } else {
        this.$buefy.toast.open({
          message: "You can only select up to 5 issues.",
          type: "is-warning",
        });
      }
    },

    gotIssues(options) {
      this.topIssuesCited.loading = false;
      if (
        options.response !== undefined &&
        options.response.data !== undefined &&
        options.response.data.code === 200
      ) {
        const data = options.response.data.data;
        this.fetchedIssues = true;
      }
    },

    fetchIssues() {
      if (this.fetchedIssues) {
        return;
      }

      const instance = this;
      this.topIssuesCited.loading = true;
      const options = {
        apiURL: YUNOCommon.config.reviewIssues(
          1,
          "learner",
          this.updateLink.data.taxonomy_id
        ),
        module: "gotData",
        store: "topIssuesCited",
        callback: true,
        callbackFunc: function (options) {
          return instance.gotIssues(options);
        },
      };

      this.$store.dispatch("fetchData", options);
    },

    onStarLabelVisibility() {
      this.resetLabel();
    },

    onStarLabel(data) {
      this.currentRating(data);
    },

    currentRating(data) {
      const currentLabel = YUNOCommon.findObjectByKey(
          this.ratingLabels,
          "rating",
          data
        ),
        ratingLabels = this.ratingLabels;

      for (let i = 0; i < ratingLabels.length; i++) {
        const label = ratingLabels[i];
        label.isActive = false;
      }

      currentLabel.isActive = true;
    },

    resetLabel() {
      const ratingLabels = this.ratingLabels;

      for (let i = 0; i < ratingLabels.length; i++) {
        const label = ratingLabels[i];
        label.isActive = false;
      }
    },

    setRating(data) {
      if (data === 5) {
        this.payload.feedback_ids = [];
        this.hasIssues = false;
        this.onFormSubmit(false, true, false);
      } else {
        this.payload.feedback_ids = [];
        this.hasIssues = true;
        this.onRate();
        this.onFormSubmit(true, false, true);
      }
    },
    onSetRating(data) {
      this.syncRating(data);
      if (data === 5) {
        this.payload.feedback_ids = [];
        this.hasIssues = false;
        this.isForm = false;
        this.onFormSubmit(false, true, false);
      } else {
        this.payload.feedback_ids = [];
        this.hasIssues = true;
        this.onRate();
        this.onFormSubmit(true, false, true);
      }
    },
    syncRating(rating) {
      this.payload.rating = rating;
      this.$emit("ratingUpdated", rating); // Emit an event with the updated rating
    },

    setPayload(hasIssues) {
      let payload = this.payload;

      payload.instructor_id = this.$props.data.instructor.user.id;
      payload.post_id = this.updateLink.data.post_id;
      payload.taxonomy_id = this.updateLink.data.taxonomy_id;

      if (hasIssues) {
        payload.feedback_type = "issue";
      } else {
        payload.feedback_type = "";
      }
    },

    formPosted(options, isSnackbar, isFormPosting, hasFullForm) {
      this.isFormLoading = false;

      if (
        options.response !== undefined &&
        options.response.data !== undefined &&
        options.response.data.code === 201
      ) {
        const response = options.response.data;

        this.$buefy.toast.open({
          duration: 5000,
          message: `${response.message}`,
        });

        this.manageVisiblity(isSnackbar, isFormPosting, hasFullForm);
      } else {
        const response = options.response.data;

        this.$buefy.toast.open({
          duration: 5000,
          message: `${response.message}`,
          position: "is-bottom",
          type: "is-danger",
        });
      }
    },

    onFormSubmit(isSnackbar, isFormPosting, hasFullForm) {
      this.setPayload(this.hasIssues);
      this.isFormLoading = isFormPosting;

      const instance = this;
      const options = {
        apiURL: YUNOCommon.config.reviewPost(),
        module: "gotData",
        store: "createResource",
        payload: this.payload,
        callback: true,
        callbackFunc: function (options) {
          return instance.formPosted(
            options,
            isSnackbar,
            isFormPosting,
            hasFullForm
          );
        },
      };

      this.$store.dispatch("postData", options);
    },

    openSnackbar() {
      this.isSnackbarActive = true;
    },

    closeSnackbar() {
      this.isSnackbarActive = false;
    },

    manageVisiblity(isSnackbar, isFormPosting, hasFullForm) {
      this.isSnackbarActive = isSnackbar;
      this.isForm = hasFullForm;
    },
  },
});
