(function(t, e) {
    "object" === typeof exports && "object" === typeof module ? module.exports = e() : "function" === typeof define && define.amd ? define([], e) : "object" === typeof exports ? exports["VueStarRating"] = e() : t["VueStarRating"] = e()
})("undefined" !== typeof self ? self : this, (function() {
    return function(t) {
        var e = {};

        function r(i) {
            if (e[i]) return e[i].exports;
            var n = e[i] = {
                i: i,
                l: !1,
                exports: {}
            };
            return t[i].call(n.exports, n, n.exports, r), n.l = !0, n.exports
        }
        return r.m = t, r.c = e, r.d = function(t, e, i) {
            r.o(t, e) || Object.defineProperty(t, e, {
                enumerable: !0,
                get: i
            })
        }, r.r = function(t) {
            "undefined" !== typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, {
                value: "Module"
            }), Object.defineProperty(t, "__esModule", {
                value: !0
            })
        }, r.t = function(t, e) {
            if (1 & e && (t = r(t)), 8 & e) return t;
            if (4 & e && "object" === typeof t && t && t.__esModule) return t;
            var i = Object.create(null);
            if (r.r(i), Object.defineProperty(i, "default", {
                    enumerable: !0,
                    value: t
                }), 2 & e && "string" != typeof t)
                for (var n in t) r.d(i, n, function(e) {
                    return t[e]
                }.bind(null, n));
            return i
        }, r.n = function(t) {
            var e = t && t.__esModule ? function() {
                return t["default"]
            } : function() {
                return t
            };
            return r.d(e, "a", e), e
        }, r.o = function(t, e) {
            return Object.prototype.hasOwnProperty.call(t, e)
        }, r.p = "", r(r.s = "fb15")
    }({
        "27c2": function(t, e, r) {
            var i = r("4bad");
            e = i(!1), e.push([t.i, ".vue-star-rating-star[data-v-fde73a0c]{display:inline-block}.vue-star-rating-pointer[data-v-fde73a0c]{cursor:pointer}.vue-star-rating[data-v-fde73a0c]{display:flex;align-items:center}.vue-star-rating-inline[data-v-fde73a0c]{display:inline-flex}.vue-star-rating-rating-text[data-v-fde73a0c]{margin-left:7px}.vue-star-rating-rtl[data-v-fde73a0c]{direction:rtl}.vue-star-rating-rtl .vue-star-rating-rating-text[data-v-fde73a0c]{margin-right:10px;direction:rtl}.sr-only[data-v-fde73a0c]{position:absolute;left:-10000px;top:auto;width:1px;height:1px;overflow:hidden}", ""]), t.exports = e
        },
        "2b2b": function(t, e, r) {
            "use strict";
            var i = r("3c76"),
                n = r.n(i);
            n.a
        },
        "3c76": function(t, e, r) {
            var i = r("27c2");
            "string" === typeof i && (i = [
                [t.i, i, ""]
            ]), i.locals && (t.exports = i.locals);
            var n = r("499e").default;
            n("af45d76c", i, !0, {
                sourceMap: !1,
                shadowMode: !1
            })
        },
        "499e": function(t, e, r) {
            "use strict";

            function i(t, e) {
                for (var r = [], i = {}, n = 0; n < e.length; n++) {
                    var o = e[n],
                        a = o[0],
                        s = o[1],
                        l = o[2],
                        c = o[3],
                        d = {
                            id: t + ":" + n,
                            css: s,
                            media: l,
                            sourceMap: c
                        };
                    i[a] ? i[a].parts.push(d) : r.push(i[a] = {
                        id: a,
                        parts: [d]
                    })
                }
                return r
            }
            r.r(e), r.d(e, "default", (function() {
                return p
            }));
            var n = "undefined" !== typeof document;
            if ("undefined" !== typeof DEBUG && DEBUG && !n) throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");
            var o = {},
                a = n && (document.head || document.getElementsByTagName("head")[0]),
                s = null,
                l = 0,
                c = !1,
                d = function() {},
                u = null,
                h = "data-vue-ssr-id",
                f = "undefined" !== typeof navigator && /msie [6-9]\b/.test(navigator.userAgent.toLowerCase());

            function p(t, e, r, n) {
                c = r, u = n || {};
                var a = i(t, e);
                return g(a),
                    function(e) {
                        for (var r = [], n = 0; n < a.length; n++) {
                            var s = a[n],
                                l = o[s.id];
                            l.refs--, r.push(l)
                        }
                        e ? (a = i(t, e), g(a)) : a = [];
                        for (n = 0; n < r.length; n++) {
                            l = r[n];
                            if (0 === l.refs) {
                                for (var c = 0; c < l.parts.length; c++) l.parts[c]();
                                delete o[l.id]
                            }
                        }
                    }
            }

            function g(t) {
                for (var e = 0; e < t.length; e++) {
                    var r = t[e],
                        i = o[r.id];
                    if (i) {
                        i.refs++;
                        for (var n = 0; n < i.parts.length; n++) i.parts[n](r.parts[n]);
                        for (; n < r.parts.length; n++) i.parts.push(m(r.parts[n]));
                        i.parts.length > r.parts.length && (i.parts.length = r.parts.length)
                    } else {
                        var a = [];
                        for (n = 0; n < r.parts.length; n++) a.push(m(r.parts[n]));
                        o[r.id] = {
                            id: r.id,
                            refs: 1,
                            parts: a
                        }
                    }
                }
            }

            function v() {
                var t = document.createElement("style");
                t.setAttribute("nonce", yunoNonce); 
                return t.type = "text/css", a.appendChild(t), t
            }

            function m(t) {
                var e, r, i = document.querySelector("style[" + h + '~="' + t.id + '"]');
                if (i) {
                    if (c) return d;
                    i.parentNode.removeChild(i)
                }
                if (f) {
                    var n = l++;
                    i = s || (s = v()), e = b.bind(null, i, n, !1), r = b.bind(null, i, n, !0)
                } else i = v(), e = C.bind(null, i), r = function() {
                    i.parentNode.removeChild(i)
                };
                return e(t),
                    function(i) {
                        if (i) {
                            if (i.css === t.css && i.media === t.media && i.sourceMap === t.sourceMap) return;
                            e(t = i)
                        } else r()
                    }
            }
            var y = function() {
                var t = [];
                return function(e, r) {
                    return t[e] = r, t.filter(Boolean).join("\n")
                }
            }();

            function b(t, e, r, i) {
                var n = r ? "" : i.css;
                if (t.styleSheet) t.styleSheet.cssText = y(e, n);
                else {
                    var o = document.createTextNode(n),
                        a = t.childNodes;
                    a[e] && t.removeChild(a[e]), a.length ? t.insertBefore(o, a[e]) : t.appendChild(o)
                }
            }

            function C(t, e) {
                var r = e.css,
                    i = e.media,
                    n = e.sourceMap;
                if (i && t.setAttribute("media", i), u.ssrId && t.setAttribute(h, e.id), n && (r += "\n/*# sourceURL=" + n.sources[0] + " */", r += "\n/*# sourceMappingURL=data:application/json;base64," + btoa(unescape(encodeURIComponent(JSON.stringify(n)))) + " */"), t.styleSheet) t.styleSheet.cssText = r;
                else {
                    while (t.firstChild) t.removeChild(t.firstChild);
                    t.appendChild(document.createTextNode(r))
                }
            }
        },
        "4bad": function(t, e, r) {
            "use strict";

            function i(t, e) {
                var r = t[1] || "",
                    i = t[3];
                if (!i) return r;
                if (e && "function" === typeof btoa) {
                    var o = n(i),
                        a = i.sources.map((function(t) {
                            return "/*# sourceURL=".concat(i.sourceRoot || "").concat(t, " */")
                        }));
                    return [r].concat(a).concat([o]).join("\n")
                }
                return [r].join("\n")
            }

            function n(t) {
                var e = btoa(unescape(encodeURIComponent(JSON.stringify(t)))),
                    r = "sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(e);
                return "/*# ".concat(r, " */")
            }
            t.exports = function(t) {
                var e = [];
                return e.toString = function() {
                    return this.map((function(e) {
                        var r = i(e, t);
                        return e[2] ? "@media ".concat(e[2], " {").concat(r, "}") : r
                    })).join("")
                }, e.i = function(t, r, i) {
                    "string" === typeof t && (t = [
                        [null, t, ""]
                    ]);
                    var n = {};
                    if (i)
                        for (var o = 0; o < this.length; o++) {
                            var a = this[o][0];
                            null != a && (n[a] = !0)
                        }
                    for (var s = 0; s < t.length; s++) {
                        var l = [].concat(t[s]);
                        i && n[l[0]] || (r && (l[2] ? l[2] = "".concat(r, " and ").concat(l[2]) : l[2] = r), e.push(l))
                    }
                }, e
            }
        },
        "70a0": function(t, e, r) {
            var i = r("812a");
            "string" === typeof i && (i = [
                [t.i, i, ""]
            ]), i.locals && (t.exports = i.locals);
            var n = r("499e").default;
            n("4599b915", i, !0, {
                sourceMap: !1,
                shadowMode: !1
            })
        },
        "812a": function(t, e, r) {
            var i = r("4bad");
            e = i(!1), e.push([t.i, ".vue-star-rating-star[data-v-ef4bc576]{overflow:visible!important}.vue-star-rating-star-rotate[data-v-ef4bc576]{transition:all .25s}.vue-star-rating-star-rotate[data-v-ef4bc576]:hover{transition:transform .25s;transform:rotate(-15deg) scale(1.3)}", ""]), t.exports = e
        },
        8875: function(t, e, r) {
            var i, n, o;
            (function(r, a) {
                n = [], i = a, o = "function" === typeof i ? i.apply(e, n) : i, void 0 === o || (t.exports = o)
            })("undefined" !== typeof self && self, (function() {
                function t() {
                    var e = Object.getOwnPropertyDescriptor(document, "currentScript");
                    if (!e && "currentScript" in document && document.currentScript) return document.currentScript;
                    if (e && e.get !== t && document.currentScript) return document.currentScript;
                    try {
                        throw new Error
                    } catch (f) {
                        var r, i, n, o = /.*at [^(]*\((.*):(.+):(.+)\)$/gi,
                            a = /@([^@]*):(\d+):(\d+)\s*$/gi,
                            s = o.exec(f.stack) || a.exec(f.stack),
                            l = s && s[1] || !1,
                            c = s && s[2] || !1,
                            d = document.location.href.replace(document.location.hash, ""),
                            u = document.getElementsByTagName("script");
                        l === d && (r = document.documentElement.outerHTML, i = new RegExp("(?:[^\\n]+?\\n){0," + (c - 2) + "}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*", "i"), n = r.replace(i, "$1").trim());
                        for (var h = 0; h < u.length; h++) {
                            if ("interactive" === u[h].readyState) return u[h];
                            if (u[h].src === l) return u[h];
                            if (l === d && u[h].innerHTML && u[h].innerHTML.trim() === n) return u[h]
                        }
                        return null
                    }
                }
                return t
            }))
        },
        ab73: function(t, e, r) {
            "use strict";
            var i = r("70a0"),
                n = r.n(i);
            n.a
        },
        d4aa: function(t, e) {
            class r {
                constructor(t) {
                    this.color = t
                }
                parseAlphaColor() {
                    return /^rgba\((\d{1,3}%?\s*,\s*){3}(\d*(?:\.\d+)?)\)$/.test(this.color) ? this.parseRgba() : /^hsla\(\d+\s*,\s*([\d.]+%\s*,\s*){2}(\d*(?:\.\d+)?)\)$/.test(this.color) ? this.parseHsla() : /^#([0-9A-Fa-f]{4}|[0-9A-Fa-f]{8})$/.test(this.color) ? this.parseAlphaHex() : /^transparent$/.test(this.color) ? this.parseTransparent() : {
                        color: this.color,
                        opacity: "1"
                    }
                }
                parseRgba() {
                    return {
                        color: this.color.replace(/,(?!.*,).*(?=\))|a/g, ""),
                        opacity: this.color.match(/\.\d+|[01](?=\))/)[0]
                    }
                }
                parseHsla() {
                    return {
                        color: this.color.replace(/,(?!.*,).*(?=\))|a/g, ""),
                        opacity: this.color.match(/\.\d+|[01](?=\))/)[0]
                    }
                }
                parseAlphaHex() {
                    return {
                        color: 5 === this.color.length ? this.color.substring(0, 4) : this.color.substring(0, 7),
                        opacity: 5 === this.color.length ? (parseInt(this.color.substring(4, 5) + this.color.substring(4, 5), 16) / 255).toFixed(2) : (parseInt(this.color.substring(7, 9), 16) / 255).toFixed(2)
                    }
                }
                parseTransparent() {
                    return {
                        color: "#fff",
                        opacity: 0
                    }
                }
            }
            t.exports = r
        },
        fb15: function(t, e, r) {
            "use strict";
            if (r.r(e), "undefined" !== typeof window) {
                var i = window.document.currentScript,
                    n = r("8875");
                i = n(), "currentScript" in document || Object.defineProperty(document, "currentScript", {
                    get: n
                });
                var o = i && i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);
                o && (r.p = o[1])
            }
            var a = function() {
                    var t = this,
                        e = t.$createElement,
                        r = t._self._c || e;
                    return r("div", {
                        class: ["vue-star-rating", {
                            "vue-star-rating-rtl": t.rtl
                        }, {
                            "vue-star-rating-inline": t.inline
                        }]
                    }, [r("div", {
                        staticClass: "sr-only"
                    }, [t._t("screen-reader", [r("span", [t._v("Rated " + t._s(t.selectedRating) + " stars out of " + t._s(t.maxRating))])], {
                        rating: t.selectedRating,
                        stars: t.maxRating
                    })], 2), r("div", {
                        staticClass: "vue-star-rating",
                        on: {
                            mouseleave: t.resetRating
                        }
                    }, [t._l(t.maxRating, (function(e) {
                        return r("span", {
                            key: e,
                            class: [{
                                "vue-star-rating-pointer": !t.readOnly
                            }, "vue-star-rating-star"],
                            style: {
                                "margin-right": t.margin + "px"
                            }
                        }, [r("star", {
                            attrs: {
                                fill: t.fillLevel[e - 1],
                                size: t.starSize,
                                points: t.starPoints,
                                "star-id": e,
                                step: t.step,
                                "active-color": t.currentActiveColor,
                                "inactive-color": t.inactiveColor,
                                "border-color": t.borderColor,
                                "active-border-color": t.currentActiveBorderColor,
                                "border-width": t.borderWidth,
                                "rounded-corners": t.roundedCorners,
                                rtl: t.rtl,
                                glow: t.glow,
                                "glow-color": t.glowColor,
                                animate: t.animate
                            },
                            on: {
                                "star-selected": function(e) {
                                    return t.setRating(e, !0)
                                },
                                "star-mouse-move": t.setRating
                            }
                        })], 1)
                    })), t.showRating ? r("span", {
                        class: ["vue-star-rating-rating-text", t.textClass]
                    }, [t._v(" " + t._s(t.formattedRating))]) : t._e()], 2)])
                },
                s = [],
                l = function() {
                    var t = this,
                        e = t.$createElement,
                        r = t._self._c || e;
                    return r("svg", {
                        class: ["vue-star-rating-star", {
                            "vue-star-rating-star-rotate": t.shouldAnimate
                        }],
                        attrs: {
                            height: t.starSize,
                            width: t.starSize,
                            viewBox: t.viewBox
                        },
                        on: {
                            mousemove: t.mouseMoving,
                            click: t.selected,
                            touchstart: t.touchStart,
                            touchend: t.touchEnd
                        }
                    }, [r("linearGradient", {
                        attrs: {
                            id: t.grad,
                            x1: "0",
                            x2: "100%",
                            y1: "0",
                            y2: "0"
                        }
                    }, [r("stop", {
                        attrs: {
                            offset: t.starFill,
                            "stop-color": t.rtl ? t.getColor(t.inactiveColor) : t.getColor(t.activeColor),
                            "stop-opacity": t.rtl ? t.getOpacity(t.inactiveColor) : t.getOpacity(t.activeColor)
                        }
                    }), r("stop", {
                        attrs: {
                            offset: t.starFill,
                            "stop-color": t.rtl ? t.getColor(t.activeColor) : t.getColor(t.inactiveColor),
                            "stop-opacity": t.rtl ? t.getOpacity(t.activeColor) : t.getOpacity(t.inactiveColor)
                        }
                    })], 1), r("filter", {
                        attrs: {
                            id: t.glowId,
                            height: "130%",
                            width: "130%",
                            filterUnits: "userSpaceOnUse"
                        }
                    }, [r("feGaussianBlur", {
                        attrs: {
                            stdDeviation: t.glow,
                            result: "coloredBlur"
                        }
                    }), r("feMerge", [r("feMergeNode", {
                        attrs: {
                            in: "coloredBlur"
                        }
                    }), r("feMergeNode", {
                        attrs: {
                            in: "SourceGraphic"
                        }
                    })], 1)], 1), t.glowColor && t.glow > 0 ? r("polygon", {
                        directives: [{
                            name: "show",
                            rawName: "v-show",
                            value: t.fill > 1,
                            expression: "fill > 1"
                        }],
                        attrs: {
                            points: t.starPointsToString,
                            fill: t.gradId,
                            stroke: t.glowColor,
                            filter: "url(#" + t.glowId + ")",
                            "stroke-width": t.border
                        }
                    }) : t._e(), r("polygon", {
                        attrs: {
                            points: t.starPointsToString,
                            fill: t.gradId,
                            stroke: t.getBorderColor,
                            "stroke-width": t.border,
                            "stroke-linejoin": t.strokeLinejoin
                        }
                    }), r("polygon", {
                        attrs: {
                            points: t.starPointsToString,
                            fill: t.gradId
                        }
                    })], 1)
                },
                c = [],
                d = r("d4aa"),
                u = r.n(d),
                h = {
                    name: "Star",
                    props: {
                        fill: {
                            type: Number,
                            default: 0
                        },
                        points: {
                            type: Array,
                            default () {
                                return []
                            }
                        },
                        size: {
                            type: Number,
                            default: 50
                        },
                        starId: {
                            type: Number,
                            required: !0
                        },
                        activeColor: {
                            type: String,
                            required: !0
                        },
                        inactiveColor: {
                            type: String,
                            required: !0
                        },
                        borderColor: {
                            type: String,
                            default: "#000"
                        },
                        activeBorderColor: {
                            type: String,
                            default: "#000"
                        },
                        borderWidth: {
                            type: Number,
                            default: 0
                        },
                        roundedCorners: {
                            type: Boolean,
                            default: !1
                        },
                        rtl: {
                            type: Boolean,
                            default: !1
                        },
                        glow: {
                            type: Number,
                            default: 0
                        },
                        glowColor: {
                            type: String,
                            default: null,
                            required: !1
                        },
                        animate: {
                            type: Boolean,
                            default: !1
                        }
                    },
                    data() {
                        return {
                            starPoints: [19.8, 2.2, 6.6, 43.56, 39.6, 17.16, 0, 17.16, 33, 43.56],
                            grad: "",
                            glowId: "",
                            isStarActive: !0
                        }
                    },
                    computed: {
                        starPointsToString() {
                            return this.starPoints.join(",")
                        },
                        gradId() {
                            return "url(#" + this.grad + ")"
                        },
                        starSize() {
                            const t = this.roundedCorners && this.borderWidth <= 0 ? parseInt(this.size) - parseInt(this.border) : this.size;
                            return parseInt(t) + parseInt(this.border)
                        },
                        starFill() {
                            return this.rtl ? 100 - this.fill + "%" : this.fill + "%"
                        },
                        border() {
                            return this.roundedCorners && this.borderWidth <= 0 ? 6 : this.borderWidth
                        },
                        getBorderColor() {
                            return this.roundedCorners && this.borderWidth <= 0 ? this.fill <= 0 ? this.inactiveColor : this.activeColor : this.fill <= 0 ? this.borderColor : this.activeBorderColor
                        },
                        maxSize() {
                            return this.starPoints.reduce((function(t, e) {
                                return Math.max(t, e)
                            }))
                        },
                        viewBox() {
                            return "0 0 " + this.maxSize + " " + this.maxSize
                        },
                        shouldAnimate() {
                            return this.animate && this.isStarActive
                        },
                        strokeLinejoin() {
                            return this.roundedCorners ? "round" : "miter"
                        }
                    },
                    created() {
                        this.starPoints = this.points.length ? this.points : this.starPoints, this.calculatePoints(), this.grad = this.getRandomId(), this.glowId = this.getRandomId()
                    },
                    methods: {
                        mouseMoving(t) {
                            "undefined" !== t.touchAction && this.$emit("star-mouse-move", {
                                event: t,
                                position: this.getPosition(t),
                                id: this.starId
                            })
                        },
                        touchStart() {
                            this.$nextTick(() => {
                                this.isStarActive = !0
                            })
                        },
                        touchEnd() {
                            this.$nextTick(() => {
                                this.isStarActive = !1
                            })
                        },
                        getPosition(t) {
                            var e = .92 * this.size;
                            const r = this.rtl ? Math.min(t.offsetX, 45) : Math.max(t.offsetX, 1);
                            var i = Math.round(100 / e * r);
                            return Math.min(i, 100)
                        },
                        selected(t) {
                            this.$emit("star-selected", {
                                id: this.starId,
                                position: this.getPosition(t)
                            })
                        },
                        getRandomId() {
                            return Math.random().toString(36).substring(7)
                        },
                        calculatePoints() {
                            this.starPoints = this.starPoints.map((t, e) => {
                                const r = e % 2 === 0 ? 1.5 * this.border : 0;
                                return this.size / this.maxSize * t + r
                            })
                        },
                        getColor(t) {
                            return new u.a(t).parseAlphaColor().color
                        },
                        getOpacity(t) {
                            return new u.a(t).parseAlphaColor().opacity
                        }
                    }
                },
                f = h;
            r("ab73");

            function p(t, e, r, i, n, o, a, s) {
                var l, c = "function" === typeof t ? t.options : t;
                if (e && (c.render = e, c.staticRenderFns = r, c._compiled = !0), i && (c.functional = !0), o && (c._scopeId = "data-v-" + o), a ? (l = function(t) {
                        t = t || this.$vnode && this.$vnode.ssrContext || this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext, t || "undefined" === typeof __VUE_SSR_CONTEXT__ || (t = __VUE_SSR_CONTEXT__), n && n.call(this, t), t && t._registeredComponents && t._registeredComponents.add(a)
                    }, c._ssrRegister = l) : n && (l = s ? function() {
                        n.call(this, (c.functional ? this.parent : this).$root.$options.shadowRoot)
                    } : n), l)
                    if (c.functional) {
                        c._injectStyles = l;
                        var d = c.render;
                        c.render = function(t, e) {
                            return l.call(e), d(t, e)
                        }
                    } else {
                        var u = c.beforeCreate;
                        c.beforeCreate = u ? [].concat(u, l) : [l]
                    } return {
                    exports: t,
                    options: c
                }
            }
            var g = p(f, l, c, !1, null, "ef4bc576", null),
                v = g.exports,
                m = {
                    components: {
                        star: v
                    },
                    model: {
                        prop: "rating",
                        event: "rating-selected"
                    },
                    props: {
                        increment: {
                            type: Number,
                            default: 1
                        },
                        rating: {
                            type: Number,
                            default: 0
                        },
                        roundStartRating: {
                            type: Boolean,
                            default: !0
                        },
                        activeColor: {
                            type: [String, Array],
                            default: "#ffd055"
                        },
                        inactiveColor: {
                            type: String,
                            default: "#d8d8d8"
                        },
                        maxRating: {
                            type: Number,
                            default: 5
                        },
                        starPoints: {
                            type: Array,
                            default () {
                                return []
                            }
                        },
                        starSize: {
                            type: Number,
                            default: 50
                        },
                        showRating: {
                            type: Boolean,
                            default: !0
                        },
                        readOnly: {
                            type: Boolean,
                            default: !1
                        },
                        textClass: {
                            type: String,
                            default: ""
                        },
                        inline: {
                            type: Boolean,
                            default: !1
                        },
                        borderColor: {
                            type: String,
                            default: "#999"
                        },
                        activeBorderColor: {
                            type: [String, Array],
                            default: null
                        },
                        borderWidth: {
                            type: Number,
                            default: 0
                        },
                        roundedCorners: {
                            type: Boolean,
                            default: !1
                        },
                        padding: {
                            type: Number,
                            default: 0
                        },
                        rtl: {
                            type: Boolean,
                            default: !1
                        },
                        fixedPoints: {
                            type: Number,
                            default: null
                        },
                        glow: {
                            type: Number,
                            default: 0
                        },
                        glowColor: {
                            type: String,
                            default: "#fff"
                        },
                        clearable: {
                            type: Boolean,
                            default: !1
                        },
                        activeOnClick: {
                            type: Boolean,
                            default: !1
                        },
                        animate: {
                            type: Boolean,
                            default: !1
                        }
                    },
                    data() {
                        return {
                            step: 0,
                            fillLevel: [],
                            currentRating: 0,
                            selectedRating: 0,
                            ratingSelected: !1
                        }
                    },
                    computed: {
                        formattedRating() {
                            return null === this.fixedPoints ? this.currentRating : this.currentRating.toFixed(this.fixedPoints)
                        },
                        shouldRound() {
                            return this.ratingSelected || this.roundStartRating
                        },
                        margin() {
                            return this.padding + this.borderWidth
                        },
                        activeColors() {
                            return Array.isArray(this.activeColor) ? this.padColors(this.activeColor, this.maxRating, this.activeColor.slice(-1)[0]) : new Array(this.maxRating).fill(this.activeColor)
                        },
                        currentActiveColor() {
                            return this.activeOnClick ? this.selectedRating > 0 ? this.activeColors[Math.ceil(this.selectedRating) - 1] : this.inactiveColor : this.currentRating > 0 ? this.activeColors[Math.ceil(this.currentRating) - 1] : this.inactiveColor
                        },
                        activeBorderColors() {
                            if (Array.isArray(this.activeBorderColor)) return this.padColors(this.activeBorderColor, this.maxRating, this.activeBorderColor.slice(-1)[0]);
                            let t = this.activeBorderColor ? this.activeBorderColor : this.borderColor;
                            return new Array(this.maxRating).fill(t)
                        },
                        currentActiveBorderColor() {
                            return this.activeOnClick ? this.selectedRating > 0 ? this.activeBorderColors[Math.ceil(this.selectedRating) - 1] : this.borderColor : this.currentRating > 0 ? this.activeBorderColors[Math.ceil(this.currentRating) - 1] : this.borderColor
                        }
                    },
                    watch: {
                        rating(t) {
                            this.currentRating = t, this.selectedRating = t, this.createStars(this.shouldRound)
                        }
                    },
                    created() {
                        this.step = 100 * this.increment, this.currentRating = this.rating, this.selectedRating = this.currentRating, this.createStars(this.roundStartRating)
                    },
                    methods: {
                        setRating(t, e) {
                            if (!this.readOnly) {
                                const r = this.rtl ? (100 - t.position) / 100 : t.position / 100;
                                this.currentRating = (t.id + r - 1).toFixed(2), this.currentRating = this.currentRating > this.maxRating ? this.maxRating : this.currentRating, e ? (this.createStars(!0, !0), this.clearable && this.currentRating === this.selectedRating ? this.selectedRating = 0 : this.selectedRating = this.currentRating, this.$emit("rating-selected", this.selectedRating), this.ratingSelected = !0) : (this.createStars(!0, !this.activeOnClick), this.$emit("current-rating", this.currentRating))
                            }
                        },
                        resetRating() {
                            this.readOnly || (this.currentRating = this.selectedRating, this.createStars(this.shouldRound))
                        },
                        createStars(t = !0, e = !0) {
                            t && this.round();
                            for (var r = 0; r < this.maxRating; r++) {
                                let t = 0;
                                r < this.currentRating && (t = this.currentRating - r > 1 ? 100 : 100 * (this.currentRating - r)), e && (this.fillLevel[r] = Math.round(t))
                            }
                        },
                        round() {
                            var t = 1 / this.increment;
                            this.currentRating = Math.min(this.maxRating, Math.ceil(this.currentRating * t) / t)
                        },
                        padColors(t, e, r) {
                            return Object.assign(new Array(e).fill(r), t)
                        }
                    }
                },
                y = m,
                b = (r("2b2b"), p(y, a, s, !1, null, "fde73a0c", null)),
                C = b.exports,
                R = C;
            e["default"] = R
        }
    })
}));
//# sourceMappingURL=VueStarRating.umd.min.js.map