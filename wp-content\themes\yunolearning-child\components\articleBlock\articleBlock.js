const YUNOArticleBlock = (function($) {
    
    const articleBlock = function() {
        Vue.component('yuno-article-block', {
            props: ["options"],
            template: `
                <section id="articleBlock" class="articleBlock" :class="[options.align, options.theme, options.btmMargin ? 'btmMargin' : '', options.topGap === false ? 'noTopGap' : '']" :style="heroStyle">
                    <article class="container">
                        <h2 class="articleTitle" :class="[options.lightTitle ? 'noBold' : '']">{{options.title}}</h2>
                        <template v-if="options.isVideo !== undefined && options.isVideo">
                            <div class="videoLPPlayer" v-if="options.videoURL !== undefined">
                                <iframe width="560" height="315" :src="options.videoURL" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                            </div>
                        </template>
                        <p class="articleDescription" v-if="options.description !== undefined && options.description !== ''">{{options.description}}</p>
                        <div class="embedDescription" :class="[options.cta !== undefined ? 'btmMargin' : '']" v-html="options.embedDescription" v-if="options.embedDescription !== undefined"></div>
                        <b-button 
                            v-if="options.cta !== undefined"
                            tag="a"
                            :href="options.cta.url"
                            @click="options.cta.scrollTo !== undefined && options.cta.callback === undefined ? moveToSecction($event, options.cta) : manageCTA($event, options.cta)"
                            :class="[options.cta.theme, options.cta.size]">
                            {{options.cta.label}}
                        </b-button>
                    </article>
                </section>
            `,
            data() {
                return {
                    heroStyle: {
                        "background-color": this.$props.options.bgColor
                    }
                }
            },
            computed: {
                
            },
            created() {
                
            },
            mounted() {
                if (this.$props.options.isVideo !== undefined && this.$props.options.isVideo) {
                    this.richSnippet(this.$props.options);    
                }
            },
            methods: {
                manageCTA(e, cta) {
                    Event.$emit('manageCTA', e, cta);
                },
                structuredData(structuredDataObj) {
                    const script = document.createElement('script');
                    
                    script.setAttribute('type', 'application/ld+json');

                    let inlineScript = document.createTextNode(JSON.stringify(structuredDataObj));

                    script.appendChild(inlineScript); 
                    document.head.appendChild(script);
                },
                richSnippet(data) {
                    if (data.videoID !== undefined) {
                        let structuredDataText = {
                            "@context": "https://schema.org",
                            "@type": "VideoObject",
                            "name": data.title,
                            "description": data.schemaDescription,
                            "thumbnailUrl": "https://i.ytimg.com/vi_webp/"+ data.videoID +"/maxresdefault.webp",
                            "embedUrl": window.location.href,
                            "uploadDate": data.uploadDate
                        };
    
                        this.structuredData(structuredDataText);    
                    }
                },
                moveToSecction(e, cta) {
                    e.preventDefault();
                    YUNOCommon.scrollToElement("#"+ cta.scrollTo +"", 1000);
                }
            }
        });
    };

    return {
        articleBlock: articleBlock
    };
})(jQuery);

