const YUNOAccordion = (function($) {
    
    const accordion = function() {
        Vue.component('yuno-accordion', {
            props: ["data", "options"],
            template: `
                <section class="yunoAccordion" :class="[options.type]">
                    <div class="container">
                        <template v-if="options.type === 'faq'">
                            <template v-if="options.hasColumn !== undefined">
                                <div class="row">
                                    <div :class="[options.hasColumn]">
                                        <div class="wrapper">
                                            <template v-if="options.fetchData !== undefined && options.fetchData">
                                                <template v-if="data.loading">
                                                    <b-collapse
                                                        class="faqCard"
                                                        animation="slide"
                                                        v-for="i in loadingResult"
                                                        :key="i"
                                                        :open="isOpen == i">
                                                        <div
                                                            slot="trigger"
                                                            slot-scope="props"
                                                            class="card-header"
                                                            :class="[props.open ? 'active' : '']"
                                                            role="button">
                                                            <p class="card-header-title">
                                                                <b-skeleton width="50%" active></b-skeleton>
                                                            </p>
                                                            <a class="card-header-icon">
                                                                <b-icon
                                                                    :icon="props.open ? 'menu-down' : 'menu-up'">
                                                                </b-icon>
                                                            </a>
                                                        </div>
                                                        <div class="card-content">
                                                            <b-skeleton width="100%" height="80px" active></b-skeleton>
                                                        </div>
                                                    </b-collapse>
                                                </template>
                                                <template v-if="data.success">
                                                    <h2 class="sectionTitle">{{ data.title }}</h2>
                                                    <b-collapse
                                                        class="faqCard"
                                                        animation="slide"
                                                        v-for="(collapse, index) of data.data"
                                                        :key="index"
                                                        :open="isOpen == index"
                                                        @open="isOpen = index">
                                                        <div
                                                            slot="trigger"
                                                            slot-scope="props"
                                                            class="card-header"
                                                            :class="[props.open ? 'active' : '']"
                                                            role="button">
                                                            <p class="card-header-title">
                                                                {{ collapse.question }}
                                                            </p>
                                                            <a class="card-header-icon">
                                                                <b-icon
                                                                    :icon="props.open ? 'menu-down' : 'menu-up'">
                                                                </b-icon>
                                                            </a>
                                                        </div>
                                                        <div class="card-content">
                                                            <div class="content" v-html="collapse.answer"></div>
                                                        </div>
                                                    </b-collapse>
                                                </template>
                                            </template>
                                            <template v-else>
                                                <h2 class="sectionTitle">{{ data.title }}</h2>
                                                <b-collapse
                                                    class="faqCard"
                                                    animation="slide"
                                                    v-for="(collapse, index) of data.list"
                                                    :key="index"
                                                    :open="isOpen == index"
                                                    @open="isOpen = index">
                                                    <div
                                                        slot="trigger"
                                                        slot-scope="props"
                                                        class="card-header"
                                                        :class="[props.open ? 'active' : '']"
                                                        role="button">
                                                        <p class="card-header-title">
                                                            {{ collapse.question }}
                                                        </p>
                                                        <a class="card-header-icon">
                                                            <b-icon
                                                                :icon="props.open ? 'menu-down' : 'menu-up'">
                                                            </b-icon>
                                                        </a>
                                                    </div>
                                                    <div class="card-content">
                                                        <div class="content" v-html="collapse.answer"></div>
                                                    </div>
                                                </b-collapse>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </template>
                    </div>
                </section>
            `,
            data() {
                return {
                    isOpen: 0,
                    loadingResult: 2,
                }
            },
            computed: {
                
            },
            async created() {
                
            },
            mounted() {
                if (this.$props.options.type === "faq" && this.$props.options.fetchData === undefined) {
                    this.faqRichSnippet(this.$props.data.list);    
                }
            },
            methods: {
                structuredData(structuredDataObj) {
                    const script = document.createElement('script');
                    
                    script.setAttribute('type', 'application/ld+json');

                    let inlineScript = document.createTextNode(JSON.stringify(structuredDataObj));

                    script.appendChild(inlineScript); 
                    document.head.appendChild(script);
                },
                faqRichSnippet(data) {
                    if (data.length !== 0) {
                        let structuredDataText = {
                            "@context": "https://schema.org",
                            "@type": "FAQPage",
                            "mainEntity": []
                        };

                        for (var i = 0; i < data.length; i++) {
                            const element = data[i];

                            let dataObj = {
                                "@type": "Question",
                                "name": element.question,
                                "acceptedAnswer": {
                                    "@type": "Answer",
                                    "text": YUNOCommon.removeTagsFromString(element.answer)
                                }
                            }

                            structuredDataText.mainEntity.push(dataObj);
                        };

                        this.structuredData(structuredDataText);    
                    };
                }
            }
        });
    };

    return {
        accordion: accordion
    };
})(jQuery);



