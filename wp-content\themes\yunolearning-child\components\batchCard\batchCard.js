const YUNOBatchCard = (function($) {
    
    const batchCard = function() {
        Vue.component('yuno-batch-card', {
            props: ["data", "type", "instructor", "startsin", "colextend", "index", "filterType", "courseStatus", "courseIndex", "isDemoPage"],
            template: `
                <article class="batchCard" :class="type" :id="type === 'instructorBatch' ? 'batch-' + data.batch_db_id  : filterType + '-' + data.batch_db_id">
                    <div class="innerWrapper">
                        <div class="row">
                            <div class="col-4 col-md-4 col-lg-3">
                                <div class="batchStart">
                                    <h3 class="date">{{data.start_date_data.date}}</h3>
                                    <p class="month">{{data.start_date_data.month}}</p>
                                    <span class="year">{{data.start_date_data.year}}</span>
                                </div>
                            </div>
                            <div class="col-8 col-md-8 noLPad" :class="colextend ? 'col-lg-9' : 'col-lg-5'">
                                <h3 class="batchTitle" v-if="type === 'demo'">
                                    <a :href="data.url">{{data.title}}</a>
                                </h3>
                                <ul class="batchInsight" :class="type">
                                    <li class="fatSize">
                                        <span class="batchLabel">Time</span>
                                        <template v-if="type === 'demo'">
                                            {{data.start_time}}
                                        </template>
                                        <template v-else>
                                            {{timeTo12(data.time)}}
                                        </template>
                                    </li>
                                    <li v-if="false"><span class="batchLabel">Starts</span>
                                        {{data.start_date}}
                                    </li>
                                    <li><span class="batchLabel">Ends</span>
                                        {{data.end_date}}
                                    </li>
                                    <li><span class="batchLabel">Duration</span>
                                        {{data.duration}}
                                    </li>
                                </ul>
                                <template v-if="type === 'demo' && userRole.data.length === 0 || type === 'demo' && userRole.data === 'Learner'">
                                    <div class="enrollCtaWrapper">
                                        <template v-if="!this.user.isLoggedin">
                                            <b-button 
                                                @click="manageLogin(data, index)"
                                                :loading="data.isLoading === true ? true : false" 
                                                class="yunoSecondaryCTA">
                                                Enroll
                                            </b-button>
                                        </template>
                                        <template v-else>
                                            <template v-if="data.is_enrolled">
                                                <b-button 
                                                    class="yunoSecondaryCTA wired">
                                                    Enrolled
                                                </b-button>
                                            </template>
                                            <template v-else>
                                                <b-button 
                                                    @click="enrollDemoClass(data, index)"
                                                    :loading="data.isLoading === true ? true : false" 
                                                    :disabled="data.isLoading === true ? true : false" 
                                                    class="yunoSecondaryCTA">
                                                    Enroll
                                                </b-button> 
                                            </template>
                                                   
                                        </template>
                                    </div>
                                    <b-modal 
                                        :active.sync="data.loginModal" 
                                        :width="350" 
                                        :can-cancel="['escape', 'x']" 
                                        @close="loginModalClose(data)"
                                        class="yunoModal">
                                            <div class="modalHeader">
                                                <h2 class="modalTitle">Login/Signup with Google</h2>
                                            </div>
                                            <div class="modalBody">
                                                <div class="wrapper">
                                                    <p>
                                                        Login or signup to get enrolled in a 
                                                        <span v-if="type === 'demo'">demo class</span>
                                                        <span v-else>batch</span>
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="modalFooter">
                                                <a :href="getSignInURL" class="yunoLoginCTA">
                                                    <span class="g_icon"></span>
                                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>
                                                </a>
                                            </div>
                                    </b-modal>
                                </template>
                            </div>
                            <template v-if="type !== 'demo'">
                                <div class="col-12 col-md-12 col-lg-4" v-if="startsin !== false">
                                    <div class="startsIn" v-if="data.start_in !== ''">
                                        <div class="starts">{{data.start_in }}</div>
                                    </div>
                                    <div class="enrollCtaWrapper forCourseDetail">
                                        <yuno-enrollment 
                                            :pageType="type"
                                            :data="data" 
                                            :batchindex="index" 
                                            :filter="filterType" 
                                            :courseStatus="courseStatus">
                                        </yuno-enrollment>
                                    </div>
                                </div>
                                <div class="col-12 offset-md-4 offset-lg-3 daysOfWeek" v-if="data.days_of_week !== undefined && data.days_of_week.length !== 0">
                                    <div class="daysOfWeekRow">
                                        <span class="label">Class days:</span> 
                                        <div class="daysRow">
                                            <b-tag :key="dayIndex" rounded v-for="(day, dayIndex) in data.days_of_week" :class="day.status ? 'active' : ''">{{ day.day }}</b-tag>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12" v-if="instructor !== false">
                                    <div class="instructorDetails">
                                        <div class="img">
                                            <a :href="data.instructor.profile_url"><img :src="data.instructor.image" :alt="data.instructor.name"></a>
                                        </div>
                                        <div class="instructorInfo"><span class="instructorLabel">Instructor</span> <span class="name"><a :href="data.instructor.profile_url">{{data.instructor.name}}</a></span></div>
                                    </div>
                                </div>
                            </template>
                            <template v-if="type === 'instructorBatch'">
                                <div class="col-12">
                                    <ul class="batchCardFooter d-flex">
                                        <li class="price">
                                            <template 
                                                v-if="data.amount === '0'  
                                                && data.subscription.length !== 0">
                                                &#8377; {{data.subscription[0].value}}
                                            </template>
                                            <template v-else>
                                                <span v-if="data.amount !== 'Free'">&#8377;</span>{{data.amount}}                                    
                                            </template>
                                        </li>
                                        <li class="cta">
                                            <yuno-enrollment
                                                v-if="userRole.data !== 'Instructor'"
                                                :pageType="type"
                                                :data="data" 
                                                :batchindex="index" 
                                                :filter="filterType" 
                                                :courseStatus="courseStatus"
                                                :courseIndex="courseIndex">
                                            </yuno-enrollment>
                                        </li>
                                    </ul>
                                </div>
                            </template>
                        </div>
                    </div>
                </article>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                ...Vuex.mapState([
                    'user',
                    'userRole',
                    'demoClassEnroll'
                    ]),
                getSignInURL() {
                    return this.$store.getters.getSignInURL
                }
            },
            created() {
                
            },
            mounted() {
                
            },
            methods: {
                daysOfWeek(str) {
                    return str.replace(/,/g, ", ")
                },
                timeTo12(timeStr) {
                    return YUNOCommon.timeConvert(timeStr);
                },
                enrollDemoClass(data, classIndex) {
                    let getModalObj = this.demoClassEnroll,
                        payload = getModalObj.payload,
                        getInstructorID = "",
                        getParentModule = "";

                    if (this.$props.isDemoPage) {
                        getInstructorID = "";
                        getParentModule = "demoClasses";
                    } else {
                        getInstructorID = instructorID;
                        getParentModule = "instructorDemoClasses";
                    }

                    data.isLoading = true;

                    payload.class_description = "";
                    payload.class_id = ""+ data.class_id +"";
                    payload.class_title = data.title;
                    payload.end_date = data.end_date;
                    payload.instructor_id = getInstructorID;
                    payload.start_date = data.start_date;
                    payload.user_id = isLoggedIn;

                    const options = {
                        apiURL: YUNOCommon.config.demoClassEnrollAPI(),
                        module: "demoClassEnroll",
                        payload: payload,
                        componentInstance: this,
                        parentModule: getParentModule,
                        classIndex: classIndex
                    };

                    this.$store.dispatch('postData', options);
                },
                loginModalClose(data) {
                    data.isLoading = false;

                    localStorage.removeItem('demoClassState');
                    localStorage.removeItem('userState');
                },
                manageLogin(data, index) {
                    data.loginModal = true;
                    data.isLoading = true;
                    data.classIndex= index;

                    localStorage.setItem('demoClassState', JSON.stringify(data));
                    localStorage.setItem('userState', window.location.pathname);
                }
            }
        });
    };

    return {
        batchCard: batchCard
    };
})(jQuery);

