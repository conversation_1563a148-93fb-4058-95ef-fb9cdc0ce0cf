@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
	.settingList {
		> li {
			margin-bottom: $gap15;
			font-size: $fontSizeLarger;
		}

		.ctaWrapper {
			margin-top: $gap15;
		}

		.settingLabel {
			@include setFontColor($primaryCopyColor, 0.5);
		}

		.settingField {

		}
	}

	.permissionList {
		margin: $gap15 0;

		.listTitle {
			font-size: $fontSizeLarger;
			font-weight: 700;
		}

		.item {
			padding: 0;

			&.setMinHeight {
				min-height: 50px;	
			}

			.smallLoader {
				font-size: 5px;
				margin: 15px;
			}

			.fa-times-circle {
				@include setFontColor($primaryCopyColor, 0.5);
			}

			.fa-check-circle {
				color: green;
			}
		}
	}
}