.dark87, #app .articleBlock {
  color: rgba(0, 0, 0, 0.87);
}

.dark60 {
  color: rgba(0, 0, 0, 0.6);
}

#app .articleBlock {
  padding: 30px 0;
}

#app .articleBlock.noTopGap {
  padding-top: 0;
}

#app .articleBlock.light {
  color: #FFF;
}

#app .articleBlock.center {
  text-align: center;
}

#app .articleBlock.btmMargin {
  margin-bottom: 30px;
}

#app .articleBlock.englishSpeaking .articleDescription, #app .articleBlock.requestSuccess .articleDescription {
  font-size: 16px;
}

#app .articleBlock.requestSuccess .articleDescription {
  margin-bottom: 15px;
}

#app .articleBlock .articleTitle {
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 15px;
  line-height: 40px;
}

#app .articleBlock .articleTitle.noBold {
  font-weight: normal;
}

#app .articleBlock .articleDescription {
  font-size: 24px;
  margin-bottom: 0;
}

#app .articleBlock .embedDescription {
  margin-top: 10px;
}

#app .articleBlock .embedDescription.btmMargin {
  margin-bottom: 30px;
}

#app .articleBlock .embedDescription h2 {
  font-size: 32px;
  margin-bottom: 15px;
  color: black;
}

#app .articleBlock .embedDescription h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: black;
}

#app .articleBlock .embedDescription h4 {
  font-size: 16px;
  margin-bottom: 15px;
  color: black;
}

#app .articleBlock .embedDescription p {
  font-size: 16px;
  margin-bottom: 30px;
  color: black;
}

#app .articleBlock .embedDescription ul, #app .articleBlock .embedDescription ol {
  padding: 0;
  margin: 0 0 15px 18px;
}

#app .articleBlock .embedDescription ul li, #app .articleBlock .embedDescription ol li {
  list-style: disc outside;
  margin-bottom: 5px;
  font-size: 16px;
  color: black;
}

#app .articleBlock .embedDescription ul li:last-child, #app .articleBlock .embedDescription ol li:last-child {
  margin-bottom: 0;
}

#app .articleBlock .embedDescription ol li {
  list-style: decimal outside;
}

#app .articleBlock .videoLPPlayer {
  position: relative;
  padding-bottom: 37.7%;
  overflow: hidden;
  max-width: 100%;
  min-height: 250px;
  margin: 30px auto;
}

@media (min-width: 768px) {
  #app .articleBlock .videoLPPlayer {
    min-height: 300px;
    margin-top: 0;
    max-width: 70%;
  }
}

#app .articleBlock .videoLPPlayer iframe,
#app .articleBlock .videoLPPlayer object,
#app .articleBlock .videoLPPlayer embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#app .articleBlock.hasVideo .articleTitle {
  margin-bottom: 30px;
}

#app .articleBlock.hasVideo .embedDescription {
  text-align: left;
}

@media (min-width: 768px) {
  #app .articleBlock {
    padding: 60px 0;
  }
  #app .articleBlock.btmMargin {
    margin-bottom: 60px;
  }
}
/*# sourceMappingURL=articleBlock.css.map */