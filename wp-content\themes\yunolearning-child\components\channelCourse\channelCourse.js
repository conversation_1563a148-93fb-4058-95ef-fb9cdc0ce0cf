const YUNOChannelCourse = (function($) {
    const channelCourse = function() {
        Vue.component('yuno-channel-course', {
            props: ["data", "options"],
            template: `
                <section id="yunoChannelCourse" class="yunoChannelCourse">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 col-md-5 col-lg-5">
                                <h1 class="sectionTitle"><a :href="'/course/' + data.product_code">{{data.title}}</a></h1>
                                <h2 class="sectionSubTitle">
                                    <span v-if="data.sub_title">
                                        {{data.sub_title}}
                                    </span>
                                    <span class="subEle" :class="{'noSubtitle': data.sub_title === ''}">{{data.dur_in_weeks}}</span>
                                </h2>
                                <ul class="sectionMeta d-flex">
                                    <li class="price">
                                        <template 
                                            v-if="data.price === 0 
                                            && data.subscription.length !== 0">
                                            &#8377; {{data.subscription[0].value}}
                                        </template>
                                        <template v-else>
                                            <span v-if="data.price !== 'Free'">&#8377;</span>{{data.price}}                                    
                                        </template>
                                    </li>
                                </ul>
                                <a v-if="false" class="primaryColor fsLarger" :href="options.detailPageURL">Day-by day Schedule</a>
                            </div>
                            <div class="col-12 col-md-7 col-lg-7">
                                <div class="sectionMedia">
                                    <template v-if="data.media.type === 'Image'">
                                        <a :href="'/course/' + data.product_code">
                                            <img 
                                                :src="data.media.url" 
                                                :alt="data.title">        
                                        </a>
                                    </template>
                                    <template v-else>
                                        <iframe 
                                            class="yunoYoutube" 
                                            :src="data.media.url" 
                                            frameborder="0" 
                                            allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" 
                                            allowfullscreen>
                                        </iframe>
                                    </template>
                                </div>
                            </div>
                        </div>  
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                
            },
            async created() {
                
            },
            mounted() {
                
            },
            methods: {
                
            }
        });
    };

    return {
        channelCourse: channelCourse
    };
})(jQuery);



