#app .yunoChannelCourse {
  padding: 30px 0;
}

#app .yunoChannelCourse a.primaryColor {
  text-decoration: underline;
}

#app .yunoChannelCourse .sectionTitle {
  font-size: 32px;
  font-weight: 600;
}

#app .yunoChannelCourse .sectionTitle a {
  color: #000;
}

@media (min-width: 768px) {
  #app .yunoChannelCourse .sectionTitle {
    font-size: 48px;
  }
}

#app .yunoChannelCourse .chooseOpt {
  margin-top: 15px;
}

#app .yunoChannelCourse .sectionSubTitle {
  font-size: 18px;
  font-weight: 400;
  margin: 0;
}

#app .yunoChannelCourse .sectionSubTitle .subEle {
  display: inline-block;
}

#app .yunoChannelCourse .sectionSubTitle .subEle:before {
  content: "|";
  color: rgba(0, 0, 0, 0.1);
  margin: 0 10px 0 10px;
}

#app .yunoChannelCourse .sectionSubTitle .subEle.noSubtitle:before {
  display: none;
}

#app .yunoChannelCourse .sectionMeta {
  margin: 16px 0 30px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .yunoChannelCourse .sectionMeta li {
  margin-right: 16px;
}

#app .yunoChannelCourse .sectionMeta .price {
  font-size: 32px;
}

#app .yunoChannelCourse .sectionMeta .price, #app .yunoChannelCourse .sectionMeta .ctaWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  margin: 0 16px 0 0;
}

#app .yunoChannelCourse .sectionMedia img {
  width: 100%;
  height: auto;
}

#app .yunoChannelCourse .sectionMedia .yunoYoutube {
  width: 100%;
  height: 357px;
}

#app .yunoChannelCourse .sectionDescription {
  margin-top: 30px;
}

#app .yunoChannelCourse .sectionDescription .descriptionTitle {
  font-weight: 400;
  font-size: 24px;
  margin-bottom: 16px;
}

#app .yunoChannelCourse .sectionDescription p {
  font-size: 16px;
  margin-bottom: 0;
}
/*# sourceMappingURL=channelCourse.css.map */