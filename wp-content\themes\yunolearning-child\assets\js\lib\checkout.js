!function(){"use strict";!function(){var s=window,c=s.document,t=s.Array,l=s.Object,r=s.String,m=s.Number,u=s.Date,d=s.Math,a=s.setTimeout,n=s.setInterval,i=s.clearTimeout,f=s.parseInt,h=s.encodeURIComponent,v=s.btoa,_=s.unescape,p=s.TypeError,y=s.navigator,b=s.location,e=s.XMLHttpRequest,o=s.FormData;function g(i){return function(e,n){return arguments.length<2?function(n){return i.call(null,n,e)}:i.call(null,e,n)}}function D(t){return function(e,i,n){return arguments.length<3?function(n){return t.call(null,n,e,i)}:t.call(null,e,i,n)}}function S(){for(var n=arguments.length,e=new t(n),i=0;i<n;i++)e[i]=arguments[i];return function(n){return function(){var i=arguments;return e.every(function(n,e){return n(i[e])||function(){console.error.apply(console,arguments)}("wrong "+e+"th argtype",i[e])})?n.apply(null,i):i[0]}}}function R(n){return B(n)&&1===n.nodeType}var M=g(function(n,e){return typeof n===e}),k=M("boolean"),w=M("number"),P=M("string"),K=M("function"),N=M("object"),L=t.isArray,B=(M("undefined"),function(n){return!(null===n)&&N(n)}),A=function(n){return!C(l.keys(n))},T=g(function(n,e){return n&&n[e]}),C=T("length"),E=T("prototype"),x=g(function(n,e){return n instanceof e}),G=u.now,z=d.random,O=d.floor;function F(n,e){return{error:(i=e,t={description:r(n)},i&&(t.field=i),t)};var i,t}function $(n){throw new Error(n)}var H=function(n){return/data:image\/[^;]+;base64/.test(n)};function I(n){var e=function a(o,r){var m={};if(!B(o))return m;var u=null==r;return l.keys(o).forEach(function(n){var e,i=o[n],t=u?n:r+"["+n+"]";"object"==typeof i?(e=a(i,t),l.keys(e).forEach(function(n){m[n]=e[n]})):m[t]=i}),m}(n);return l.keys(e).map(function(n){return h(n)+"="+h(e[n])}).join("&")}function U(n,e){return B(e)&&(e=I(e)),e&&(n+=0<n.indexOf("?")?"&":"?",n+=e),n}function Z(n){return l.keys(n||{})}function Y(n){return Kn(Pn(n))}function j(e,t,a,o){return x(e,Ln)?console.error("use el |> _El.on(e, cb)"):function(i){var n=t;return P(a)?n=function(n){for(var e=n.target;!Qn(e,a)&&e!==i;)e=An(e);e!==i&&(n.delegateTarget=e,t(n))}:o=a,o=!!o,i.addEventListener(e,n,o),function(){return i.removeEventListener(e,n,o)}}}function W(n){return P(n)?re(n):n}var V,q,J,Q,X,nn,en,tn,an,on,rn,mn,un,cn=E(t),ln=cn.slice,sn=g(function(n,e){return n&&cn.forEach.call(n,e),n}),dn=(V="indexOf",g(function(n,e){return cn[V].call(n,e)})),fn=g(function(n,e){return 0<=dn(n,e)}),hn=g(function(n,e){return ln.call(n,e)}),vn=D(function(n,e,i){return cn.reduce.call(n,e,i)}),_n=function(n){return n},pn=(E(Function),J=function(n,e){return n.bind(e)},q=function(n){if(K(n))return J.apply(null,arguments);throw new p("not a function")},g(function(n,e){var i=arguments;return P(n)&&((i=hn(i,0))[0]=e[n]),q.apply(null,i)})),yn=E(r).slice,bn=D(function(n,e,i){return yn.call(n,e,i)}),gn=g(function(n,e){return yn.call(n,e)}),Dn=g(function(n,e){return e in n}),Sn=g(function(n,e){return n&&n.hasOwnProperty(e)}),Rn=D(function(n,e,i){return n[e]=i,n}),Mn=D(function(n,e,i){return i&&(n[e]=i),n}),kn=g(function(n,e){return delete n[e],n}),wn=g(function(e,i){return sn(Z(e),function(n){return i(e[n],n,e)}),e}),Pn=JSON.stringify,Kn=function(n){try{return JSON.parse(n)}catch(n){}},Nn=g(function(i,n){return wn(n,function(n,e){return i[e]=n}),i}),Ln=s.Element,Bn=function(n){return c.createElement(n||"div")},An=function(n){return n.parentNode},Tn=S(R),Cn=S(R,R),En=S(R,P),xn=S(R,P,function(){return!0}),Gn=S(R,B),zn=(Q=Cn(function(n,e){return e.appendChild(n)}),g(Q)),On=(X=Cn(function(n,e){var i=e;return zn(n)(i),n}),g(X)),Fn=Tn(function(n){var e=An(n);return e&&e.removeChild(n),n}),$n=(Tn(T("selectionStart")),Tn(T("selectionEnd")),en=function(n,e){return n.selectionStart=n.selectionEnd=e,n},nn=S(R,w)(en),g(nn),Tn(function(n){return n.submit(),n})),Hn=D(xn(function(n,e,i){return n.setAttribute(e,i),n})),In=D(xn(function(n,e,i){return n.style[e]=i,n})),Un=(tn=Gn(function(t,n){var e=n;return wn(function(n,e){var i=t;return Hn(e,n)(i)})(e),t}),g(tn)),Zn=(an=Gn(function(t,n){var e=n;return wn(function(n,e){var i=t;return In(e,n)(i)})(e),t}),g(an)),Yn=(on=En(function(n,e){return n.innerHTML=e,n}),g(on)),jn=(rn=En(function(n,e){var i=n;return In("display",e)(i)}),g(rn)),Wn=(jn("none"),jn("block"),jn("inline-block"),T("offsetWidth")),Vn=T("offsetHeight"),qn=E(Ln),Jn=qn.matches||qn.matchesSelector||qn.webkitMatchesSelector||qn.mozMatchesSelector||qn.msMatchesSelector||qn.oMatchesSelector,Qn=(mn=En(function(n,e){return Jn.call(n,e)}),g(mn)),Xn=c.documentElement,ne=c.body,ee=s.innerHeight,ie=s.pageYOffset,te=s.scrollBy,ae=s.scrollTo,oe=s.requestAnimationFrame,re=pn("querySelector",c),me=pn("querySelectorAll",c);pn("getElementById",c),pn("getComputedStyle",s);function ue(n,e,i,t){var a,o,r,m,u,c;i&&"get"===i.toLowerCase()?(n=U(n,e),t?s.open(n,t):s.location=n):(c={action:n,method:i},t&&(c.target=t),u=Bn("form"),m=Un(c)(u),r=Yn(ce(e))(m),o=zn(Xn)(r),a=$n(o),Fn(a))}function ce(n,i){if(B(n)){var t="";return wn(n,function(n,e){i&&(e=i+"["+e+"]"),t+=ce(n,e)}),t}var e=Bn("input");return e.type="hidden",e.value=n,e.name=i,e.outerHTML}function le(n){!function(m){if(!s.requestAnimationFrame)return te(0,m);un&&i(un);un=a(function(){var t=ie,a=d.min(t+m,Vn(ne)-ee);m=a-t;var o=0,r=s.performance.now();oe(function n(e){if(1<=(o+=(e-r)/300))return ae(0,a);var i=d.sin(se*o/2);ae(0,t+d.round(m*i)),r=e,oe(n)})},100)}(n-ie)}var se=d.PI;var de,fe,he,ve,_e=e,pe=F("Network error"),ye=0;function be(n){if(!x(this,be))return new be(n);this.options=function(n){P(n)&&(n={url:n});var e=n.method,i=n.headers,t=n.callback,a=n.data;i||(n.headers={});e||(n.method="get");t||(n.callback=_n);B(a)&&!x(a,o)&&(a=I(a));return n.data=a,n}(n),this.defer()}((he={setReq:function(n,e){return this.abort(),this.type=n,this.req=e,this},till:function(e,i){var t=this;return void 0===i&&(i=0),this.setReq("timeout",a(function(){t.call(function(n){n.error&&0<i?t.till(e,i-1):e(n)?t.till(e,i):t.options.callback(n)})},3e3))},abort:function(){var n=this.req,e=this.type;n&&("ajax"===e?this.req.abort():"jsonp"===e?s.Razorpay[this.req]=_n:i(this.req),this.req=null)},defer:function(){var n=this;this.req=a(function(){return n.call()})},call:function(e){var n,i,t;void 0===e&&(e=this.options.callback);var a=this.options,o=a.url,r=a.method,m=a.data,u=a.headers,c=new _e;this.setReq("ajax",c),c.open(r,o,!0),c.onreadystatechange=function(){var n;4===c.readyState&&c.status&&((n=Kn(c.responseText))||((n=F("Parsing error")).xhr={status:c.status,text:c.responseText}),e(n))},c.onerror=function(){var n=pe;n.xhr={status:0},e(n)},t=u,i=Mn("X-Razorpay-SessionId",de)(t),n=Mn("X-Razorpay-TrackId",fe)(i),wn(function(n,e){return c.setRequestHeader(e,n)})(n),c.send(m)}}).constructor=be).prototype=he,be.post=function(n){return n.method="post",n.headers||(n.headers={}),n.headers["Content-type"]||(n.headers["Content-type"]="application/x-www-form-urlencoded"),be(n)},be.setSessionId=function(n){de=n},be.setTrackId=function(n){fe=n},be.jsonp=function(u){u.data||(u.data={});var c=ye++,l=0,n=new be(u);return u=n.options,n.call=function(e){void 0===e&&(e=u.callback);function n(){t||this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||(t=!0,this.onload=this.onreadystatechange=null,Fn(this))}var i="jsonp"+c+"_"+ ++l,t=!1,a=s.Razorpay[i]=function(n){kn(n,"http_status_code"),e(n),kn(s.Razorpay,i)};this.setReq("jsonp",a);var o=U(u.url,u.data),o=U(o,I({callback:"Razorpay."+i})),r=Bn("script"),m=Nn({src:o,async:!0,onerror:function(){return e(pe)},onload:n,onreadystatechange:n})(r);zn(Xn)(m)},n};var ge=function(n){return console.warn("Promise error:",n)},De=function(n){return x(n,Se)};function Se(n){if(!De(this))throw"new Promise";this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],Ke(n,this)}function Re(i,t){for(;3===i._state;)i=i._value;0!==i._state?(i._handled=!0,a(function(){var n,e=1===i._state?t.onFulfilled:t.onRejected;if(null!==e){try{n=e(i._value)}catch(n){return void ke(t.promise,n)}Me(t.promise,n)}else(1===i._state?Me:ke)(t.promise,i._value)})):i._deferreds.push(t)}function Me(e,n){try{if(n===e)throw new p("promise resolved by itself");if(B(n)||K(n)){var i=n.then;if(De(n))return e._state=3,e._value=n,void we(e);if(K(i))return void Ke(pn(i,n),e)}e._state=1,e._value=n,we(e)}catch(n){ke(e,n)}}function ke(n,e){n._state=2,n._value=e,we(n)}function we(e){var n;2===e._state&&0===e._deferreds.length&&a(function(){e._handled||ge(e._value)}),n=e._deferreds,sn(function(n){return Re(e,n)})(n),e._deferreds=null}function Pe(n,e,i){this.onFulfilled=K(n)?n:null,this.onRejected=K(e)?e:null,this.promise=i}function Ke(n,e){var i=!1;try{n(function(n){i||(i=!0,Me(e,n))},function(n){i||(i=!0,ke(e,n))})}catch(n){if(i)return;i=!0,ke(e,n)}}ve=Se.prototype,Nn({catch:function(n){return this.then(null,n)},then:function(n,e){var i=new Se(_n);return Re(this,new Pe(n,e,i)),i},finally:function(e){return this.then(function(n){return Se.resolve(e()).then(function(){return n})},function(n){return Se.resolve(e()).then(function(){return Se.reject(n)})})}})(ve),Se.all=function(r){return new Se(function(t,a){if(!r||void 0===r.length)throw new p("Promise.all accepts an array");if(0===r.length)return t([]);var o=r.length,n=r;sn(function e(n,i){try{if((B(n)||K(n))&&K(n.then))return n.then(function(n){return e(n,i)},a);r[i]=n,0==--o&&t(r)}catch(n){a(n)}})(n)})},Se.resolve=function(e){return De(e)?e:new Se(function(n){return n(e)})},Se.reject=function(i){return new Se(function(n,e){return e(i)})},Se.race=function(t){return new Se(function(e,i){var n=t;return sn(function(n){return n.then(e,i)})(n)})};var Ne=s.Promise,Le=Ne&&K(E(Ne).then)&&Ne||Se;K(Le.prototype.finally)||(Le.prototype.finally=Se.prototype.finally);var Be={_storage:{},setItem:function(n,e){this._storage[n]=e},getItem:function(n){return this._storage[n]||null},removeItem:function(n){delete this._storage[n]}};var Ae,Te=function(){var n=G();try{s.localStorage.setItem("_storage",n);var e=s.localStorage.getItem("_storage");return s.localStorage.removeItem("_storage"),n!==f(e)?Be:s.localStorage}catch(n){return Be}}(),Ce="rzp_checkout_exp";var Ee="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",xe=(Ae=Ee,vn(function(n,e,i){return Rn(n,e,i)},{})(Ae));function Ge(n){for(var e="";n;)e=Ee[n%62]+e,n=O(n/62);return e}function ze(){var i,t=Ge(r(G()-13885344e5)+gn("000000"+O(1e6*z()),-6))+Ge(O(238328*z()))+"0",a=0,n=t;return sn(function(n,e){i=xe[t[t.length-1-e]],(t.length-e)%2&&(i*=2),62<=i&&(i=i%62+1),a+=i})(n),i=(i=a%62)&&Ee[62-i],bn(t,0,13)+i}var Oe=ze(),Fe={library:"checkoutjs",platform:"browser",referer:b.href};function $e(n){var i={checkout_id:n?n.id:Oe},e=["device","env","integration","library","os_version","os","platform_version","platform","referer"];return sn(function(n){var e=i;return Mn(n,Fe[n])(e)})(e),i}var He,Ie=[],Ue=[],Ze=function(n){return Ie.push(n)},Ye=function(n){He=n},je=function(){var n,e,i,t;if(Ie.length){var a=Dn(y,"sendBeacon"),o={context:He,addons:[{name:"ua_parser",input_key:"user_agent",output_key:"user_agent_parsed"}],events:Ie.splice(0,Ie.length)},r={url:"https://lumberjack.razorpay.com/v1/track",data:{key:"ZmY5N2M0YzVkN2JiYzkyMWM1ZmVmYWJk",data:(t=Pn(o),i=h(t),e=_(i),n=v(e),h(n))}};try{a?y.sendBeacon(r.url,Pn(r.data)):be.post(r)}catch(n){}}};n(function(){je()},1e3);function We(m,u,c,l){m?m.isLiveMode()&&a(function(){var n;c instanceof Error&&(c={message:c.message,stack:c.stack});var e=$e(m);e.user_agent=null,e.mode="live";var i=m.get("order_id");i&&(e.order_id=i);var a={},t={options:a};c&&(t.data=c);var o=["amount","callback_url","checkout_config_id","contact_id","currency","description","display_amount","display_currency","ecod","hidden","image","key","method","name","prefill","readonly","recurring","redirect","theme"];wn(m.get(),function(n,e){var i=e.split("."),t=i[0];-1!==o.indexOf(t)&&(1<i.length?(a.hasOwnProperty(t)||(a[t]={}),a[t][i[1]]=n):a[e]=n)}),Dn(a,"prefill")&&sn(["card[number]","card[cvv]","card[expiry]"],function(n){Dn(a.prefill,n)&&(a.prefill[n]=!0)}),a.image&&H(a.image)&&(a.image="base64");var r=m.get("external.wallets")||[];a.external_wallets=(n=r,vn(function(n,e){var i=n;return Rn(e,!0)(i)},{})(n)),Oe&&(t.local_order_id=Oe),t.build_number=10125,t.experiments=function(){try{var n=Te.getItem(Ce),e=Kn(n)}catch(n){}return B(e)&&!L(e)?e:{}}(),Ze({event:u,properties:t,timestamp:G()}),Ye(e),l&&je()}):Ue.push([u,c,l])}We.dispatchPendingEvents=function(n){var e;n&&(e=We.bind(We,n),Ue.splice(0,Ue.length).forEach(function(n){e.apply(We,n)}))},We.parseAnalyticsData=function(n){var e;B(n)&&(e=n,wn(function(n,e){Fe[n]=e})(e))},We.makeUid=ze,We.common=$e,We.props=Fe,We.id=Oe,We.updateUid=function(n){We.id=Oe=n},We.flush=je;function Ve(n){var i=function t(n,a){void 0===a&&(a="");var o={};return wn(n,function(n,e){var i=a?a+"."+e:e;B(n)?Nn(o,t(n,i)):o[i]=n}),o}(n);return wn(i,function(n,e){K(n)&&(i[e]=n.call())}),i}var qe,Je={},Qe={setR:function(n){We.dispatchPendingEvents(qe=n)},track:function(n,e){var i,t=void 0===e?{}:e,a=t.type,o=t.data,r=void 0===o?{}:o,m=t.r,u=void 0===m?qe:m,c=t.immediately,l=void 0!==c&&c,s=Ve(Je);i=Y(r||{}),["token"].forEach(function(n){i[n]&&(i[n]="__REDACTED__")}),(r=B(r=i)?Y(r):{data:r}).meta&&B(r.meta)&&(s=Nn(s,r.meta)),r.meta=s,a&&(n=a+":"+n),We(u,n,r,l)},setMeta:function(n,e){Rn(Je,n,e)},removeMeta:function(n){kn(Je,n)},getMeta:function(){return e={},wn(Je,function(i,n){var t=(n=n.replace(/\[([^[\]]+)\]/g,".$1")).split("."),a=e;sn(t,function(n,e){e<t.length-1?(a[n]||(a[n]={}),a=a[n]):a[n]=i})}),e;var e}};function Xe(){return this._evts={},this._defs={},this}Xe.prototype={onNew:_n,def:function(n,e){this._defs[n]=e},on:function(n,e){var i;return P(n)&&K(e)&&((i=this._evts)[n]||(i[n]=[]),!1!==this.onNew(n,e)&&i[n].push(e)),this},once:function(e,n){var i=n,t=this;return n=function n(){i.apply(t,arguments),t.off(e,n)},this.on(e,n)},off:function(i,n){var e=arguments.length;if(!e)return Xe.call(this);var t=this._evts;if(2===e){var a=t[i];if(!K(n)||!L(a))return;if(a.splice(dn(a,n),1),a.length)return}return t[i]?delete t[i]:(i+=".",wn(t,function(n,e){e.indexOf(i)||delete t[e]})),this},emit:function(n,e){var i=this;return sn(this._evts[n],function(n){try{n.call(i,e)}catch(n){console.error}}),this},emitter:function(){var n=arguments,e=this;return function(){e.emit.apply(e,n)}}};var ni=y.userAgent,ei=y.vendor;function ii(n){return n.test(ni)}function ti(n){return n.test(ei)}ii(/MSIE |Trident\//);var ai=ii(/iPhone/),oi=ai||ii(/iPad/),ri=(ii(/Android/),ii(/iPad/),ii(/Windows NT/),ii(/Linux/),ii(/Mac OS/),ii(/^((?!chrome|android).)*safari/i)||ti(/Apple/),ii(/firefox/),ii(/Chrome/)&&ti(/Google Inc/),ii(/; wv\) |Gecko\) Version\/[^ ]+ Chrome/),ii(/Instagram/)),mi=ii(/FB_IAB\/FB4A/),ui=ii(/FBAN\/FBIOS/),ci=mi||ui;var li=ii(/; wv\) |Gecko\) Version\/[^ ]+ Chrome|Windows Phone|Opera Mini|UCBrowser|CriOS/)||ci||ri||oi||ii(/Android 4/),si=(ii(/iPhone/),(si=ni.match(/Chrome\/(\d+)/))&&f(si[1],10)),di=(ii(/(Vivo|HeyTap|Realme|Oppo)Browser/),{key:"",account_id:"",image:"",amount:100,currency:"INR",order_id:"",invoice_id:"",subscription_id:"",auth_link_id:"",payment_link_id:"",notes:null,callback_url:"",redirect:!1,description:"",customer_id:"",recurring:null,payout:null,contact_id:"",signature:"",retry:!0,target:"",subscription_card_change:null,display_currency:"",display_amount:"",recurring_token:{max_amount:0,expire_by:0},checkout_config_id:"",send_sms_hash:!1});function fi(n,e,i,t){var a=e[i=i.toLowerCase()],o=typeof a;"object"==o&&null===a?P(t)&&("true"===t||"1"===t?t=!0:"false"!==t&&"0"!==t||(t=!1)):"string"==o&&(w(t)||k(t))?t=r(t):"number"==o?t=m(t):"boolean"==o&&(P(t)?"true"===t||"1"===t?t=!0:"false"!==t&&"0"!==t||(t=!1):w(t)&&(t=!!t)),null!==a&&o!=typeof t||(n[i]=t)}function hi(t,a,o){wn(t[a],function(n,e){var i=typeof n;"string"!=i&&"number"!=i&&"boolean"!=i||(e=a+o[0]+e,1<o.length&&(e+=o[1]),t[e]=n)}),delete t[a]}function vi(n,t){var a={};return wn(n,function(n,i){i in _i?wn(n,function(n,e){fi(a,t,i+"."+e,n)}):fi(a,t,i,n)}),a}var _i={};function pi(i){wn(di,function(n,i){B(n)&&!A(n)&&(_i[i]=!0,wn(n,function(n,e){di[i+"."+e]=n}),delete di[i])}),(i=vi(i,di)).callback_url&&li&&(i.redirect=!0),this.get=function(n){return arguments.length?n in i?i[n]:di[n]:i},this.set=function(n,e){i[n]=e},this.unset=function(n){delete i[n]}}var yi,bi,gi,Di="",Si=s.screen;try{gi=[y.userAgent,y.language,(new u).getTimezoneOffset(),y.platform,y.cpuClass,y.hardwareConcurrency,Si.colorDepth,y.deviceMemory,Si.width+Si.height,Si.width*Si.height,s.devicePixelRatio],yi=gi.join(),bi=new s.TextEncoder("utf-8").encode(yi),Di=void s.crypto.subtle.digest("SHA-1",bi).then(function(n){return Di=function(n){for(var e=[],i=new s.DataView(n),t=0;t<i.byteLength;t+=4){var a=i.getUint32(t).toString(16),o="********",r=(o+a).slice(-o.length);e.push(r)}return e.join("")}(n)})}catch(n){}var Ri={api:"https://api.razorpay.com/",version:"v1/",frameApi:"/",cdn:"https://cdn.razorpay.com/"};try{Nn(Ri,s.Razorpay.config)}catch(n){}function Mi(n,i,e){var t;void 0===e&&(e={});var a=Y(n);e.feesRedirect&&(a.view="html");var o=i.get;sn(["amount","currency","signature","description","order_id","account_id","notes","subscription_id","auth_link_id","payment_link_id","customer_id","recurring","subscription_card_change","recurring_token.max_amount","recurring_token.expire_by"],function(n){var e,i=a;Sn(n)(i)||(e=o(n))&&(k(e)&&(e=1),a[n.replace(/\.(\w+)/g,"[$1]")]=e)});var r=o("key");!a.key_id&&r&&(a.key_id=r),e.avoidPopup&&"wallet"===a.method&&(a["_[source]"]="checkoutjs"),(e.tez||e.gpay)&&(a["_[flow]"]="intent",a["_[app]"]="com.google.android.apps.nbu.paisa.user"),sn(["integration","integration_version","integration_parent_version"],function(n){var e=i.get("_."+n);e&&(a["_["+n+"]"]=e)}),Di&&(a["_[shield][fhash]"]=Di),a["_[shield][tz]"]=-(new u).getTimezoneOffset(),t=Li,wn(function(n,e){a["_[shield]["+e+"]"]=n})(t),a["_[build]"]=10125,hi(a,"notes","[]"),hi(a,"card","[]");var m=a["card[expiry]"];return P(m)&&(a["card[expiry_month]"]=m.slice(0,2),a["card[expiry_year]"]=m.slice(-2),delete a["card[expiry]"]),a._=We.common(),hi(a,"_","[]"),a}function ki(t,a){return void 0===a&&(a="."),function(n){for(var e=a,i=0;i<t;i++)e+="0";return n.replace(e,"")}}function wi(n,e){return void 0===e&&(e=","),n.replace(/\./,e)}function Pi(a){wn(a,function(n,e){var i,t;Ti[e]=(t={},i=Nn(Ti.default)(t),Nn(Ti[e]||{})(i)),Ti[e].code=e,a[e]&&(Ti[e].symbol=a[e])})}var Ki,Ni,Li={},Bi={AED:{code:"784",denomination:100,min_value:10,min_auth_value:100,symbol:"د.إ",name:"Emirati Dirham"},ALL:{code:"008",denomination:100,min_value:221,min_auth_value:100,symbol:"Lek",name:"Albanian Lek"},AMD:{code:"051",denomination:100,min_value:975,min_auth_value:100,symbol:"֏",name:"Armenian Dram"},ARS:{code:"032",denomination:100,min_value:80,min_auth_value:100,symbol:"ARS",name:"Argentine Peso"},AUD:{code:"036",denomination:100,min_value:50,min_auth_value:100,symbol:"A$",name:"Australian Dollar"},AWG:{code:"533",denomination:100,min_value:10,min_auth_value:100,symbol:"Afl.",name:"Aruban or Dutch Guilder"},BBD:{code:"052",denomination:100,min_value:10,min_auth_value:100,symbol:"Bds$",name:"Barbadian or Bajan Dollar"},BDT:{code:"050",denomination:100,min_value:168,min_auth_value:100,symbol:"৳",name:"Bangladeshi Taka"},BMD:{code:"060",denomination:100,min_value:10,min_auth_value:100,symbol:"$",name:"Bermudian Dollar"},BND:{code:"096",denomination:100,min_value:10,min_auth_value:100,symbol:"BND",name:"Bruneian Dollar"},BOB:{code:"068",denomination:100,min_value:14,min_auth_value:100,symbol:"Bs",name:"Bolivian Bolíviano"},BSD:{code:"044",denomination:100,min_value:10,min_auth_value:100,symbol:"BSD",name:"Bahamian Dollar"},BWP:{code:"072",denomination:100,min_value:22,min_auth_value:100,symbol:"P",name:"Botswana Pula"},BZD:{code:"084",denomination:100,min_value:10,min_auth_value:100,symbol:"BZ$",name:"Belizean Dollar"},CAD:{code:"124",denomination:100,min_value:50,min_auth_value:100,symbol:"C$",name:"Canadian Dollar"},CHF:{code:"756",denomination:100,min_value:50,min_auth_value:100,symbol:"CHf",name:"Swiss Franc"},CNY:{code:"156",denomination:100,min_value:14,min_auth_value:100,symbol:"¥",name:"Chinese Yuan Renminbi"},COP:{code:"170",denomination:100,min_value:1e3,min_auth_value:100,symbol:"COL$",name:"Colombian Peso"},CRC:{code:"188",denomination:100,min_value:1e3,min_auth_value:100,symbol:"₡",name:"Costa Rican Colon"},CUP:{code:"192",denomination:100,min_value:53,min_auth_value:100,symbol:"$MN",name:"Cuban Peso"},CZK:{code:"203",denomination:100,min_value:46,min_auth_value:100,symbol:"Kč",name:"Czech Koruna"},DKK:{code:"208",denomination:100,min_value:250,min_auth_value:100,symbol:"DKK",name:"Danish Krone"},DOP:{code:"214",denomination:100,min_value:102,min_auth_value:100,symbol:"RD$",name:"Dominican Peso"},DZD:{code:"012",denomination:100,min_value:239,min_auth_value:100,symbol:"د.ج",name:"Algerian Dinar"},EGP:{code:"818",denomination:100,min_value:35,min_auth_value:100,symbol:"E£",name:"Egyptian Pound"},ETB:{code:"230",denomination:100,min_value:57,min_auth_value:100,symbol:"ብር",name:"Ethiopian Birr"},EUR:{code:"978",denomination:100,min_value:50,min_auth_value:100,symbol:"€",name:"Euro"},FJD:{code:"242",denomination:100,min_value:10,min_auth_value:100,symbol:"FJ$",name:"Fijian Dollar"},GBP:{code:"826",denomination:100,min_value:30,min_auth_value:100,symbol:"£",name:"British Pound"},GIP:{code:"292",denomination:100,min_value:10,min_auth_value:100,symbol:"GIP",name:"Gibraltar Pound"},GMD:{code:"270",denomination:100,min_value:100,min_auth_value:100,symbol:"D",name:"Gambian Dalasi"},GTQ:{code:"320",denomination:100,min_value:16,min_auth_value:100,symbol:"Q",name:"Guatemalan Quetzal"},GYD:{code:"328",denomination:100,min_value:418,min_auth_value:100,symbol:"G$",name:"Guyanese Dollar"},HKD:{code:"344",denomination:100,min_value:400,min_auth_value:100,symbol:"HK$",name:"Hong Kong Dollar"},HNL:{code:"340",denomination:100,min_value:49,min_auth_value:100,symbol:"HNL",name:"Honduran Lempira"},HRK:{code:"191",denomination:100,min_value:14,min_auth_value:100,symbol:"kn",name:"Croatian Kuna"},HTG:{code:"332",denomination:100,min_value:167,min_auth_value:100,symbol:"G",name:"Haitian Gourde"},HUF:{code:"348",denomination:100,min_value:555,min_auth_value:100,symbol:"Ft",name:"Hungarian Forint"},IDR:{code:"360",denomination:100,min_value:1e3,min_auth_value:100,symbol:"Rp",name:"Indonesian Rupiah"},ILS:{code:"376",denomination:100,min_value:10,min_auth_value:100,symbol:"₪",name:"Israeli Shekel"},INR:{code:"356",denomination:100,min_value:100,min_auth_value:100,symbol:"₹",name:"Indian Rupee"},JMD:{code:"388",denomination:100,min_value:250,min_auth_value:100,symbol:"J$",name:"Jamaican Dollar"},KES:{code:"404",denomination:100,min_value:201,min_auth_value:100,symbol:"Ksh",name:"Kenyan Shilling"},KGS:{code:"417",denomination:100,min_value:140,min_auth_value:100,symbol:"Лв",name:"Kyrgyzstani Som"},KHR:{code:"116",denomination:100,min_value:1e3,min_auth_value:100,symbol:"៛",name:"Cambodian Riel"},KYD:{code:"136",denomination:100,min_value:10,min_auth_value:100,symbol:"CI$",name:"Caymanian Dollar"},KZT:{code:"398",denomination:100,min_value:759,min_auth_value:100,symbol:"₸",name:"Kazakhstani Tenge"},LAK:{code:"418",denomination:100,min_value:1e3,min_auth_value:100,symbol:"₭",name:"Lao Kip"},LBP:{code:"422",denomination:100,min_value:1e3,min_auth_value:100,symbol:"&#1604;.&#1604;.",name:"Lebanese Pound"},LKR:{code:"144",denomination:100,min_value:358,min_auth_value:100,symbol:"රු",name:"Sri Lankan Rupee"},LRD:{code:"430",denomination:100,min_value:325,min_auth_value:100,symbol:"L$",name:"Liberian Dollar"},LSL:{code:"426",denomination:100,min_value:29,min_auth_value:100,symbol:"LSL",name:"Basotho Loti"},MAD:{code:"504",denomination:100,min_value:20,min_auth_value:100,symbol:"د.م.",name:"Moroccan Dirham"},MDL:{code:"498",denomination:100,min_value:35,min_auth_value:100,symbol:"MDL",name:"Moldovan Leu"},MKD:{code:"807",denomination:100,min_value:109,min_auth_value:100,symbol:"ден",name:"Macedonian Denar"},MMK:{code:"104",denomination:100,min_value:1e3,min_auth_value:100,symbol:"MMK",name:"Burmese Kyat"},MNT:{code:"496",denomination:100,min_value:1e3,min_auth_value:100,symbol:"₮",name:"Mongolian Tughrik"},MOP:{code:"446",denomination:100,min_value:17,min_auth_value:100,symbol:"MOP$",name:"Macau Pataca"},MUR:{code:"480",denomination:100,min_value:70,min_auth_value:100,symbol:"₨",name:"Mauritian Rupee"},MVR:{code:"462",denomination:100,min_value:31,min_auth_value:100,symbol:"Rf",name:"Maldivian Rufiyaa"},MWK:{code:"454",denomination:100,min_value:1e3,min_auth_value:100,symbol:"MK",name:"Malawian Kwacha"},MXN:{code:"484",denomination:100,min_value:39,min_auth_value:100,symbol:"Mex$",name:"Mexican Peso"},MYR:{code:"458",denomination:100,min_value:10,min_auth_value:100,symbol:"RM",name:"Malaysian Ringgit"},NAD:{code:"516",denomination:100,min_value:29,min_auth_value:100,symbol:"N$",name:"Namibian Dollar"},NGN:{code:"566",denomination:100,min_value:723,min_auth_value:100,symbol:"₦",name:"Nigerian Naira"},NIO:{code:"558",denomination:100,min_value:66,min_auth_value:100,symbol:"NIO",name:"Nicaraguan Cordoba"},NOK:{code:"578",denomination:100,min_value:300,min_auth_value:100,symbol:"NOK",name:"Norwegian Krone"},NPR:{code:"524",denomination:100,min_value:221,min_auth_value:100,symbol:"रू",name:"Nepalese Rupee"},NZD:{code:"554",denomination:100,min_value:50,min_auth_value:100,symbol:"NZ$",name:"New Zealand Dollar"},PEN:{code:"604",denomination:100,min_value:10,min_auth_value:100,symbol:"S/",name:"Peruvian Sol"},PGK:{code:"598",denomination:100,min_value:10,min_auth_value:100,symbol:"PGK",name:"Papua New Guinean Kina"},PHP:{code:"608",denomination:100,min_value:106,min_auth_value:100,symbol:"₱",name:"Philippine Peso"},PKR:{code:"586",denomination:100,min_value:227,min_auth_value:100,symbol:"₨",name:"Pakistani Rupee"},QAR:{code:"634",denomination:100,min_value:10,min_auth_value:100,symbol:"QR",name:"Qatari Riyal"},RUB:{code:"643",denomination:100,min_value:130,min_auth_value:100,symbol:"₽",name:"Russian Ruble"},SAR:{code:"682",denomination:100,min_value:10,min_auth_value:100,symbol:"SR",name:"Saudi Arabian Riyal"},SCR:{code:"690",denomination:100,min_value:28,min_auth_value:100,symbol:"SRe",name:"Seychellois Rupee"},SEK:{code:"752",denomination:100,min_value:300,min_auth_value:100,symbol:"SEK",name:"Swedish Krona"},SGD:{code:"702",denomination:100,min_value:50,min_auth_value:100,symbol:"S$",name:"Singapore Dollar"},SLL:{code:"694",denomination:100,min_value:1e3,min_auth_value:100,symbol:"Le",name:"Sierra Leonean Leone"},SOS:{code:"706",denomination:100,min_value:1e3,min_auth_value:100,symbol:"Sh.so.",name:"Somali Shilling"},SSP:{code:"728",denomination:100,min_value:100,min_auth_value:100,symbol:"SS£",name:"South Sudanese Pound"},SVC:{code:"222",denomination:100,min_value:18,min_auth_value:100,symbol:"₡",name:"Salvadoran Colon"},SZL:{code:"748",denomination:100,min_value:29,min_auth_value:100,symbol:"E",name:"Swazi Lilangeni"},THB:{code:"764",denomination:100,min_value:64,min_auth_value:100,symbol:"฿",name:"Thai Baht"},TTD:{code:"780",denomination:100,min_value:14,min_auth_value:100,symbol:"TT$",name:"Trinidadian Dollar"},TZS:{code:"834",denomination:100,min_value:1e3,min_auth_value:100,symbol:"Sh",name:"Tanzanian Shilling"},USD:{code:"840",denomination:100,min_value:50,min_auth_value:100,symbol:"$",name:"US Dollar"},UYU:{code:"858",denomination:100,min_value:67,min_auth_value:100,symbol:"$U",name:"Uruguayan Peso"},UZS:{code:"860",denomination:100,min_value:1e3,min_auth_value:100,symbol:"so'm",name:"Uzbekistani Som"},YER:{code:"886",denomination:100,min_value:501,min_auth_value:100,symbol:"﷼",name:"Yemeni Rial"},ZAR:{code:"710",denomination:100,min_value:29,min_auth_value:100,symbol:"R",name:"South African Rand"}},Ai={three:function(n,e){var i=r(n).replace(new RegExp("(.{1,3})(?=(...)+(\\..{"+e+"})$)","g"),"$1,");return ki(e)(i)},threecommadecimal:function(n,e){var i=wi(r(n)).replace(new RegExp("(.{1,3})(?=(...)+(\\,.{"+e+"})$)","g"),"$1.");return ki(e,",")(i)},threespaceseparator:function(n,e){var i=r(n).replace(new RegExp("(.{1,3})(?=(...)+(\\..{"+e+"})$)","g"),"$1 ");return ki(e)(i)},threespacecommadecimal:function(n,e){var i=wi(r(n)).replace(new RegExp("(.{1,3})(?=(...)+(\\,.{"+e+"})$)","g"),"$1 ");return ki(e,",")(i)},szl:function(n,e){var i=r(n).replace(new RegExp("(.{1,3})(?=(...)+(\\..{"+e+"})$)","g"),"$1, ");return ki(e)(i)},chf:function(n,e){var i=r(n).replace(new RegExp("(.{1,3})(?=(...)+(\\..{"+e+"})$)","g"),"$1'");return ki(e)(i)},inr:function(n,e){var i=r(n).replace(new RegExp("(.{1,2})(?=.(..)+(\\..{"+e+"})$)","g"),"$1,");return ki(e)(i)},none:function(n){return r(n)}},Ti={default:{decimals:2,format:Ai.three,minimum:100},AED:{minor:"fil",minimum:10},AFN:{minor:"pul"},ALL:{minor:"qindarka",minimum:221},AMD:{minor:"luma",minimum:975},ANG:{minor:"cent"},AOA:{minor:"lwei"},ARS:{format:Ai.threecommadecimal,minor:"centavo",minimum:80},AUD:{format:Ai.threespaceseparator,minimum:50,minor:"cent"},AWG:{minor:"cent",minimum:10},AZN:{minor:"qäpik"},BAM:{minor:"fenning"},BBD:{minor:"cent",minimum:10},BDT:{minor:"paisa",minimum:168},BGN:{minor:"stotinki"},BHD:{decimals:3,minor:"fils"},BIF:{decimals:0,major:"franc",minor:"centime"},BMD:{minor:"cent",minimum:10},BND:{minor:"sen",minimum:10},BOB:{minor:"centavo",minimum:14},BRL:{format:Ai.threecommadecimal,minimum:50,minor:"centavo"},BSD:{minor:"cent",minimum:10},BTN:{minor:"chetrum"},BWP:{minor:"thebe",minimum:22},BYR:{decimals:0,major:"ruble"},BZD:{minor:"cent",minimum:10},CAD:{minimum:50,minor:"cent"},CDF:{minor:"centime"},CHF:{format:Ai.chf,minimum:50,minor:"rappen"},CLP:{decimals:0,format:Ai.none,major:"peso",minor:"centavo"},CNY:{minor:"jiao",minimum:14},COP:{format:Ai.threecommadecimal,minor:"centavo",minimum:1e3},CRC:{format:Ai.threecommadecimal,minor:"centimo",minimum:1e3},CUC:{minor:"centavo"},CUP:{minor:"centavo",minimum:53},CVE:{minor:"centavo"},CZK:{format:Ai.threecommadecimal,minor:"haler",minimum:46},DJF:{decimals:0,major:"franc",minor:"centime"},DKK:{minimum:250,minor:"øre"},DOP:{minor:"centavo",minimum:102},DZD:{minor:"centime",minimum:239},EGP:{minor:"piaster",minimum:35},ERN:{minor:"cent"},ETB:{minor:"cent",minimum:57},EUR:{minimum:50,minor:"cent"},FJD:{minor:"cent",minimum:10},FKP:{minor:"pence"},GBP:{minimum:30,minor:"pence"},GEL:{minor:"tetri"},GHS:{minor:"pesewas",minimum:3},GIP:{minor:"pence",minimum:10},GMD:{minor:"butut"},GTQ:{minor:"centavo",minimum:16},GYD:{minor:"cent",minimum:418},HKD:{minimum:400,minor:"cent"},HNL:{minor:"centavo",minimum:49},HRK:{format:Ai.threecommadecimal,minor:"lipa",minimum:14},HTG:{minor:"centime",minimum:167},HUF:{decimals:0,format:Ai.none,major:"forint",minimum:555},IDR:{format:Ai.threecommadecimal,minor:"sen",minimum:1e3},ILS:{minor:"agorot",minimum:10},INR:{format:Ai.inr,minor:"paise"},IQD:{decimals:3,minor:"fil"},IRR:{minor:"rials"},ISK:{decimals:0,format:Ai.none,major:"króna",minor:"aurar"},JMD:{minor:"cent",minimum:250},JOD:{decimals:3,minor:"fil"},JPY:{decimals:0,minimum:50,minor:"sen"},KES:{minor:"cent",minimum:201},KGS:{minor:"tyyn",minimum:140},KHR:{minor:"sen",minimum:1e3},KMF:{decimals:0,major:"franc",minor:"centime"},KPW:{minor:"chon"},KRW:{decimals:0,major:"won",minor:"chon"},KWD:{decimals:3,minor:"fil"},KYD:{minor:"cent",minimum:10},KZT:{minor:"tiyn",minimum:759},LAK:{minor:"at",minimum:1e3},LBP:{format:Ai.threespaceseparator,minor:"piastre",minimum:1e3},LKR:{minor:"cent",minimum:358},LRD:{minor:"cent",minimum:325},LSL:{minor:"lisente",minimum:29},LTL:{format:Ai.threespacecommadecimal,minor:"centu"},LVL:{minor:"santim"},LYD:{decimals:3,minor:"dirham"},MAD:{minor:"centime",minimum:20},MDL:{minor:"ban",minimum:35},MGA:{decimals:0,major:"ariary"},MKD:{minor:"deni"},MMK:{minor:"pya",minimum:1e3},MNT:{minor:"mongo",minimum:1e3},MOP:{minor:"avo",minimum:17},MRO:{minor:"khoum"},MUR:{minor:"cent",minimum:70},MVR:{minor:"lari",minimum:31},MWK:{minor:"tambala",minimum:1e3},MXN:{minor:"centavo",minimum:39},MYR:{minor:"sen",minimum:10},MZN:{decimals:0,major:"metical"},NAD:{minor:"cent",minimum:29},NGN:{minor:"kobo",minimum:723},NIO:{minor:"centavo",minimum:66},NOK:{format:Ai.threecommadecimal,minimum:300,minor:"øre"},NPR:{minor:"paise",minimum:221},NZD:{minimum:50,minor:"cent"},OMR:{minor:"baiza",decimals:3},PAB:{minor:"centesimo"},PEN:{minor:"centimo",minimum:10},PGK:{minor:"toea",minimum:10},PHP:{minor:"centavo",minimum:106},PKR:{minor:"paisa",minimum:227},PLN:{format:Ai.threespacecommadecimal,minor:"grosz"},PYG:{decimals:0,major:"guarani",minor:"centimo"},QAR:{minor:"dirham",minimum:10},RON:{format:Ai.threecommadecimal,minor:"bani"},RUB:{format:Ai.threecommadecimal,minor:"kopeck",minimum:130},RWF:{decimals:0,major:"franc",minor:"centime"},SAR:{minor:"halalat",minimum:10},SBD:{minor:"cent"},SCR:{minor:"cent",minimum:28},SEK:{format:Ai.threespacecommadecimal,minimum:300,minor:"öre"},SGD:{minimum:50,minor:"cent"},SHP:{minor:"new pence"},SLL:{minor:"cent",minimum:1e3},SOS:{minor:"centesimi",minimum:1e3},SRD:{minor:"cent"},STD:{minor:"centimo"},SSP:{minor:"piaster"},SVC:{minor:"centavo",minimum:18},SYP:{minor:"piaster"},SZL:{format:Ai.szl,minor:"cent",minimum:29},THB:{minor:"satang",minimum:64},TJS:{minor:"diram"},TMT:{minor:"tenga"},TND:{decimals:3,minor:"millime"},TOP:{minor:"seniti"},TRY:{minor:"kurus"},TTD:{minor:"cent",minimum:14},TWD:{minor:"cent"},TZS:{minor:"cent",minimum:1e3},UAH:{format:Ai.threespacecommadecimal,minor:"kopiyka"},UGX:{minor:"cent"},USD:{minimum:50,minor:"cent"},UYU:{format:Ai.threecommadecimal,minor:"centé",minimum:67},UZS:{minor:"tiyin",minimum:1e3},VND:{format:Ai.none,minor:"hao,xu"},VUV:{decimals:0,major:"vatu",minor:"centime"},WST:{minor:"sene"},XAF:{decimals:0,major:"franc",minor:"centime"},XCD:{minor:"cent"},XPF:{decimals:0,major:"franc",minor:"centime"},YER:{minor:"fil",minimum:501},ZAR:{format:Ai.threespaceseparator,minor:"cent",minimum:29},ZMK:{minor:"ngwee"}},Ci=function(n){return Ti[n]?Ti[n]:Ti.default},Ei=["AED","ALL","AMD","ARS","AUD","AWG","BBD","BDT","BMD","BND","BOB","BSD","BWP","BZD","CAD","CHF","CNY","COP","CRC","CUP","CZK","DKK","DOP","DZD","EGP","ETB","EUR","FJD","GBP","GHS","GIP","GMD","GTQ","GYD","HKD","HNL","HRK","HTG","HUF","IDR","ILS","INR","JMD","KES","KGS","KHR","KYD","KZT","LAK","LBP","LKR","LRD","LSL","MAD","MDL","MKD","MMK","MNT","MOP","MUR","MVR","MWK","MXN","MYR","NAD","NGN","NIO","NOK","NPR","NZD","PEN","PGK","PHP","PKR","QAR","RUB","SAR","SCR","SEK","SGD","SLL","SOS","SSP","SVC","SZL","THB","TTD","TZS","USD","UYU","UZS","YER","ZAR"],xi={AED:"د.إ",AFN:"&#x60b;",ALL:"Lek",AMD:"֏",ANG:"NAƒ",AOA:"Kz",ARS:"ARS",AUD:"A$",AWG:"Afl.",AZN:"ман",BAM:"KM",BBD:"Bds$",BDT:"৳",BGN:"лв",BHD:"د.ب",BIF:"FBu",BMD:"$",BND:"BND",BOB:"Bs.",BRL:"R$",BSD:"BSD",BTN:"Nu.",BWP:"P",BYR:"Br",BZD:"BZ$",CAD:"C$",CDF:"FC",CHF:"CHf",CLP:"CLP$",CNY:"¥",COP:"COL$",CRC:"₡",CUC:"&#x20b1;",CUP:"$MN",CVE:"Esc",CZK:"Kč",DJF:"Fdj",DKK:"DKK",DOP:"RD$",DZD:"د.ج",EGP:"E£",ERN:"Nfa",ETB:"ብር",EUR:"€",FJD:"FJ$",FKP:"FK&#163;",GBP:"£",GEL:"ლ",GHS:"&#x20b5;",GIP:"GIP",GMD:"D",GNF:"FG",GTQ:"Q",GYD:"G$",HKD:"HK$",HNL:"HNL",HRK:"kn",HTG:"G",HUF:"Ft",IDR:"Rp",ILS:"₪",INR:"₹",IQD:"ع.د",IRR:"&#xfdfc;",ISK:"ISK",JMD:"J$",JOD:"د.ا",JPY:"&#165;",KES:"Ksh",KGS:"Лв",KHR:"៛",KMF:"CF",KPW:"KPW",KRW:"KRW",KWD:"د.ك",KYD:"CI$",KZT:"₸",LAK:"₭",LBP:"&#1604;.&#1604;.",LD:"LD",LKR:"රු",LRD:"L$",LSL:"LSL",LTL:"Lt",LVL:"Ls",LYD:"LYD",MAD:"د.م.",MDL:"MDL",MGA:"Ar",MKD:"ден",MMK:"MMK",MNT:"₮",MOP:"MOP$",MRO:"UM",MUR:"₨",MVR:"Rf",MWK:"MK",MXN:"Mex$",MYR:"RM",MZN:"MT",NAD:"N$",NGN:"₦",NIO:"NIO",NOK:"NOK",NPR:"रू",NZD:"NZ$",OMR:"ر.ع.",PAB:"B/.",PEN:"S/",PGK:"PGK",PHP:"₱",PKR:"₨",PLN:"Zł",PYG:"&#x20b2;",QAR:"QR",RON:"RON",RSD:"Дин.",RUB:"₽",RWF:"RF",SAR:"SR",SBD:"SI$",SCR:"SRe",SDG:"&#163;Sd",SEK:"SEK",SFR:"Fr",SGD:"S$",SHP:"&#163;",SLL:"Le",SOS:"Sh.so.",SRD:"Sr$",SSP:"SS£",STD:"Db",SVC:"₡",SYP:"S&#163;",SZL:"E",THB:"฿",TJS:"SM",TMT:"M",TND:"د.ت",TOP:"T$",TRY:"TL",TTD:"TT$",TWD:"NT$",TZS:"Sh",UAH:"&#x20b4;",UGX:"USh",USD:"$",UYU:"$U",UZS:"so'm",VEF:"Bs",VND:"&#x20ab;",VUV:"VT",WST:"T",XAF:"FCFA",XCD:"EC$",XOF:"CFA",XPF:"CFPF",YER:"﷼",ZAR:"R",ZMK:"ZK",ZWL:"Z$"};Ni={},wn(Ki=Bi,function(n,e){Bi[e]=n,Ti[e]=Ti[e]||{},Ki[e].min_value&&(Ti[e].minimum=Ki[e].min_value),Ki[e].denomination&&(Ti[e].decimals=d.LOG10E*d.log(Ki[e].denomination)),Ni[e]=Ki[e].symbol}),Nn(xi,Ni),Pi(Ni),Pi(xi);vn(Ei,function(n,e){return n[e]=xi[e],n},{});function Gi(n,e,i){return void 0===i&&(i=!0),[xi[e],(t=n,a=Ci(e),o=t/d.pow(10,a.decimals),a.format(o.toFixed(a.decimals),a.decimals))].join(i?" ":"");var t,a,o}function zi(n){return void 0===n&&(n=""),Ri.api+Ri.version+n}var Oi=["key","order_id","invoice_id","subscription_id","auth_link_id","payment_link_id","contact_id","checkout_config_id"];function Fi(e){var i,t=this;if(!x(this,Fi))return new Fi(e);Xe.call(this),this.id=We.makeUid(),Qe.setR(this);try{i=function(n){n&&"object"==typeof n||$("Invalid options");var e=new pi(n);return function(t,a){void 0===a&&(a=[]);var o=!0;t=t.get(),wn(Ui,function(n,e){var i;fn(a,e)||e in t&&((i=n(t[e],t))&&(o=!1,$("Invalid "+e+" ("+i+")")))})}(e,["amount"]),function(n){var i=n.get("notes");wn(i,function(n,e){P(n)?254<n.length&&(i[e]=n.slice(0,254)):w(n)||k(n)||delete i[e]})}(e),e}(e),this.get=i.get,this.set=i.set}catch(n){var a=n.message;this.get&&this.isLiveMode()||B(e)&&!e.parent&&s.alert(a),$(a)}sn(["integration","integration_version","integration_parent_version"],function(n){var e=t.get("_."+n);e&&(We.props[n]=e)}),Oi.every(function(n){return!i.get(n)})&&$("No key passed"),this.postInit()}var $i=Fi.prototype=new Xe;function Hi(n,e){return be.jsonp({url:zi("preferences"),data:n,callback:e})}$i.postInit=_n,$i.onNew=function(n,e){var i=this;"ready"===n&&(this.prefs?e(n,this.prefs):Hi(Ii(this),function(n){n.methods&&(i.prefs=n,i.methods=n.methods),e(i.prefs,n)}))},$i.emi_calculator=function(n,e){return Fi.emi.calculator(this.get("amount")/100,n,e)},Fi.emi={calculator:function(n,e,i){if(!i)return d.ceil(n/e);i/=1200;var t=d.pow(1+i,e);return f(n*i*t/(t-1),10)}};Fi.payment={getMethods:function(e){return Hi({key_id:Fi.defaults.key},function(n){e(n.methods||n)})},getPrefs:function(e,i){var t,a=(t=G(),function(n){return G()-t});return Qe.track("prefs:start",{type:"metric"}),be({url:U(zi("preferences"),e),callback:function(n){if(Qe.track("prefs:end",{type:"metric",data:{time:a()}}),n.xhr&&0===n.xhr.status)return Hi(e,i);i(n)}})}};function Ii(n){if(n){var i=n.get,t={},e=i("key");e&&(t.key_id=e);var a=[i("currency")],o=i("display_currency"),r=i("display_amount");return o&&(""+r).length&&a.push(o),t.currency=a,sn(["order_id","customer_id","invoice_id","payment_link_id","subscription_id","auth_link_id","recurring","subscription_card_change","account_id","contact_id","checkout_config_id"],function(n){var e=i(n);e&&(t[n]=e)}),t["_[build]"]=10125,t["_[checkout_id]"]=n.id,t["_[library]"]=We.props.library,t["_[platform]"]=We.props.platform,t}}$i.isLiveMode=function(){var n=this.preferences;return!n&&/^rzp_l/.test(this.get("key"))||n&&"live"===n.mode},$i.calculateFees=function(n){var t=this;return new Le(function(e,i){n=Mi(n,t),be.post({url:zi("payments/calculate/fees"),data:n,callback:function(n){return(n.error?i:e)(n)}})})};var Ui={notes:function(n){if(B(n)&&15<C(Z(n)))return"At most 15 notes are allowed"},amount:function(n,e){var i,t,a=e.display_currency||e.currency||"INR",o=Ci(a),r=o.minimum,m="";if(o.decimals&&o.minor?m=" "+o.minor:o.major&&(m=" "+o.major),void 0===(t=r)&&(t=100),(/[^0-9]/.test(i=n)||!(t<=(i=f(i,10))))&&!e.recurring)return"should be passed in integer"+m+". Minimum value is "+r+m+", i.e. "+Gi(r,a)},currency:function(n){if(!fn(Ei,n))return"The provided currency is not currently supported"},display_currency:function(n){if(!(n in xi)&&n!==Fi.defaults.display_currency)return"This display currency is not supported"},display_amount:function(n){if(!(n=r(n).replace(/([^0-9.])/g,""))&&n!==Fi.defaults.display_amount)return""},payout:function(n,e){if(n){if(!e.key)return"key is required for a Payout";if(!e.contact_id)return"contact_id is required for a Payout"}}};Fi.configure=function(n){wn(vi(n,di),function(n,e){typeof di[e]==typeof n&&(di[e]=n)})},Fi.defaults=di,s.Razorpay=Fi,di.timeout=0,di.name="",di.partnership_logo="",di.nativeotp=!0,di.remember_customer=!1,di.personalization=!1,di.paused=!1,di.fee_label="",di.min_amount_label="",di.partial_payment={min_amount_label:"",full_amount_label:"",partial_amount_label:"",partial_amount_description:"",select_partial:!1},di.method={netbanking:null,card:!0,credit_card:!0,debit_card:!0,cardless_emi:null,wallet:null,emi:!0,upi:null,upi_intent:!0,qr:!0,bank_transfer:!0,upi_otm:!0},di.prefill={amount:"",wallet:"",provider:"",method:"",name:"",contact:"",email:"",vpa:"","card[number]":"","card[expiry]":"","card[cvv]":"",bank:"","bank_account[name]":"","bank_account[account_number]":"","bank_account[account_type]":"","bank_account[ifsc]":"",auth_type:""},di.features={cardsaving:!0},di.readonly={contact:!1,email:!1,name:!1},di.hidden={contact:!1,email:!1},di.modal={confirm_close:!1,ondismiss:_n,onhidden:_n,escape:!0,animation:!s.matchMedia("(prefers-reduced-motion: reduce)").matches,backdropclose:!1,handleback:!0},di.external={wallets:[],handler:_n},di.theme={upi_only:!1,color:"",backdrop_color:"rgba(0,0,0,0.6)",image_padding:!0,image_frame:!0,close_button:!0,close_method_back:!1,hide_topbar:!1,branding:"",debit_card:!1},di._={integration:null,integration_version:null,integration_parent_version:null},di.config={display:{}};var Zi,Yi,ji,Wi,Vi=s.screen,qi=s.scrollTo,Ji=ai,Qi={overflow:"",metas:null,orientationchange:function(){Qi.resize.call(this),Qi.scroll.call(this)},resize:function(){var n=s.innerHeight||Vi.height;et.container.style.position=n<450?"absolute":"fixed",this.el.style.height=d.max(n,460)+"px"},scroll:function(){var n;"number"==typeof s.pageYOffset&&(s.innerHeight<460?(n=460-s.innerHeight,s.pageYOffset>120+n&&le(n)):this.isFocused||le(0))}};function Xi(){return Qi.metas||(Qi.metas=me('head meta[name=viewport],head meta[name="theme-color"]')),Qi.metas}function nt(n){try{et.backdrop.style.background=n}catch(n){}}function et(n){if(Zi=c.body,Yi=c.head,ji=Zi.style,n)return this.getEl(n),this.openRzp(n);this.getEl(),this.time=G()}et.prototype={getEl:function(n){var e,i,t,a,o,r;return this.el||(i={style:"opacity: 1; height: 100%; position: relative; background: none; display: block; border: 0 none transparent; margin: 0px; padding: 0px; z-index: 2;",allowtransparency:!0,frameborder:0,width:"100%",height:"100%",allowpaymentrequest:!0,src:(t=n,o=Ri.frame,r=z()<.01,o||(o=zi("checkout"),(a=Ii(t))?o=U(o,a):(o+="/public",r&&(o+="/canary"))),r&&(o=U(o,{canary:1})),o),class:"razorpay-checkout-frame"},this.el=(e=Bn("iframe"),Un(i)(e))),this.el},openRzp:function(n){var e,i,t,a,o,r=(e=this.el,Zn({width:"100%",height:"100%"})(e)),m=n.get("parent"),u=(m=m&&W(m))||et.container;!function(n,e){if(!Wi)try{var i;(Wi=c.createElement("div")).className="razorpay-loader";var t="margin:-25px 0 0 -25px;height:50px;width:50px;animation:rzp-rot 1s infinite linear;-webkit-animation:rzp-rot 1s infinite linear;border: 1px solid rgba(255, 255, 255, 0.2);border-top-color: rgba(255, 255, 255, 0.7);border-radius: 50%;";t+=e?"margin: 100px auto -150px;border: 1px solid rgba(0, 0, 0, 0.2);border-top-color: rgba(0, 0, 0, 0.7);":"position:absolute;left:50%;top:50%;",Wi.setAttribute("style",t),i=Wi,zn(n)(i)}catch(n){}}(u,m),n!==this.rzp&&(An(r)!==u&&(i=u,On(r)(i)),this.rzp=n),m?(t=r,In("minHeight","530px")(t),this.embedded=!0):(a=u,o=In("display","block")(a),Wn(o),nt(n.get("theme.backdrop_color")),/^rzp_t/.test(n.get("key"))&&et.ribbon&&(et.ribbon.style.opacity=1),this.setMetaAndOverflow()),this.bind(),this.onload()},makeMessage:function(){var n=this.rzp,i=n.get(),e={integration:We.props.integration,referer:b.href,options:i,id:n.id};return n.metadata&&(e.metadata=n.metadata),wn(n.modal.options,function(n,e){i["modal."+e]=n}),this.embedded&&(delete i.parent,e.embedded=!0),function(n){var e,i,t=n.image;if(t&&P(t)){if(H(t))return;t.indexOf("http")&&(e=b.protocol+"//"+b.hostname+(b.port?":"+b.port:""),i="","/"!==t[0]&&"/"!==(i+=b.pathname.replace(/[^/]*$/g,""))[0]&&(i="/"+i),n.image=e+i+t)}}(i),e},close:function(){nt(""),et.ribbon&&(et.ribbon.style.opacity=0),function(n){n&&sn(n,Fn);var e=Xi();e&&sn(e,zn(Yi))}(this.$metas),ji.overflow=Qi.overflow,this.unbind(),Ji&&qi(0,Qi.oldY),We.flush()},bind:function(){var n,t=this;this.listeners||(this.listeners=[],n={},Ji&&(n.orientationchange=Qi.orientationchange,this.rzp.get("parent")||(n.resize=Qi.resize)),wn(n,function(n,e){var i;t.listeners.push((i=window,j(e,pn(n,t))(i)))}))},unbind:function(){var n=this.listeners;sn(n,function(n){return n()}),this.listeners=null},setMetaAndOverflow:function(){var n,e;Yi&&(sn(Xi(),function(n){return Fn(n)}),this.$metas=[(n=Bn("meta"),Un({name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"})(n)),(e=Bn("meta"),Un({name:"theme-color",content:this.rzp.get("theme.color")})(e))],sn(this.$metas,zn(Yi)),Qi.overflow=ji.overflow,ji.overflow="hidden",Ji&&(Qi.oldY=s.pageYOffset,s.scrollTo(0,0),Qi.orientationchange.call(this)))},postMessage:function(n){n.id=this.rzp.id,n=Pn(n),this.el.contentWindow.postMessage(n,"*")},onmessage:function(n){var e,i,t=Kn(n.data);t&&(e=t.event,i=this.rzp,n.origin&&"frame"===t.source&&n.source===this.el.contentWindow&&(t=t.data,this["on"+e](t),"dismiss"!==e&&"fault"!==e||Qe.track(e,{data:t,r:i,immediately:!0})))},onload:function(){this.rzp&&this.postMessage(this.makeMessage())},onfocus:function(){this.isFocused=!0},onblur:function(){this.isFocused=!1,Qi.orientationchange.call(this)},onrender:function(){Wi&&(Fn(Wi),Wi=null),this.rzp.emit("render")},onevent:function(n){this.rzp.emit(n.event,n.data)},onredirect:function(n){We.flush(),n.target||(n.target=this.rzp.get("target")||"_top"),function(n){if(!n.target&&s!==s.parent)return s.Razorpay.sendMessage({event:"redirect",data:n});ue(n.url,n.content,n.method,n.target)}(n)},onsubmit:function(e){We.flush();var i=this.rzp;"wallet"===e.method&&sn(i.get("external.wallets"),function(n){if(n===e.wallet)try{i.get("external.handler").call(i,e)}catch(n){}}),i.emit("payment.submit",{method:e.method})},ondismiss:function(n){this.close();var e=this.rzp.get("modal.ondismiss");K(e)&&a(function(){return e(n)})},onhidden:function(){We.flush(),this.afterClose();var n=this.rzp.get("modal.onhidden");K(n)&&n()},oncomplete:function(n){this.close();var e=this.rzp,i=e.get("handler");Qe.track("checkout_success",{r:e,data:n,immediately:!0}),K(i)&&a(function(){i.call(e,n)},200)},onpaymenterror:function(n){We.flush();try{this.rzp.emit("payment.error",n),this.rzp.emit("payment.failed",n)}catch(n){}},onfailure:function(n){this.ondismiss(),s.alert("Payment Failed.\n"+n.error.description),this.onhidden()},onfault:function(n){var e="Something went wrong.";P(n)?e=n:N(n)&&(n.message||n.description)&&(e=n.message||n.description),We.flush(),this.rzp.close();var i=this.rzp.get("callback_url");(this.rzp.get("redirect")||li)&&i?ue(i,{error:n},"post"):s.alert("Oops! Something went wrong.\n"+e),this.afterClose()},afterClose:function(){et.container.style.display="none"},onflush:function(){We.flush()}};var it,tt=E(Fi);function at(e){return function n(){return it?e.call(this):(a(pn(n,this),99),this)}}!function n(){(it=c.body||c.getElementsByTagName("body")[0])||a(n,99)}();var ot,rt=c.currentScript||(ot=me("script"))[ot.length-1];function mt(n){var e,i=An(rt),t=On((e=Bn(),Yn(ce(n))(e)))(i),a=Rn("onsubmit",_n)(t);$n(a)}function ut(m){var n,e=An(rt),i=On((n=Bn("input"),Nn({type:"submit",value:m.get("buttontext"),className:"razorpay-payment-button"})(n)))(e);Rn("onsubmit",function(n){n.preventDefault();var e=this.action,i=this.method,t=this.target,a=m.get();if(P(e)&&e&&!a.callback_url){var o={url:e,content:vn(this.querySelectorAll("[name]"),function(n,e){return n[e.name]=e.value,n},{}),method:P(i)?i:"get",target:P(t)&&t};try{var r=v(Pn({request:o,options:Pn(a),back:b.href}));a.callback_url=zi("checkout/onyx")+"?data="+r}catch(n){}}return m.open(),!1})(i)}var ct,lt;function st(){var n,e,i,t,a,o,r,m,u,c,l,s,d,f,h,v;return ct||(n=Bn(),e=Rn("className","razorpay-container")(n),i=Rn("innerHTML","<style>@keyframes rzp-rot{to{transform: rotate(360deg);}}@-webkit-keyframes rzp-rot{to{-webkit-transform: rotate(360deg);}}</style>")(e),t=Zn({zIndex:1e9,position:"fixed",top:0,display:"none",left:0,height:"100%",width:"100%","-webkit-overflow-scrolling":"touch","-webkit-backface-visibility":"hidden","overflow-y":"visible"})(i),ct=zn(it)(t),d=et.container=ct,v=Bn(),h=Rn("className","razorpay-backdrop")(v),f=Zn({"min-height":"100%",transition:"0.3s ease-out",position:"fixed",top:0,left:0,width:"100%",height:"100%"})(h),a=zn(d)(f),r=et.backdrop=a,l="rotate(45deg)",s="opacity 0.3s ease-in",c=Bn("span"),u=Rn("innerHTML","Test Mode")(c),m=Zn({"text-decoration":"none",background:"#D64444",border:"1px dashed white",padding:"3px",opacity:"0","-webkit-transform":l,"-moz-transform":l,"-ms-transform":l,"-o-transform":l,transform:l,"-webkit-transition":s,"-moz-transition":s,transition:s,"font-family":"lato,ubuntu,helvetica,sans-serif",color:"white",position:"absolute",width:"200px","text-align":"center",right:"-50px",top:"50px"})(u),o=zn(r)(m),et.ribbon=o),ct}function dt(n){var e,i;return lt?lt.openRzp(n):(lt=new et(n),e=s,j("message",pn("onmessage",lt))(e),i=ct,On(lt.el)(i)),lt}Fi.open=function(n){return Fi(n).open()},tt.postInit=function(){this.modal={options:{}},this.get("parent")&&this.open()};var ft=tt.onNew;tt.onNew=function(n,e){"payment.error"===n&&We(this,"event_paymenterror",b.href),K(ft)&&ft.call(this,n,e)},tt.open=at(function(){this.metadata||(this.metadata={}),this.metadata.openedAt=u.now();var n=this.checkoutFrame=dt(this);return We(this,"open"),n.el.contentWindow||(n.close(),n.afterClose(),s.alert("This browser is not supported.\nPlease try payment in another browser.")),"-new.js"===rt.src.slice(-7)&&We(this,"oldscript",b.href),this}),tt.resume=function(n){var e=this.checkoutFrame;e&&e.postMessage({event:"resume",data:n})},tt.close=function(){var n=this.checkoutFrame;n&&n.postMessage({event:"close"})};var ht=at(function(){st(),lt=dt();try{!function(){var a={};wn(rt.attributes,function(n){var e,i,t=n.name.toLowerCase();/^data-/.test(t)&&(e=a,t=t.replace(/^data-/,""),"true"===(i=n.value)?i=!0:"false"===i&&(i=!1),/^notes\./.test(t)&&(a.notes||(a.notes={}),e=a.notes,t=t.replace(/^notes\./,"")),e[t]=i)});var n,e=a.key;e&&0<e.length&&(a.handler=mt,n=Fi(a),a.parent||ut(n))}()}catch(n){}});We.props.library="checkoutjs",di.handler=function(n){var e;!x(this,Fi)||(e=this.get("callback_url"))&&ue(e,n,"post")},di.buttontext="Pay Now",di.parent=null,Ui.parent=function(n){if(!W(n))return"parent provided for embedded mode doesn't exist"},ht()}()}();