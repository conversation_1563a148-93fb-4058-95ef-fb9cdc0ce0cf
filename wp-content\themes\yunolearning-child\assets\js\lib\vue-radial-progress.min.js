!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("RadialProgressBar",[],t):"object"==typeof exports?exports.RadialProgressBar=t():e.RadialProgressBar=t()}(window,(function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="/dist/",r(r.s="./src/main.js")}({"./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./src/RadialProgressBar.vue?vue&type=script&lang=js&":function(e,t,r){"use strict";r.r(t),t.default={props:{diameter:{type:Number,required:!1,default:200},totalSteps:{type:Number,required:!0,default:10},completedSteps:{type:Number,required:!0,default:0},startColor:{type:String,required:!1,default:"#bbff42"},stopColor:{type:String,required:!1,default:"#429321"},strokeWidth:{type:Number,required:!1,default:10},innerStrokeWidth:{type:Number,required:!1,default:10},strokeLinecap:{type:String,required:!1,default:"round"},animateSpeed:{type:Number,required:!1,default:1e3},innerStrokeColor:{type:String,required:!1,default:"#323232"},fps:{type:Number,required:!1,default:60},timingFunc:{type:String,required:!1,default:"linear"},isClockwise:{type:Boolean,required:!1,default:!0}},data:function(){return{gradient:{fx:.99,fy:.5,cx:.5,cy:.5,r:.65},gradientAnimation:null,currentAngle:0,strokeDashoffset:0}},computed:{radius:function(){return this.diameter/2},circumference:function(){return Math.PI*this.innerCircleDiameter},stepSize:function(){return 0===this.totalSteps?0:100/this.totalSteps},finishedPercentage:function(){return this.stepSize*this.completedSteps},circleSlice:function(){return 2*Math.PI/this.totalSteps},animateSlice:function(){return this.circleSlice/this.totalPoints},innerCircleDiameter:function(){return this.diameter-2*this.innerStrokeWidth},innerCircleRadius:function(){return this.innerCircleDiameter/2},totalPoints:function(){return this.animateSpeed/this.animationIncrements},animationIncrements:function(){return 1e3/this.fps},hasGradient:function(){return this.startColor!==this.stopColor},containerStyle:function(){return{height:"".concat(this.diameter,"px"),width:"".concat(this.diameter,"px")}},progressStyle:function(){return{height:"".concat(this.diameter,"px"),width:"".concat(this.diameter,"px"),strokeWidth:"".concat(this.strokeWidth,"px"),strokeDashoffset:this.strokeDashoffset,transition:"stroke-dashoffset ".concat(this.animateSpeed,"ms ").concat(this.timingFunc)}},strokeStyle:function(){return{height:"".concat(this.diameter,"px"),width:"".concat(this.diameter,"px"),strokeWidth:"".concat(this.innerStrokeWidth,"px")}},innerCircleStyle:function(){return{width:"".concat(this.innerCircleDiameter,"px")}}},methods:{getStopPointsOfCircle:function(e){for(var t=[],r=0;r<e;r++){var n=this.circleSlice*r;t.push(this.getPointOfCircle(n))}return t},getPointOfCircle:function(e){return{x:.5+.5*Math.cos(e),y:.5+.5*Math.sin(e)}},gotoPoint:function(){var e=this.getPointOfCircle(this.currentAngle);e.x&&e.y&&(this.gradient.fx=e.x,this.gradient.fy=e.y)},direction:function(){return this.isClockwise?1:-1},changeProgress:function(e){var t=this,r=e.isAnimate,n=void 0===r||r;if(this.strokeDashoffset=(100-this.finishedPercentage)/100*this.circumference*this.direction(),this.gradientAnimation&&clearInterval(this.gradientAnimation),n){var i=(this.completedSteps-1)*this.circleSlice,s=(this.currentAngle-i)/this.animateSlice,o=Math.abs(s-this.totalPoints)/this.totalPoints,a=s<this.totalPoints;this.gradientAnimation=setInterval((function(){a&&s>=t.totalPoints||!a&&s<t.totalPoints?clearInterval(t.gradientAnimation):(t.currentAngle=i+t.animateSlice*s,t.gotoPoint(),s+=a?o:-o)}),this.animationIncrements)}else this.gotoNextStep()},gotoNextStep:function(){this.currentAngle=this.completedSteps*this.circleSlice,this.gotoPoint()}},watch:{totalSteps:function(){this.changeProgress({isAnimate:!0})},completedSteps:function(){this.changeProgress({isAnimate:!0})},diameter:function(){this.changeProgress({isAnimate:!0})},strokeWidth:function(){this.changeProgress({isAnimate:!0})}},created:function(){this.changeProgress({isAnimate:!1})}}},"./node_modules/css-loader/dist/cjs.js!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/vue-loader/lib/index.js?!./src/RadialProgressBar.vue?vue&type=style&index=0&lang=css&":function(e,t,r){(t=r("./node_modules/css-loader/dist/runtime/api.js")(!1)).push([e.i,"\n.radial-progress-container {\n  position: relative;\n}\n.radial-progress-inner {\n  position: absolute;\n  top: 0; right: 0; bottom: 0; left: 0;\n  position: absolute;\n  border-radius: 50%;\n  margin: 0 auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n",""]),e.exports=t},"./node_modules/css-loader/dist/runtime/api.js":function(e,t,r){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r=function(e,t){var r=e[1]||"",n=e[3];if(!n)return r;if(t&&"function"==typeof btoa){var i=(o=n,a=btoa(unescape(encodeURIComponent(JSON.stringify(o)))),d="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(a),"/*# ".concat(d," */")),s=n.sources.map((function(e){return"/*# sourceURL=".concat(n.sourceRoot||"").concat(e," */")}));return[r].concat(s).concat([i]).join("\n")}var o,a,d;return[r].join("\n")}(t,e);return t[2]?"@media ".concat(t[2]," {").concat(r,"}"):r})).join("")},t.i=function(e,r,n){"string"==typeof e&&(e=[[null,e,""]]);var i={};if(n)for(var s=0;s<this.length;s++){var o=this[s][0];null!=o&&(i[o]=!0)}for(var a=0;a<e.length;a++){var d=[].concat(e[a]);n&&i[d[0]]||(r&&(d[2]?d[2]="".concat(r," and ").concat(d[2]):d[2]=r),t.push(d))}},t}},"./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./src/RadialProgressBar.vue?vue&type=template&id=09df9cd4&":function(e,t,r){"use strict";r.r(t),r.d(t,"render",(function(){return n})),r.d(t,"staticRenderFns",(function(){return i}));var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"radial-progress-container",style:e.containerStyle},[r("div",{staticClass:"radial-progress-inner",style:e.innerCircleStyle},[e._t("default")],2),e._v(" "),r("svg",{staticClass:"radial-progress-bar",attrs:{width:e.diameter,height:e.diameter,version:"1.1",xmlns:"http://www.w3.org/2000/svg"}},[r("defs",[r("radialGradient",{attrs:{id:"radial-gradient"+e._uid,fx:e.gradient.fx,fy:e.gradient.fy,cx:e.gradient.cx,cy:e.gradient.cy,r:e.gradient.r}},[r("stop",{attrs:{offset:"30%","stop-color":e.startColor}}),e._v(" "),r("stop",{attrs:{offset:"100%","stop-color":e.stopColor}})],1)],1),e._v(" "),r("circle",{style:e.strokeStyle,attrs:{r:e.innerCircleRadius,cx:e.radius,cy:e.radius,fill:"transparent",stroke:e.innerStrokeColor,"stroke-dasharray":e.circumference,"stroke-dashoffset":"0","stroke-linecap":e.strokeLinecap}}),e._v(" "),r("circle",{style:e.progressStyle,attrs:{transform:"rotate(270, "+e.radius+","+e.radius+")",r:e.innerCircleRadius,cx:e.radius,cy:e.radius,fill:"transparent",stroke:"url(#radial-gradient"+e._uid+")","stroke-dasharray":e.circumference,"stroke-dashoffset":e.circumference,"stroke-linecap":e.strokeLinecap}})])])},i=[]},"./node_modules/vue-loader/lib/runtime/componentNormalizer.js":function(e,t,r){"use strict";function n(e,t,r,n,i,s,o,a){var d,l="function"==typeof e?e.options:e;if(t&&(l.render=t,l.staticRenderFns=r,l._compiled=!0),n&&(l.functional=!0),s&&(l._scopeId="data-v-"+s),o?(d=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(o)},l._ssrRegister=d):i&&(d=a?function(){i.call(this,(l.functional?this.parent:this).$root.$options.shadowRoot)}:i),d)if(l.functional){l._injectStyles=d;var u=l.render;l.render=function(e,t){return d.call(t),u(e,t)}}else{var c=l.beforeCreate;l.beforeCreate=c?[].concat(c,d):[d]}return{exports:e,options:l}}r.r(t),r.d(t,"default",(function(){return n}))},"./node_modules/vue-style-loader/index.js!./node_modules/css-loader/dist/cjs.js!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/vue-loader/lib/index.js?!./src/RadialProgressBar.vue?vue&type=style&index=0&lang=css&":function(e,t,r){var n=r("./node_modules/css-loader/dist/cjs.js!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/vue-loader/lib/index.js?!./src/RadialProgressBar.vue?vue&type=style&index=0&lang=css&");"string"==typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);(0,r("./node_modules/vue-style-loader/lib/addStylesClient.js").default)("60f95787",n,!0,{})},"./node_modules/vue-style-loader/lib/addStylesClient.js":function(e,t,r){"use strict";r.r(t),r.d(t,"default",(function(){return p}));var n=r("./node_modules/vue-style-loader/lib/listToStyles.js"),i="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!i)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var s={},o=i&&(document.head||document.getElementsByTagName("head")[0]),a=null,d=0,l=!1,u=function(){},c=null,f="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function p(e,t,r,i){l=r,c=i||{};var o=Object(n.default)(e,t);return h(o),function(t){for(var r=[],i=0;i<o.length;i++){var a=o[i];(d=s[a.id]).refs--,r.push(d)}t?h(o=Object(n.default)(e,t)):o=[];for(i=0;i<r.length;i++){var d;if(0===(d=r[i]).refs){for(var l=0;l<d.parts.length;l++)d.parts[l]();delete s[d.id]}}}}function h(e){for(var t=0;t<e.length;t++){var r=e[t],n=s[r.id];if(n){n.refs++;for(var i=0;i<n.parts.length;i++)n.parts[i](r.parts[i]);for(;i<r.parts.length;i++)n.parts.push(v(r.parts[i]));n.parts.length>r.parts.length&&(n.parts.length=r.parts.length)}else{var o=[];for(i=0;i<r.parts.length;i++)o.push(v(r.parts[i]));s[r.id]={id:r.id,refs:1,parts:o}}}}function m(){var e=document.createElement("style");return e.type="text/css",o.appendChild(e),e}function v(e){var t,r,n=document.querySelector('style[data-vue-ssr-id~="'+e.id+'"]');if(n){if(l)return u;n.parentNode.removeChild(n)}if(f){var i=d++;n=a||(a=m()),t=b.bind(null,n,i,!1),r=b.bind(null,n,i,!0)}else n=m(),t=x.bind(null,n),r=function(){n.parentNode.removeChild(n)};return t(e),function(n){if(n){if(n.css===e.css&&n.media===e.media&&n.sourceMap===e.sourceMap)return;t(e=n)}else r()}}var g,y=(g=[],function(e,t){return g[e]=t,g.filter(Boolean).join("\n")});function b(e,t,r,n){var i=r?"":n.css;if(e.styleSheet)e.styleSheet.cssText=y(t,i);else{var s=document.createTextNode(i),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(s,o[t]):e.appendChild(s)}}function x(e,t){var r=t.css,n=t.media,i=t.sourceMap;if(n&&e.setAttribute("media",n),c.ssrId&&e.setAttribute("data-vue-ssr-id",t.id),i&&(r+="\n/*# sourceURL="+i.sources[0]+" */",r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */"),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}},"./node_modules/vue-style-loader/lib/listToStyles.js":function(e,t,r){"use strict";function n(e,t){for(var r=[],n={},i=0;i<t.length;i++){var s=t[i],o=s[0],a={id:e+":"+i,css:s[1],media:s[2],sourceMap:s[3]};n[o]?n[o].parts.push(a):r.push(n[o]={id:o,parts:[a]})}return r}r.r(t),r.d(t,"default",(function(){return n}))},"./src/RadialProgressBar.vue":function(e,t,r){"use strict";r.r(t);var n=r("./src/RadialProgressBar.vue?vue&type=template&id=09df9cd4&"),i=r("./src/RadialProgressBar.vue?vue&type=script&lang=js&"),s=(r("./src/RadialProgressBar.vue?vue&type=style&index=0&lang=css&"),r("./node_modules/vue-loader/lib/runtime/componentNormalizer.js")),o=Object(s.default)(i.default,n.render,n.staticRenderFns,!1,null,null,null);t.default=o.exports},"./src/RadialProgressBar.vue?vue&type=script&lang=js&":function(e,t,r){"use strict";r.r(t);var n=r("./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./src/RadialProgressBar.vue?vue&type=script&lang=js&");t.default=n.default},"./src/RadialProgressBar.vue?vue&type=style&index=0&lang=css&":function(e,t,r){"use strict";r.r(t);var n=r("./node_modules/vue-style-loader/index.js!./node_modules/css-loader/dist/cjs.js!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/vue-loader/lib/index.js?!./src/RadialProgressBar.vue?vue&type=style&index=0&lang=css&"),i=r.n(n);for(var s in n)"default"!==s&&function(e){r.d(t,e,(function(){return n[e]}))}(s);t.default=i.a},"./src/RadialProgressBar.vue?vue&type=template&id=09df9cd4&":function(e,t,r){"use strict";r.r(t);var n=r("./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./src/RadialProgressBar.vue?vue&type=template&id=09df9cd4&");r.d(t,"render",(function(){return n.render})),r.d(t,"staticRenderFns",(function(){return n.staticRenderFns}))},"./src/main.js":function(e,t,r){e.exports=r("./src/RadialProgressBar.vue").default}})}));