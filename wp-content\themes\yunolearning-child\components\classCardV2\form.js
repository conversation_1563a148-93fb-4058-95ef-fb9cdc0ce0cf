Vue.component("star-rating", VueStarRating.default);

Vue.component("yuno-form", {
  props: ["data", "options"],
  template: `
    <validation-observer
      tag="div"
      style="width: 100%"
      v-slot="{ handleSubmit, invalid }"
    >
      <form 
			id="yunoForm" 
			@submit.prevent="handleSubmit(initForm)" 
			style="display: flex; flex-direction: column; gap: 17px;"
      	>
			<div 
				style="display: flex; flex-direction: column; gap: 11px; padding: 6px; text-align: center; border: 1px solid #E6E6E6"
			>
				<span class="onSurfaceVariant caption1">Rate this Class</span>
				<b-field>
				<validation-provider 
					tag="div"
					:customMessages="{ required: 'Rating is required', is_not: 'Rating should not be blank' }"
					:rules="{ required: true, is_not: 0 }" 
					v-slot="{ errors, classes }"
				>
					<div @mouseleave="resetLabel">
					<star-rating 
						@rating-selected="setRating"
						@current-rating="currentRating"
						active-color="#F9B600"
						active-border-color="#F9B600" 
						border-color="#696969"
						inactive-color="#FFFFFF"
						:border-width="4"
						:padding="10"
						:star-size="14"
						:show-rating="false"
						v-model="data.rating"
					></star-rating>
					</div>
					<ul class="starLabel onSurfaceVariant">
					<li 
						v-for="(label, i) in options.ratingLabels" 
						:class="[label.isActive ? 'active' : 'notActive']"
					>
						{{ label.label }}
					</li>
					</ul>
					<p class="error">{{ errors[0] }}</p>
				</validation-provider>
				</b-field>
			</div>
			<template>
				<div class="issues" v-if="options.hasIssues">
					<p class="subtitle2 onSurface pb-1">What were the issues?</p>
					<div>
						<template v-if="topIssuesCited.loading">
							<div v-for="i in 3" :key="i">
								<b-skeleton width="150px"></b-skeleton>
							</div>
						</template>
						<template v-else-if="topIssuesCited.success">
							<validation-provider
								tag="div"
								class="checkboxList"
								:rules="{ required: false }" 
								v-slot="{ errors, classes }"
							>
								<template v-for="(item, j) in topIssuesCited.data.issues">
									<b-field :key="j">
										<b-checkbox-button
										:class="classes"
										class="classCheckbox"
										:native-value="item.id"
										v-model="data.feedback_ids"
										:disabled="isMaxSelected && !isSelected(item.id)"
										>
										<span class="caption1">{{ item.label }}</span>
										</b-checkbox-button>
									</b-field>
								</template>
							</validation-provider>
						</template>
					</div>
				</div>
				<div class="comment" style="height: 100px" v-if="options.hasIssues">
					<p class="subtitle2 onSurface pb-1">Please leave a comment for the instructor</p>
					<b-field>
						<validation-provider 
						:customMessages="{ required: 'Comment should not be blank' }"
						:rules="{ required: false }" 
						v-slot="{ errors, classes }"
						>
							<b-input 
								:class="classes"
								class="yunoInput"
								placeholder="Enter your comment here"
								v-model="data.review"
								type="textarea"
							>
							</b-input>
							<p class="error">{{ errors[0] }}</p>
						</validation-provider>
					</b-field>
				</div>
				<div 
					v-if="options.hasIssues" 
					class="ctaWrapper" 
					style="display: flex; justify-content: center; margin-top: 0"
				>
					<b-button
						:loading="options.loading ? true : false"
						:disabled="options.loading ? true : false"  
						class="yunoPrimaryCTA"
						native-type="submit"
					>
						Submit Feedback
					</b-button> 
				</div>
			</template>
      	</form>
    </validation-observer>
  `,
  data() {
    return {
      drag: false,
    };
  },
  computed: {
    ...Vuex.mapState(["topIssuesCited", "resource"]),

    isMaxSelected() {
      return this.data.feedback_ids.length >= 5;
    },

    isSelected() {
      return (issueId) => this.data.feedback_ids.includes(issueId);
    },
  },
  async created() {},
  mounted() {},
  methods: {
    resetLabel() {
      this.$emit("starLabelVisibility");
    },
    currentRating(data) {
      this.$emit("starLabel", data);
    },
    setRating(data) {
      this.$emit("setRating", data);
    },
    initForm() {
      this.$emit("initForm");
    },
  },
});
