#app .chooseOptions {
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .chooseOptions {
    padding: 60px 0 30px;
  }
}

#app .chooseOptions .sectionTitle {
  font-size: 32px;
  color: #002F5A;
  font-weight: 700;
  text-align: center;
  margin: 0;
}

@media (min-width: 768px) {
  #app .chooseOptions .sectionTitle {
    font-size: 48px;
  }
}

#app .chooseOptions .sectionSubtitle {
  font-size: 18px;
  font-weight: 400;
  text-align: center;
  margin: 16px 0 0 0;
  line-height: 24px;
}

#app .chooseOptions .optionsWrapper {
  margin-top: 30px;
}

#app .chooseOptions .optionCard {
  border: 1px solid #E6E6E6;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  #app .chooseOptions .optionCard {
    margin-bottom: 0;
    height: 100%;
  }
}

#app .chooseOptions .optionCard a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

#app .chooseOptions .optionCard a:hover {
  text-decoration: none;
}

#app .chooseOptions .optionCard a:hover .optionCaption {
  color: #a62027;
}

#app .chooseOptions .optionCard .optionImg {
  width: 100%;
  height: auto;
}

#app .chooseOptions .optionCard figcaption {
  padding: 15px;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .chooseOptions .optionCard .optionCaption {
  font-size: 18px;
  font-weight: 600;
  color: #000;
}
/*# sourceMappingURL=chooseOptions.css.map */