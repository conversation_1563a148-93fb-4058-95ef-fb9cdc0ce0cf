<svg xmlns="http://www.w3.org/2000/svg" width="475.155" height="407.44" viewBox="0 0 475.155 407.44">
  <g id="Group_1937" data-name="Group 1937" transform="translate(-758 -896)">
    <g id="Group_1934" data-name="Group 1934" transform="translate(-688 -73.171)">
      <g id="Group_1917" data-name="Group 1917" transform="translate(657 74)">
        <g id="Group_105" data-name="Group 105" transform="translate(805 941)">
          <g id="Group_98" data-name="Group 98" transform="translate(0 0)">
            <g id="Group_88" data-name="Group 88" transform="translate(0 7.449)" opacity="0.1">
              <path id="Path_138" data-name="Path 138" d="M459.773,291.644a2.293,2.293,0,0,0-2.862-1.478l-12.622,5.163a2.939,2.939,0,0,0-.885-2.1l3.05-1.247a2.258,2.258,0,0,0,.918-3.024h0a2.26,2.26,0,0,0-2.775-1.515l-4.015,1.642a2.238,2.238,0,0,0-2.712-1.54l-8.557,3.5a3.15,3.15,0,0,0-.618-.93,2.221,2.221,0,0,0,.79-2.971h0a2.219,2.219,0,0,0-2.652-1.564l-3.751,1.534a2.991,2.991,0,0,0-1-1.3l15.97-6.533a2.244,2.244,0,0,0,.879-3.009h0a2.245,2.245,0,0,0-2.734-1.53l-11.792,4.824a3.14,3.14,0,0,0-.883-2.1l2.849-1.165a2.22,2.22,0,0,0,.8-2.975h0a2.219,2.219,0,0,0-2.653-1.564l-3.751,1.534c-.513-1.254-1.674-1.965-2.594-1.588l-12.1,4.949a2.366,2.366,0,0,0-.451.038,2.291,2.291,0,0,0-.447.131l-.262.107h0l-.073.03.024-9.471.049-.02h0l5.425-2.219a2.469,2.469,0,0,0,1.371-3.21h0a2.468,2.468,0,0,0-3.228-1.329l-3.605,1.474.026-10.177,3.208-1.312,3.158-1.292h0l28.514-11.662a2.754,2.754,0,0,0,1.792-3.425h0a2.754,2.754,0,0,0-3.68-1.189l-8.135,3.327a2.136,2.136,0,0,0-.91-2.128,2.751,2.751,0,0,0,1.781-3.42h0a2.753,2.753,0,0,0-3.679-1.188L421,232.164h0a2.3,2.3,0,0,0-1.241-1.223l25.332-10.361c1.582-.647,2.444-2.2,1.922-3.479s-2.226-1.781-3.809-1.134l-18.7,7.65a2.141,2.141,0,0,0-.91-2.128l4.52-1.848a2.755,2.755,0,0,0,1.792-3.426h0a2.753,2.753,0,0,0-3.678-1.188l-5.949,2.433h0a2.657,2.657,0,0,0-3.4-1.284l8.359-3.419a2.648,2.648,0,0,0,1.67-3.332h0a2.647,2.647,0,0,0-3.527-1.207l-7.728,3.16a2.163,2.163,0,0,0-.895-2.094,2.645,2.645,0,0,0,1.66-3.327h0a2.647,2.647,0,0,0-3.526-1.207l-1.624.664h0l-4.028,1.647h0a2.3,2.3,0,0,0-1.2-1.213l5.227-2.138h0l18.837-7.7a2.729,2.729,0,0,0,1.795-3.382h0a2.731,2.731,0,0,0-3.652-1.156l-17.766,7.267a2.166,2.166,0,0,0-.895-2.094l4.294-1.756a2.647,2.647,0,0,0,1.67-3.332h0a2.647,2.647,0,0,0-3.526-1.207l-.757.309h0l-4.9,2h0a2.168,2.168,0,0,0-.2-.379c-.023-.035-.049-.066-.074-.1a2.183,2.183,0,0,0-.2-.234c-.03-.031-.061-.06-.093-.089a2.358,2.358,0,0,0-.249-.2c-.028-.019-.055-.041-.084-.058a2.557,2.557,0,0,0-.374-.2h0l-.044-.018,0-.644.043-.018h0l6.171-2.524h0l22.479-9.193a2.648,2.648,0,0,0,1.67-3.332h0a2.648,2.648,0,0,0-3.527-1.207l-7.728,3.161a2.169,2.169,0,0,0-.9-2.1,2.645,2.645,0,0,0,1.66-3.327h0A2.648,2.648,0,0,0,421.4,171l-5.651,2.311h0a2.3,2.3,0,0,0-1.2-1.213l24.064-9.842a2.729,2.729,0,0,0,1.794-3.382h0a2.731,2.731,0,0,0-3.651-1.157l-17.767,7.267a2.163,2.163,0,0,0-.894-2.094l4.293-1.756a2.648,2.648,0,0,0,1.67-3.332h0a2.648,2.648,0,0,0-3.527-1.207l-5.651,2.311h0a2.593,2.593,0,0,0-3.439-1.243l-6.313,2.582.06-24.116,10.014-4.1a2.5,2.5,0,0,0,1.42-3.23h0a2.5,2.5,0,0,0-3.277-1.309l-6.985,2.857a2.359,2.359,0,0,0-.892-2.1,2.494,2.494,0,0,0,1.412-3.225h0a2.5,2.5,0,0,0-3.277-1.309l-5.107,2.089h0a2.433,2.433,0,0,0-1.141-1.237l21.751-8.9a2.46,2.46,0,1,0-1.857-4.539l-16.059,6.568a2.358,2.358,0,0,0-.891-2.1l3.88-1.587a2.5,2.5,0,0,0,1.421-3.23h0a2.5,2.5,0,0,0-3.277-1.309l-5.108,2.089h0a2.452,2.452,0,0,0-3.2-1.341l-45.337,18.542a2.558,2.558,0,0,0-2.208-.127l-1.524.624-1.781,0h0l-2.358-.006,21.166-8.657a2.46,2.46,0,1,0-1.857-4.539l-16.059,6.567a2.368,2.368,0,0,0-.891-2.1l3.88-1.587a2.5,2.5,0,0,0,1.42-3.23h0a2.5,2.5,0,0,0-3.276-1.309l-5.108,2.089h0a2.452,2.452,0,0,0-3.2-1.341l-.669.273a2.416,2.416,0,0,0,.893-2.97h0a2.427,2.427,0,0,0-2.564-1.522,2.539,2.539,0,0,0-.883-2.177l5.967-2.441a2.4,2.4,0,0,0,1.237-3.154h0a2.4,2.4,0,0,0-3.092-1.385l-.724.3h0a2.557,2.557,0,0,0-1.1-1.254l.027-.011a2.451,2.451,0,0,0,1.339-3.2h0a2.452,2.452,0,0,0-3.2-1.342l-4.741,1.939a2.544,2.544,0,0,0-.889-2.1l2.381-.974a2.454,2.454,0,0,0-1.856-4.539l-.724.3a2.363,2.363,0,0,0-3.02-1.414l-57.94,23.7a2.317,2.317,0,0,0,.589-2.854h0a2.223,2.223,0,0,0-2.667-1.558l-5.17,2.114a3.142,3.142,0,0,0-.23-1.1,3.111,3.111,0,0,0-.655-.994,2.226,2.226,0,0,0,.8-2.977h0a2.223,2.223,0,0,0-2.667-1.558l-3.781,1.546h0a2.971,2.971,0,0,0-1-1.294l16.1-6.585a2.25,2.25,0,0,0,.893-3.014h0a2.251,2.251,0,0,0-2.75-1.524l-11.886,4.861a3.105,3.105,0,0,0-.883-2.1l2.872-1.175a2.224,2.224,0,0,0,.81-2.98h0a2.223,2.223,0,0,0-2.667-1.558l-3.781,1.546h0c-.513-1.253-1.68-1.962-2.608-1.582l-93.889,38.4L75.5,128.6l-.081,32.649-.166.069h0l-6.171,2.524h0L27.169,180.988c-.928.379-1.264,1.7-.751,2.956l-.582.238a2.54,2.54,0,0,0,1.857,4.539l1.913-.782a3.1,3.1,0,0,0,.84,2.116l-3.807,1.557a2.511,2.511,0,0,0,1.857,4.539l.021-.009a2.982,2.982,0,0,0,.193,1.624l-.581.237a2.224,2.224,0,0,0-.81,2.98h0a2.223,2.223,0,0,0,2.667,1.558l4.792-1.96a3.1,3.1,0,0,0,.9,2.172,2.4,2.4,0,0,0-.435,2.749h0a2.222,2.222,0,0,0,2.666,1.558l11.322-4.631a2.386,2.386,0,0,0-.9,2.98h0l-4.708,1.925a2.4,2.4,0,0,0-1.236,3.154h0a2.4,2.4,0,0,0,3.092,1.385l3.576-1.463a2.535,2.535,0,0,0,.835,2.118l-14.8,6.054a2.451,2.451,0,0,0-1.339,3.2h0a2.452,2.452,0,0,0,3.2,1.342l20.045-8.2a2.554,2.554,0,0,0,.1,1.665h0l-4.708,1.925a2.4,2.4,0,0,0-1.236,3.154h0a2.4,2.4,0,0,0,3.083,1.387,2.543,2.543,0,0,0,.835,2.119l-6.438,2.633a2.4,2.4,0,0,0-1.236,3.154h0a2.4,2.4,0,0,0,3.092,1.384l21.41-8.756,3.367-1.377h0l.014-.007-.121,48.35L45.723,282.593a2.452,2.452,0,0,0-1.341,3.2h0l-.785.321a2.5,2.5,0,0,0-1.42,3.231h0a2.494,2.494,0,0,0,3.276,1.308l2.585-1.056a2.363,2.363,0,0,0,.832,2.119l-5.144,2.1a2.561,2.561,0,0,0-1.532,3.276h0a2.561,2.561,0,0,0,3.389,1.263l.029-.012a2.425,2.425,0,0,0,.052,1.682h0l-.786.321a2.5,2.5,0,0,0-1.42,3.231h0a2.5,2.5,0,0,0,3.277,1.308l6.475-2.647a2.366,2.366,0,0,0,.9,2.171,2.468,2.468,0,0,0-.9,2.941h0a2.5,2.5,0,0,0,3.277,1.308L69.084,303.5h0l2.828-1.157,3.162-1.293-.062,24.481-3.144,1.286a2.592,2.592,0,0,0-1.583,3.3h0l-.869.355a2.647,2.647,0,0,0-1.669,3.332h0a2.647,2.647,0,0,0,3.526,1.207l2.859-1.169a2.166,2.166,0,0,0,.829,2.12l-5.691,2.328a2.73,2.73,0,0,0-1.795,3.383h0a2.73,2.73,0,0,0,3.651,1.156l.032-.013a2.3,2.3,0,0,0,0,1.705h0l-.869.355a2.647,2.647,0,0,0-1.67,3.332h0a2.647,2.647,0,0,0,3.526,1.207l7.163-2.929a2.17,2.17,0,0,0,.9,2.17,2.546,2.546,0,0,0-1.1,3.02,2.613,2.613,0,0,0,3.34,1.265l-14.258,5.831a2.693,2.693,0,0,0-1.7,3.388l-.915.375a2.752,2.752,0,0,0-1.791,3.425h0a2.753,2.753,0,0,0,3.679,1.189l3.008-1.231a2.138,2.138,0,0,0,.842,2.156l-5.991,2.45A2.844,2.844,0,0,0,63.4,374c.521,1.274,2.226,1.781,3.81,1.135l.033-.015a2.3,2.3,0,0,0-.027,1.743h0l-.914.374a2.528,2.528,0,1,0,1.887,4.613l7.54-3.084a2.144,2.144,0,0,0,.912,2.207,2.62,2.62,0,0,0-1.188,3.1,2.755,2.755,0,0,0,3.679,1.188l27-11.044c-.008.009-.014.019-.021.028l-.9.367.638,0a2.5,2.5,0,0,0-.122,2.176h0l-.734.3a2.453,2.453,0,0,0,1.857,4.539l2.414-.987a2.51,2.51,0,0,0,.835,2.118l-4.807,1.966a2.468,2.468,0,0,0-1.371,3.209h0a2.468,2.468,0,0,0,3.228,1.329l.027-.011a2.531,2.531,0,0,0,.088,1.666h0l-.734.3a2.414,2.414,0,0,0-1.267,3.167h0a2.413,2.413,0,0,0,3.123,1.372l6.05-2.475a2.507,2.507,0,0,0,.9,2.172,2.431,2.431,0,0,0-.787,2.892h0a2.413,2.413,0,0,0,3.122,1.372l60.948-24.928,15.684.04h0l2.656.007h0a3.04,3.04,0,0,0,.116.363h0a2.246,2.246,0,0,0,2.735,1.53l.02-.009a3,3,0,0,0,.2,1.622l-.576.236a2.545,2.545,0,0,0,1.857,4.54l4.754-1.944a3.131,3.131,0,0,0,.21,1.095l-3.595,1.47a2.238,2.238,0,0,0-.855,3h0l-.618.252a2.259,2.259,0,0,0-.917,3.025h0a2.258,2.258,0,0,0,2.774,1.514l2.03-.83a2.941,2.941,0,0,0,.839,2.117l-4.043,1.653a2.292,2.292,0,0,0-1.006,3.061h0a2.292,2.292,0,0,0,2.863,1.478l.022-.009a2.842,2.842,0,0,0,.169,1.634h0l-.618.253a2.259,2.259,0,0,0-.917,3.024h0a2.259,2.259,0,0,0,2.774,1.515l5.089-2.082a2.932,2.932,0,0,0,.9,2.172,2.4,2.4,0,0,0-.518,2.783h0a2.259,2.259,0,0,0,2.774,1.515l57.567-23.546a4.42,4.42,0,0,0,.3,1.122,4.507,4.507,0,0,0,.548.974l-2.582,1.056c-.683.279-.821,1.522-.308,2.775h0c.513,1.253,1.482,2.043,2.164,1.764l.014-.006a4.11,4.11,0,0,0,.323,1.571l-.394.161c-.652.266-.764,1.5-.251,2.752h0c.513,1.253,1.456,2.054,2.108,1.787l3.25-1.329a4.5,4.5,0,0,0,.893,2.173,2.649,2.649,0,0,0-.006,2.573h0c.513,1.253,1.456,2.054,2.108,1.787l73.837-30.2,15.612.126.174-.09,35.83.09.053-21.259.266-.108h0l6.148-2.514h0l23.556-9.636c.652-.267.764-1.5.251-2.752h0c-.513-1.253-1.456-2.053-2.107-1.787l-3.508,1.435a4.5,4.5,0,0,0-.291-1.079,4.444,4.444,0,0,0-.586-1.022c.647-.27.758-1.5.246-2.749h0c-.513-1.254-1.456-2.054-2.108-1.787l-2.564,1.049h0a4.076,4.076,0,0,0-.87-1.347L434,327.333c.682-.279.82-1.522.307-2.775s-1.482-2.043-2.164-1.764l-8.063,3.3a4.457,4.457,0,0,0-.292-1.079,4.393,4.393,0,0,0-.575-1.007l33.592-13.739a2.259,2.259,0,0,0,.918-3.024h0a2.26,2.26,0,0,0-2.775-1.515l-5.49,2.246a2.973,2.973,0,0,0-.219-1.109,2.935,2.935,0,0,0-.667-.99,2.26,2.26,0,0,0,.911-3.02h0a2.261,2.261,0,0,0-2.775-1.515l-4.015,1.642a2.851,2.851,0,0,0-1.024-1.284l17.1-6.992a2.291,2.291,0,0,0,1.006-3.06Zm-67.113-26.8c-.027-.024-.047-.056-.074-.079l.074-.03Zm-.019,7.656,0,1.705-1.518.621h0a2.526,2.526,0,0,0-1.105-1.251ZM196.858,373.962c0,.027,0,.055,0,.082C196.856,374.017,196.856,373.99,196.858,373.962Zm.182-.823c-.02.046-.04.093-.057.142C197,373.232,197.02,373.186,197.041,373.139Zm-.127.4c-.008.038-.015.076-.022.115C196.9,373.614,196.906,373.577,196.913,373.538Zm.326-.758c-.036.051-.071.1-.1.156C197.167,372.881,197.2,372.831,197.24,372.78Zm.277-.308a1.876,1.876,0,0,0-.168.159A1.739,1.739,0,0,1,197.517,372.472Zm.43-.276a1.576,1.576,0,0,0-.325.183A1.576,1.576,0,0,1,197.946,372.2ZM357.3,179.809a2.339,2.339,0,0,0-.153-.62,2.437,2.437,0,0,0-.728-.966A2.5,2.5,0,0,0,357.83,175h0a2.5,2.5,0,0,0-3.277-1.309l-5.108,2.089h0a2.428,2.428,0,0,0-1.141-1.236l21.751-8.9a2.46,2.46,0,1,0-1.857-4.539l-16.058,6.568a2.368,2.368,0,0,0-.891-2.1l3.88-1.587a2.5,2.5,0,0,0,1.42-3.23h0a2.5,2.5,0,0,0-3.277-1.309l-5.108,2.089h0a2.439,2.439,0,0,0-1.623-1.427L391.816,141.6l1.15,0-.059,23.642ZM87.682,140.843l37.413.093L87.643,156.254ZM87.392,257.5a2.173,2.173,0,0,0,.958.228,2.2,2.2,0,1,0-.948-4.185l.09-36.334.021-.009v30.42l3.564-1.457a2.307,2.307,0,0,0,.168.962h0a2.562,2.562,0,0,0,3.389,1.263l.029-.012a2.433,2.433,0,0,0,.052,1.681h0l-.786.322a2.5,2.5,0,0,0-1.42,3.23h0a2.5,2.5,0,0,0,3.277,1.309l6.474-2.647a2.37,2.37,0,0,0,.9,2.171,2.468,2.468,0,0,0-.9,2.941h0a2.409,2.409,0,0,0,1.655,1.413l-16.535,6.763Zm-.157,63.036.019-7.573.493-.2a2.2,2.2,0,0,0,.133,1.012c.007.015.019.027.026.043.014.042.014.086.032.128a2.189,2.189,0,0,0,.645.843,3.184,3.184,0,0,0-.3.238c-.038.035-.069.074-.1.111a2.974,2.974,0,0,0-.207.222c-.037.046-.065.1-.1.145-.051.073-.106.146-.15.222-.031.054-.051.111-.078.167-.036.075-.075.149-.1.226-.022.06-.034.121-.05.181a2.379,2.379,0,0,0-.062.23c-.011.062-.012.127-.017.189a2.284,2.284,0,0,0-.018.229c0,.065.013.128.018.192a1.991,1.991,0,0,0,.025.227c.014.064.038.126.057.19a2.043,2.043,0,0,0,.07.218l0,0c.015.049.024.1.043.148a2.478,2.478,0,0,0,2.5,1.435ZM392.68,257.019l-.006,2.411-3.092,1.264h0a2.462,2.462,0,0,0-2.09-1.553ZM196.873,374.4c0,.015,0,.031.007.045C196.878,374.43,196.874,374.415,196.873,374.4Zm2.966-2.978,1.889-.772h0ZM392.426,359.1l-.011,4.02-9.758-.024Z" transform="translate(-18.758 -89.335)"/>
              <ellipse id="Ellipse_17" data-name="Ellipse 17" cx="2.204" cy="2.204" rx="2.204" ry="2.204" transform="translate(16.338 207.592)"/>
              <path id="Path_139" data-name="Path 139" d="M66.145,352.368a2.2,2.2,0,1,0-2.2,2.2A2.2,2.2,0,0,0,66.145,352.368Z" transform="translate(-20.032 -98.334)"/>
              <path id="Path_140" data-name="Path 140" d="M60.854,391.218a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,60.854,391.218Z" transform="translate(-19.926 -99.751)"/>
              <path id="Path_141" data-name="Path 141" d="M101.53,398.1a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,101.53,398.1Z" transform="translate(-21.329 -99.988)"/>
              <path id="Path_142" data-name="Path 142" d="M199.863,391.657a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,199.863,391.657Z" transform="translate(-24.722 -99.766)"/>
              <path id="Path_143" data-name="Path 143" d="M203.965,415.38a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,203.965,415.38Z" transform="translate(-24.863 -100.584)"/>
              <path id="Path_144" data-name="Path 144" d="M275.834,409.5a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,275.834,409.5Z" transform="translate(-27.343 -100.382)"/>
              <path id="Path_145" data-name="Path 145" d="M477.74,311.349a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,477.74,311.349Z" transform="translate(-34.309 -96.995)"/>
              <path id="Path_146" data-name="Path 146" d="M449.776,338.279a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,449.776,338.279Z" transform="translate(-33.345 -97.924)"/>
              <path id="Path_147" data-name="Path 147" d="M456.753,280.332a2.2,2.2,0,1,0,2.2-2.2A2.2,2.2,0,0,0,456.753,280.332Z" transform="translate(-33.661 -95.849)"/>
              <path id="Path_148" data-name="Path 148" d="M459.035,237.551a2.2,2.2,0,1,0,2.2-2.2A2.2,2.2,0,0,0,459.035,237.551Z" transform="translate(-33.74 -94.373)"/>
              <path id="Path_149" data-name="Path 149" d="M446.808,218.9a2.2,2.2,0,1,0,2.2-2.2A2.2,2.2,0,0,0,446.808,218.9Z" transform="translate(-33.318 -93.729)"/>
              <path id="Path_150" data-name="Path 150" d="M454.065,193.631a2.2,2.2,0,1,0-2.2,2.2A2.2,2.2,0,0,0,454.065,193.631Z" transform="translate(-33.416 -92.858)"/>
              <path id="Path_151" data-name="Path 151" d="M442.483,159.513a2.2,2.2,0,1,0-2.2-2.2A2.2,2.2,0,0,0,442.483,159.513Z" transform="translate(-33.093 -91.604)"/>
              <path id="Path_152" data-name="Path 152" d="M389.66,160.209a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,389.66,160.209Z" transform="translate(-31.27 -91.78)"/>
              <path id="Path_153" data-name="Path 153" d="M437.164,129.361a2.2,2.2,0,1,0-2.2-2.2A2.2,2.2,0,0,0,437.164,129.361Z" transform="translate(-32.909 -90.564)"/>
              <path id="Path_154" data-name="Path 154" d="M423.513,111.556a2.2,2.2,0,1,0-2.2-2.2A2.2,2.2,0,0,0,423.513,111.556Z" transform="translate(-32.438 -89.95)"/>
              <path id="Path_155" data-name="Path 155" d="M379.83,118.2a2.2,2.2,0,1,0-2.2-2.2A2.2,2.2,0,0,0,379.83,118.2Z" transform="translate(-30.931 -90.179)"/>
              <path id="Path_156" data-name="Path 156" d="M30.558,264.617a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,30.558,264.617Z" transform="translate(-18.881 -95.383)"/>
              <path id="Path_157" data-name="Path 157" d="M43.6,237.551a2.2,2.2,0,1,0-2.2,2.2A2.2,2.2,0,0,0,43.6,237.551Z" transform="translate(-19.255 -94.373)"/>
              <path id="Path_158" data-name="Path 158" d="M31.574,228.055a2.2,2.2,0,1,0-2.2,2.2A2.2,2.2,0,0,0,31.574,228.055Z" transform="translate(-18.84 -94.045)"/>
              <path id="Path_159" data-name="Path 159" d="M21.7,204.723a2.2,2.2,0,1,0,2.2,2.2A2.2,2.2,0,0,0,21.7,204.723Z" transform="translate(-18.575 -93.316)"/>
              <path id="Path_160" data-name="Path 160" d="M22.949,191.969a2.2,2.2,0,1,0-2.2,2.2A2.2,2.2,0,0,0,22.949,191.969Z" transform="translate(-18.542 -92.8)"/>
            </g>
            <g id="Group_89" data-name="Group 89" transform="translate(8.24)">
              <path id="Path_161" data-name="Path 161" d="M371.1,155.891l-16.059,6.568a2.365,2.365,0,0,0-.891-2.1l3.88-1.587a2.5,2.5,0,0,0,1.42-3.231h0a2.494,2.494,0,0,0-3.276-1.308l-5.108,2.089h0a2.452,2.452,0,0,0-3.2-1.341L48.621,277.376a2.452,2.452,0,0,0-1.341,3.2h0l-.786.322a2.5,2.5,0,0,0-1.42,3.23h0a2.5,2.5,0,0,0,3.277,1.309l2.584-1.057a2.363,2.363,0,0,0,.832,2.119l-5.144,2.1a2.562,2.562,0,0,0-1.532,3.276h0a2.562,2.562,0,0,0,3.389,1.264l.029-.013a2.436,2.436,0,0,0,.052,1.682h0l-.785.322a2.5,2.5,0,0,0-1.42,3.23h0a2.5,2.5,0,0,0,3.277,1.309l6.474-2.648a2.364,2.364,0,0,0,.9,2.171,2.466,2.466,0,0,0-.9,2.941h0a2.5,2.5,0,0,0,3.276,1.309L369.049,176.785a2.5,2.5,0,0,0,1.42-3.231h0a2.5,2.5,0,0,0-3.277-1.308l-6.985,2.857a2.359,2.359,0,0,0-.892-2.1,2.5,2.5,0,0,0,1.412-3.226h0a2.5,2.5,0,0,0-3.277-1.308l-5.107,2.089h0a2.433,2.433,0,0,0-1.141-1.237l21.751-8.9a2.562,2.562,0,0,0,1.532-3.276h0A2.561,2.561,0,0,0,371.1,155.891Z" transform="translate(-27.691 -84.118)" fill="#01b5d0"/>
              <path id="Path_162" data-name="Path 162" d="M413.577,259.054l-15,6.137a2.511,2.511,0,0,0-.889-2.1l3.625-1.483a2.412,2.412,0,0,0,1.267-3.167h0a2.413,2.413,0,0,0-3.122-1.372l-4.773,1.952h0a2.376,2.376,0,0,0-3.049-1.4L112.016,371.985a2.376,2.376,0,0,0-1.192,3.137h0l-.735.3a2.412,2.412,0,0,0-1.266,3.167h0a2.413,2.413,0,0,0,3.122,1.372l2.415-.988a2.506,2.506,0,0,0,.834,2.118l-4.806,1.966a2.468,2.468,0,0,0-1.371,3.21h0a2.468,2.468,0,0,0,3.228,1.328l.026-.011a2.534,2.534,0,0,0,.089,1.667h0l-.735.3a2.413,2.413,0,0,0-1.266,3.168h0a2.413,2.413,0,0,0,3.122,1.372l6.05-2.474a2.513,2.513,0,0,0,.9,2.171,2.433,2.433,0,0,0-.786,2.893h0a2.413,2.413,0,0,0,3.122,1.372L412.127,279.705a2.415,2.415,0,0,0,1.267-3.168h0a2.414,2.414,0,0,0-3.123-1.372l-6.527,2.67a2.509,2.509,0,0,0-.89-2.1,2.413,2.413,0,0,0,1.258-3.163h0a2.413,2.413,0,0,0-3.122-1.372l-4.773,1.952h0a2.533,2.533,0,0,0-1.106-1.25l20.324-8.313a2.468,2.468,0,0,0,1.371-3.21h0A2.468,2.468,0,0,0,413.577,259.054Z" transform="translate(-29.89 -87.667)" fill="#01b5d0"/>
              <path id="Path_163" data-name="Path 163" d="M444.6,273.548l-11.792,4.823a3.127,3.127,0,0,0-.883-2.1l2.849-1.165a2.218,2.218,0,0,0,.8-2.975h0a2.219,2.219,0,0,0-2.652-1.564l-3.751,1.534h0c-.513-1.253-1.674-1.964-2.6-1.588L206.852,360.381c-.92.376-1.251,1.7-.739,2.952h0l-.576.236a2.219,2.219,0,0,0-.8,2.975h0a2.22,2.22,0,0,0,2.653,1.564l1.9-.776a3.133,3.133,0,0,0,.84,2.116l-3.777,1.545a2.246,2.246,0,0,0-.879,3.009h0a2.245,2.245,0,0,0,2.735,1.53l.021-.009a2.992,2.992,0,0,0,.2,1.623h0l-.576.236a2.219,2.219,0,0,0-.8,2.975h0a2.219,2.219,0,0,0,2.652,1.564l4.754-1.945a3.126,3.126,0,0,0,.9,2.172,2.4,2.4,0,0,0-.426,2.745h0a2.22,2.22,0,0,0,2.653,1.564l227.384-93a2.219,2.219,0,0,0,.8-2.975h0a2.219,2.219,0,0,0-2.652-1.564l-5.13,2.1a3.131,3.131,0,0,0-.883-2.1,2.222,2.222,0,0,0,.79-2.971h0a2.22,2.22,0,0,0-2.653-1.564l-3.751,1.534h0a2.99,2.99,0,0,0-1-1.3l15.971-6.532a2.246,2.246,0,0,0,.879-3.008h0A2.245,2.245,0,0,0,444.6,273.548Z" transform="translate(-33.198 -88.133)" fill="#01b5d0"/>
              <path id="Path_164" data-name="Path 164" d="M81.216,314.031c.012.028.031.051.043.078l3.434,1.13.048,0,7.163-2.929a2.172,2.172,0,0,0,.9,2.171,3.214,3.214,0,0,0-.368.291c-.038.035-.068.074-.1.111a2.8,2.8,0,0,0-.207.222c-.038.046-.066.1-.1.145-.052.074-.106.146-.151.223-.03.054-.051.111-.077.166a2.37,2.37,0,0,0-.1.226c-.022.06-.033.121-.05.182a2.352,2.352,0,0,0-.061.229c-.012.063-.012.126-.018.189a2.276,2.276,0,0,0-.017.229c0,.065.012.128.017.193a2.241,2.241,0,0,0,.026.226c.013.064.037.127.056.19a2.219,2.219,0,0,0,.07.218c.006.013.014.024.02.037h0a2.655,2.655,0,0,0,3.5,1.149L146.2,297.844S81.147,313.863,81.216,314.031Z" transform="translate(-28.944 -89.08)" fill="#ccc" opacity="0.5"/>
              <path id="Path_165" data-name="Path 165" d="M450.672,211.781c-.52-1.274-2.227-1.782-3.809-1.134l-18.7,7.649a2.136,2.136,0,0,0-.91-2.128l4.52-1.848a2.753,2.753,0,0,0,1.792-3.426h0a2.753,2.753,0,0,0-3.678-1.188l-5.949,2.433h0a2.657,2.657,0,0,0-3.4-1.284l8.36-3.419a2.647,2.647,0,0,0,1.669-3.332h0a2.647,2.647,0,0,0-3.526-1.207l-7.729,3.161a2.164,2.164,0,0,0-.895-2.1,2.647,2.647,0,0,0,1.661-3.327h0a2.648,2.648,0,0,0-3.527-1.207l-5.651,2.311h0a2.3,2.3,0,0,0-1.2-1.213l24.064-9.841a2.731,2.731,0,0,0,1.794-3.383h0a2.73,2.73,0,0,0-3.651-1.156l-17.767,7.266a2.165,2.165,0,0,0-.894-2.094l4.294-1.756a2.649,2.649,0,0,0,1.67-3.332h0a2.648,2.648,0,0,0-3.527-1.207l-5.651,2.311h0a2.468,2.468,0,0,0-2.442-1.458L437.4,173.685a2.647,2.647,0,0,0,1.67-3.332h0a2.647,2.647,0,0,0-3.526-1.207l-7.729,3.161a2.163,2.163,0,0,0-.9-2.1,2.645,2.645,0,0,0,1.66-3.327h0a2.647,2.647,0,0,0-3.526-1.206l-5.651,2.311h0a2.3,2.3,0,0,0-1.2-1.213l24.063-9.842a2.73,2.73,0,0,0,1.795-3.383h0a2.73,2.73,0,0,0-3.651-1.156l-17.767,7.266a2.165,2.165,0,0,0-.894-2.094l4.294-1.756a2.647,2.647,0,0,0,1.669-3.332h0a2.647,2.647,0,0,0-3.526-1.207l-5.652,2.311h0a2.592,2.592,0,0,0-3.438-1.243L84.034,287.749a2.592,2.592,0,0,0-1.582,3.3h0l-.869.355a2.647,2.647,0,0,0-1.67,3.332h0a2.647,2.647,0,0,0,3.526,1.207L86.3,294.77a2.169,2.169,0,0,0,.829,2.121l-5.691,2.328a2.73,2.73,0,0,0-1.795,3.382h0a2.731,2.731,0,0,0,3.652,1.157l.032-.014a2.3,2.3,0,0,0-.006,1.705h0l-.869.355a2.474,2.474,0,1,0,1.857,4.539l7.162-2.929a2.171,2.171,0,0,0,.9,2.171,2.543,2.543,0,0,0-1.1,3.019,2.479,2.479,0,0,0,2.5,1.436L75.525,321.5a2.592,2.592,0,0,0-1.582,3.3h0l-.869.355a2.648,2.648,0,0,0-1.67,3.332h0a2.648,2.648,0,0,0,3.527,1.207l2.858-1.169a2.169,2.169,0,0,0,.829,2.121l-5.691,2.327a2.73,2.73,0,0,0-1.794,3.383h0a2.73,2.73,0,0,0,3.651,1.156l.032-.013a2.3,2.3,0,0,0-.006,1.705h0l-.869.355a2.649,2.649,0,0,0-1.67,3.333h0a2.648,2.648,0,0,0,3.527,1.206l7.163-2.929a2.168,2.168,0,0,0,.9,2.171,2.544,2.544,0,0,0-1.1,3.019,2.612,2.612,0,0,0,3.339,1.265l-14.257,5.832a2.693,2.693,0,0,0-1.7,3.387l-.914.375a2.752,2.752,0,0,0-1.792,3.425h0a2.754,2.754,0,0,0,3.679,1.188l3.009-1.231a2.136,2.136,0,0,0,.842,2.156l-5.991,2.45c-1.583.647-2.444,2.2-1.922,3.479s2.226,1.781,3.809,1.134l.034-.013a2.3,2.3,0,0,0-.028,1.742h0l-.914.375a2.528,2.528,0,1,0,1.887,4.613l7.541-3.084a2.143,2.143,0,0,0,.911,2.206,2.619,2.619,0,0,0-1.187,3.1,2.754,2.754,0,0,0,3.679,1.189L443.438,232.433a2.753,2.753,0,0,0,1.792-3.426h0a2.753,2.753,0,0,0-3.679-1.188l-8.135,3.327a2.137,2.137,0,0,0-.911-2.129,2.75,2.75,0,0,0,1.781-3.42h0a2.753,2.753,0,0,0-3.679-1.188l-5.948,2.433h0a2.3,2.3,0,0,0-1.24-1.224l25.331-10.36C450.333,214.612,451.193,213.055,450.672,211.781Z" transform="translate(-28.45 -84.015)" fill="#01b5d0"/>
              <path id="Path_166" data-name="Path 166" d="M465.457,289.526l-12.622,5.162a2.969,2.969,0,0,0-.218-1.109,2.935,2.935,0,0,0-.667-.989L455,291.343a2.26,2.26,0,0,0,.917-3.025h0a2.26,2.26,0,0,0-2.774-1.515l-4.015,1.642h0a2.237,2.237,0,0,0-2.711-1.54l-235.2,96.2a2.238,2.238,0,0,0-.855,3h0l-.617.253a2.258,2.258,0,0,0-.918,3.024h0a2.26,2.26,0,0,0,2.775,1.515l2.03-.83a2.929,2.929,0,0,0,.839,2.116l-4.044,1.654a2.293,2.293,0,0,0-1.006,3.061h0a2.294,2.294,0,0,0,2.863,1.478l.022-.01a2.85,2.85,0,0,0,.169,1.634h0l-.617.252a2.258,2.258,0,0,0-.918,3.024h0a2.259,2.259,0,0,0,2.775,1.514l5.088-2.081a2.937,2.937,0,0,0,.9,2.171,2.4,2.4,0,0,0-.518,2.783h0a2.258,2.258,0,0,0,2.774,1.515l243.4-99.549a2.26,2.26,0,0,0,.918-3.025h0a2.258,2.258,0,0,0-2.774-1.514l-5.491,2.245a2.932,2.932,0,0,0-.885-2.1,2.261,2.261,0,0,0,.91-3.021h0a2.258,2.258,0,0,0-2.774-1.514l-4.015,1.641h0a2.85,2.85,0,0,0-1.025-1.283l17.1-6.992A2.293,2.293,0,0,0,468.319,291h0A2.293,2.293,0,0,0,465.457,289.526Z" transform="translate(-33.339 -88.695)" fill="#01b5d0"/>
              <path id="Path_167" data-name="Path 167" d="M443.091,323.244l-8.063,3.3a4.484,4.484,0,0,0-.876-2.1l1.948-.8c.651-.266.763-1.5.25-2.752h0c-.513-1.254-1.456-2.054-2.107-1.787l-2.564,1.049h0c-.512-1.253-1.438-2.06-2.067-1.8L279.373,379.8c-.629.257-.723,1.482-.211,2.735h0l-.395.161c-.651.266-.764,1.5-.251,2.752h0c.513,1.253,1.456,2.054,2.108,1.787l1.3-.531a4.5,4.5,0,0,0,.848,2.113l-2.583,1.056c-.683.279-.82,1.522-.307,2.775h0c.513,1.253,1.481,2.044,2.164,1.764l.015-.006a4.07,4.07,0,0,0,.323,1.571h0l-.395.161c-.651.266-.764,1.5-.251,2.752h0c.513,1.253,1.456,2.054,2.108,1.787l3.251-1.329a4.4,4.4,0,0,0,.3,1.139,4.456,4.456,0,0,0,.594,1.034,2.647,2.647,0,0,0,0,2.573h0c.513,1.253,1.456,2.054,2.107,1.787l155.477-63.59c.651-.266.764-1.5.251-2.752h0c-.513-1.254-1.456-2.054-2.108-1.787l-3.507,1.434a4.478,4.478,0,0,0-.292-1.078,4.42,4.42,0,0,0-.586-1.023c.647-.27.758-1.5.247-2.749h0c-.513-1.253-1.457-2.054-2.108-1.787L434.9,333.6h0a4.1,4.1,0,0,0-.87-1.347l10.92-4.466c.683-.279.82-1.522.307-2.775h0C444.742,323.755,443.774,322.965,443.091,323.244Z" transform="translate(-35.74 -89.786)" fill="#01b5d0"/>
              <path id="Path_168" data-name="Path 168" d="M95.028,253.668a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,95.028,253.668Z" transform="translate(-29.268 -87.48)" fill="#01b5d0"/>
              <path id="Path_169" data-name="Path 169" d="M42.154,298.833a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,42.154,298.833Z" transform="translate(-27.444 -89.038)" fill="#01b5d0"/>
              <path id="Path_170" data-name="Path 170" d="M68.426,344.652a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,68.426,344.652Z" transform="translate(-28.351 -90.619)" fill="#01b5d0"/>
              <path id="Path_171" data-name="Path 171" d="M65.34,385.706a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,65.34,385.706Z" transform="translate(-28.244 -92.035)" fill="#01b5d0"/>
              <path id="Path_172" data-name="Path 172" d="M106.017,392.591a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,106.017,392.591Z" transform="translate(-29.648 -92.273)" fill="#01b5d0"/>
              <path id="Path_173" data-name="Path 173" d="M204.35,386.145a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,204.35,386.145Z" transform="translate(-33.04 -92.05)" fill="#01b5d0"/>
              <path id="Path_174" data-name="Path 174" d="M208.452,409.868a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,208.452,409.868Z" transform="translate(-33.182 -92.869)" fill="#01b5d0"/>
              <path id="Path_175" data-name="Path 175" d="M280.32,403.987a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,280.32,403.987Z" transform="translate(-35.661 -92.666)" fill="#01b5d0"/>
              <path id="Path_176" data-name="Path 176" d="M482.227,305.836a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,482.227,305.836Z" transform="translate(-42.628 -89.28)" fill="#01b5d0"/>
              <path id="Path_177" data-name="Path 177" d="M454.263,332.767a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,454.263,332.767Z" transform="translate(-41.663 -90.209)" fill="#01b5d0"/>
              <path id="Path_178" data-name="Path 178" d="M463.442,272.616a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,463.442,272.616Z" transform="translate(-41.98 -88.133)" fill="#01b5d0"/>
              <path id="Path_179" data-name="Path 179" d="M465.724,229.836a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,465.724,229.836Z" transform="translate(-42.058 -86.657)" fill="#01b5d0"/>
              <path id="Path_180" data-name="Path 180" d="M453.5,211.18a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,453.5,211.18Z" transform="translate(-41.636 -86.014)" fill="#01b5d0"/>
              <path id="Path_181" data-name="Path 181" d="M456.346,185.916a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,456.346,185.916Z" transform="translate(-41.735 -85.142)" fill="#01b5d0"/>
              <path id="Path_182" data-name="Path 182" d="M446.97,149.593a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,446.97,149.593Z" transform="translate(-41.411 -83.889)" fill="#01b5d0"/>
              <path id="Path_183" data-name="Path 183" d="M394.147,154.7a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,394.147,154.7Z" transform="translate(-39.589 -84.065)" fill="#01b5d0"/>
              <path id="Path_184" data-name="Path 184" d="M441.651,119.442a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,441.651,119.442Z" transform="translate(-41.228 -82.849)" fill="#01b5d0"/>
              <path id="Path_185" data-name="Path 185" d="M428,101.637a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,428,101.637Z" transform="translate(-40.757 -82.234)" fill="#01b5d0"/>
              <path id="Path_186" data-name="Path 186" d="M384.316,108.284a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,384.316,108.284Z" transform="translate(-39.25 -82.464)" fill="#01b5d0"/>
              <path id="Path_187" data-name="Path 187" d="M421.9,104.137l-16.059,6.568a2.368,2.368,0,0,0-.891-2.1l3.88-1.587a2.5,2.5,0,0,0,1.42-3.23h0a2.5,2.5,0,0,0-3.277-1.309l-5.107,2.089h0a2.451,2.451,0,0,0-3.2-1.341L99.425,225.622a2.452,2.452,0,0,0-1.341,3.2h0l-.785.321a2.5,2.5,0,0,0-1.42,3.231h0a2.494,2.494,0,0,0,3.276,1.308l2.585-1.056a2.363,2.363,0,0,0,.832,2.119l-5.144,2.1a2.562,2.562,0,0,0-1.532,3.276h0a2.561,2.561,0,0,0,3.389,1.263l.029-.012a2.43,2.43,0,0,0,.052,1.682h0l-.786.321a2.5,2.5,0,0,0-1.42,3.231h0a2.5,2.5,0,0,0,3.277,1.309l6.475-2.648a2.366,2.366,0,0,0,.9,2.171,2.468,2.468,0,0,0-.9,2.941h0a2.5,2.5,0,0,0,3.277,1.308L419.854,125.03a2.5,2.5,0,0,0,1.42-3.23h0A2.5,2.5,0,0,0,418,120.491l-6.985,2.857a2.365,2.365,0,0,0-.892-2.1,2.5,2.5,0,0,0,1.412-3.226h0a2.5,2.5,0,0,0-3.277-1.309l-5.109,2.089h0a2.427,2.427,0,0,0-1.141-1.236l21.751-8.9a2.562,2.562,0,0,0,1.533-3.276h0A2.562,2.562,0,0,0,421.9,104.137Z" transform="translate(-29.444 -82.333)" fill="#01b5d0"/>
              <path id="Path_188" data-name="Path 188" d="M343.148,95.456h0a2.4,2.4,0,0,0-3.093-1.384l-.724.3h0a2.552,2.552,0,0,0-1.1-1.253l.026-.011a2.452,2.452,0,0,0,1.34-3.2h0a2.451,2.451,0,0,0-3.2-1.341L331.663,90.5a2.536,2.536,0,0,0-.888-2.1l2.381-.974a2.455,2.455,0,0,0-1.857-4.539l-.723.3a2.362,2.362,0,0,0-3.02-1.413l-57.941,23.7a2.32,2.32,0,0,0,.59-2.855h0a2.223,2.223,0,0,0-2.667-1.558l-5.17,2.114a3.11,3.11,0,0,0-.884-2.1,2.225,2.225,0,0,0,.8-2.977h0a2.223,2.223,0,0,0-2.667-1.558l-3.781,1.546h0a2.974,2.974,0,0,0-1-1.294l16.1-6.585a2.25,2.25,0,0,0,.893-3.014h0a2.251,2.251,0,0,0-2.75-1.525l-11.886,4.861a3.1,3.1,0,0,0-.883-2.1l2.872-1.175a2.223,2.223,0,0,0,.81-2.98h0a2.222,2.222,0,0,0-2.666-1.558l-3.781,1.547h0c-.513-1.253-1.681-1.962-2.609-1.582L29.451,173.272c-.927.379-1.264,1.7-.751,2.956l-.581.238A2.54,2.54,0,0,0,29.975,181l1.912-.782a3.1,3.1,0,0,0,.841,2.116L28.92,183.9a2.511,2.511,0,0,0,1.857,4.539l.021-.009a2.973,2.973,0,0,0,.194,1.624l-.581.238a2.223,2.223,0,0,0-.81,2.98h0a2.222,2.222,0,0,0,2.666,1.558l4.793-1.96a3.1,3.1,0,0,0,.894,2.172,2.4,2.4,0,0,0-.435,2.749h0a2.224,2.224,0,0,0,2.667,1.559l11.321-4.631a2.387,2.387,0,0,0-.894,2.98h0L45.9,199.62a2.4,2.4,0,0,0-1.237,3.155h0a2.4,2.4,0,0,0,3.092,1.384l3.577-1.463a2.539,2.539,0,0,0,.834,2.118l-14.8,6.054a2.45,2.45,0,0,0-1.34,3.2h0a2.452,2.452,0,0,0,3.2,1.342l20.046-8.2a2.559,2.559,0,0,0,.095,1.665h0L54.66,210.8a2.4,2.4,0,0,0-1.236,3.155h0a2.4,2.4,0,0,0,3.084,1.386,2.536,2.536,0,0,0,.835,2.119l-6.438,2.633a2.4,2.4,0,0,0-1.237,3.155h0a2.4,2.4,0,0,0,3.093,1.385L338.156,107.9a2.4,2.4,0,0,0,1.237-3.154h0a2.429,2.429,0,0,0-2.565-1.522,2.542,2.542,0,0,0-.883-2.177l5.967-2.44A2.4,2.4,0,0,0,343.148,95.456Z" transform="translate(-27.076 -81.62)" fill="#01b5d0"/>
            </g>
            <path id="Path_189" data-name="Path 189" d="M35.044,259.105a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,35.044,259.105Z" transform="translate(-18.959 -87.667)" fill="#01b5d0"/>
            <path id="Path_190" data-name="Path 190" d="M45.885,229.836a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,45.885,229.836Z" transform="translate(-19.333 -86.657)" fill="#01b5d0"/>
            <path id="Path_191" data-name="Path 191" d="M33.857,220.339a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,33.857,220.339Z" transform="translate(-18.918 -86.33)" fill="#01b5d0"/>
            <path id="Path_192" data-name="Path 192" d="M26.181,199.21a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,26.181,199.21Z" transform="translate(-18.654 -85.601)" fill="#01b5d0"/>
            <path id="Path_193" data-name="Path 193" d="M25.231,184.254a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,25.231,184.254Z" transform="translate(-18.621 -85.085)" fill="#01b5d0"/>
            <g id="Group_92" data-name="Group 92" transform="translate(58.457 39.268)">
              <path id="Path_194" data-name="Path 194" d="M233.6,122.674,79.58,122.29l-.492,197.6,12.222-5,.448-180.365,112.17.279Z" transform="translate(-79.088 -122.29)" fill="#ffd438"/>
              <path id="Path_195" data-name="Path 195" d="M314.742,122.878l-29.672,12.136,119.08.3L403.6,356.824l-10.18-.025-29.673,12.136,52.031.129.612-245.934Z" transform="translate(-86.195 -122.311)" fill="#ffd438"/>
              <g id="Group_90" data-name="Group 90" transform="translate(328.184 0.815)">
                <path id="Path_196" data-name="Path 196" d="M419,123.135V369.07l1.4,0,.612-245.934Z" transform="translate(-419 -123.135)" fill="#ffe588"/>
              </g>
              <g id="Group_91" data-name="Group 91" transform="translate(223.048 0.567)">
                <path id="Path_197" data-name="Path 197" d="M310.107,125.127H417.249l0-2-101.648-.253Z" transform="translate(-310.107 -122.878)" fill="#ffe588"/>
              </g>
              <path id="Path_198" data-name="Path 198" d="M285.071,135.34l119.08.3L403.6,357.151l-10.18-.025-6.262,2.561h20.3V132.341H292.4Z" transform="translate(-86.195 -122.637)" fill="#0794a3" opacity="0.3"/>
              <path id="Path_199" data-name="Path 199" d="M203.106,374.239l3.777-1.545a3.144,3.144,0,0,1-.607-.95,3.109,3.109,0,0,1-.233-1.166l-1.9.776a2.22,2.22,0,0,1-2.653-1.564h0a2.219,2.219,0,0,1,.8-2.975l.576-.236a2.888,2.888,0,0,1-.133-1.9l-62.694-.156-29.672,12.136,91.745.228A2.15,2.15,0,0,1,203.106,374.239Z" transform="translate(-80.167 -130.648)" fill="#ffd438"/>
            </g>
            <g id="Group_93" data-name="Group 93" transform="translate(88.657 39.835)">
              <path id="Path_200" data-name="Path 200" d="M354.726,123.587a2.344,2.344,0,0,0-.376-.61l-39.608-.1-29.672,12.135,73.194.182,5.776.01a7.339,7.339,0,0,0,1.894.052,5.217,5.217,0,0,0,1.746-1.314c2.168-1.86,7.3-4.387,4.822-7.989a2.563,2.563,0,0,0-2.926-.685l-6.985,2.857a2.365,2.365,0,0,0-.892-2.1A2.5,2.5,0,0,0,363.167,123l-6.96-.017Z" transform="translate(-116.394 -122.878)" fill="#b29115"/>
              <path id="Path_201" data-name="Path 201" d="M209.432,371.749a3.11,3.11,0,0,1-.233-1.166l-1.9.776a2.545,2.545,0,0,1-1.857-4.539l.576-.236a2.889,2.889,0,0,1-.133-1.9l-1.842,0a2.28,2.28,0,0,0-.584,2.862l-.576.236a2.22,2.22,0,0,0-.8,2.975,2.651,2.651,0,0,0,1.729,1.629c.749.168,1.7-.576,2.326.122a.9.9,0,0,1,.1,1.033,1.769,1.769,0,0,1-.867.617c-1.273.558-2.638,1.123-2.763,2.733l2.656.007a2.15,2.15,0,0,1,.994-2.646l3.777-1.545A3.143,3.143,0,0,1,209.432,371.749Z" transform="translate(-113.523 -131.221)" fill="#ccc"/>
              <path id="Path_202" data-name="Path 202" d="M396.231,365.158h9.2l-22.845,12.265-16.027-.129Z" transform="translate(-119.206 -131.237)" fill="#b29115"/>
              <path id="Path_203" data-name="Path 203" d="M150.645,364.548,129.1,376.7l-18.732-.047,29.672-12.136Z" transform="translate(-110.367 -131.215)" fill="#b29115"/>
            </g>
            <path id="Path_204" data-name="Path 204" d="M368.03,110.5a2.561,2.561,0,0,0-3.389-1.263l-16.059,6.568a2.358,2.358,0,0,0-.891-2.1l3.881-1.587a2.5,2.5,0,0,0,1.42-3.231h0a2.5,2.5,0,0,0-3.277-1.308l-5.109,2.088h0a2.452,2.452,0,0,0-3.2-1.341L92.036,210.329v30.464L362.6,130.135a2.5,2.5,0,0,0,1.421-3.231h0a2.494,2.494,0,0,0-3.276-1.308l-6.985,2.857a2.365,2.365,0,0,0-.892-2.1,2.5,2.5,0,0,0,1.412-3.226h0A2.5,2.5,0,0,0,351,121.822l-5.108,2.089h0a2.436,2.436,0,0,0-1.141-1.236l21.751-8.9A2.561,2.561,0,0,0,368.03,110.5Z" transform="translate(-21.078 -82.509)" fill="#01b5d0"/>
            <g id="Group_94" data-name="Group 94" transform="translate(118.117 51.787)">
              <path id="Path_205" data-name="Path 205" d="M206.06,135.257,140.88,161.583l48.867-26.326Z" transform="translate(-140.88 -135.257)" fill="#0794a3"/>
              <path id="Path_206" data-name="Path 206" d="M141.1,363.671l58.376-23.788L151.705,363.7Z" transform="translate(-140.887 -142.317)" fill="#0794a3"/>
              <path id="Path_207" data-name="Path 207" d="M358.934,135.636,303.513,158.3l73.574-22.622Z" transform="translate(-146.491 -135.27)" fill="#0794a3"/>
            </g>
            <g id="Group_95" data-name="Group 95" transform="translate(388.358 171.199)" opacity="0.5">
              <path id="Path_208" data-name="Path 208" d="M427.574,260.453h0a2.468,2.468,0,0,0-3.228-1.329l-3.569,1.46v5.3l5.425-2.219A2.468,2.468,0,0,0,427.574,260.453Z" transform="translate(-420.778 -258.936)" fill="#ccc"/>
            </g>
            <g id="Group_97" data-name="Group 97" transform="translate(52.53 68.4)">
              <g id="Group_96" data-name="Group 96">
                <path id="Path_209" data-name="Path 209" d="M427.141,152.463l-6.171,2.524v30.464l6.171-2.524Z" transform="translate(-84.957 -152.463)" fill="#0794a3" opacity="0.5"/>
                <path id="Path_210" data-name="Path 210" d="M427.141,201.32l-4.028,1.647h0a2.3,2.3,0,0,0-1.2-1.213l5.227-2.138v-5.3l-.787.322a2.165,2.165,0,0,0-.894-2.094l1.681-.687v-5.3l-4.9,2h0a2.306,2.306,0,0,0-1.276-1.253v60.536l6.171-2.524Z" transform="translate(-84.957 -153.64)" fill="#0794a3" opacity="0.5"/>
                <path id="Path_211" data-name="Path 211" d="M426.926,273.53l-4.989,2.041a2.288,2.288,0,0,0-.9.168l-.262.107v77.049l6.148-2.514Z" transform="translate(-84.95 -156.64)" fill="#0794a3" opacity="0.5"/>
                <path id="Path_212" data-name="Path 212" d="M79.121,156.177,72.949,158.7v60.893l6.171-2.524Z" transform="translate(-72.949 -152.592)" fill="#0794a3" opacity="0.5"/>
                <path id="Path_213" data-name="Path 213" d="M72.949,302.261l6.171-2.524V269.273L72.949,271.8Z" transform="translate(-72.949 -156.494)" fill="#0794a3" opacity="0.5"/>
              </g>
            </g>
            <path id="Path_214" data-name="Path 214" d="M203.515,364.677l-62.694-.156-7.766,3.176h69.174a1.584,1.584,0,0,1,.843-.882l.576-.236A2.889,2.889,0,0,1,203.515,364.677Z" transform="translate(-22.493 -91.38)" fill="#0794a3" opacity="0.3"/>
            <path id="Path_215" data-name="Path 215" d="M88.632,132.341V316.472l3.007-1.23.448-180.365,112.17.279,6.882-2.815Z" transform="translate(-20.96 -83.37)" fill="#0794a3" opacity="0.3"/>
            <path id="Path_216" data-name="Path 216" d="M79.59,125.107H227.668l5.95-2.433L79.6,122.29Z" transform="translate(-20.648 -83.023)" fill="#ffe588"/>
          </g>
        </g>
        <g id="Group_106" data-name="Group 106" transform="translate(789 895.171)">
          <path id="Path_217" data-name="Path 217" d="M50.125,135.522a3.182,3.182,0,1,1-3.182-3.181A3.182,3.182,0,0,1,50.125,135.522Z" transform="translate(-3.441 -37.971)" fill="#fff"/>
          <path id="Path_218" data-name="Path 218" d="M124.823,73.254a4.989,4.989,0,1,1-4.989-4.989A4.989,4.989,0,0,1,124.823,73.254Z" transform="translate(-5.893 -35.761)" fill="#fff"/>
          <ellipse id="Ellipse_18" data-name="Ellipse 18" cx="2.492" cy="2.492" rx="2.492" ry="2.492" transform="translate(157.054 19.153)" fill="#fff"/>
          <ellipse id="Ellipse_19" data-name="Ellipse 19" cx="4.184" cy="4.184" rx="4.184" ry="4.184" transform="translate(56.643 37.493)" fill="#fff"/>
          <path id="Path_219" data-name="Path 219" d="M122.207,94.036a1.77,1.77,0,1,1-1.77-1.771A1.77,1.77,0,0,1,122.207,94.036Z" transform="translate(-6.025 -36.589)" fill="#fff"/>
          <path id="Path_220" data-name="Path 220" d="M167.159,84.844a1.246,1.246,0,1,1-1.246-1.245A1.246,1.246,0,0,1,167.159,84.844Z" transform="translate(-7.612 -36.29)" fill="#fff"/>
          <path id="Path_221" data-name="Path 221" d="M54.007,108.554A4.556,4.556,0,1,1,49.452,104,4.555,4.555,0,0,1,54.007,108.554Z" transform="translate(-3.48 -36.993)" fill="#fff"/>
          <path id="Path_222" data-name="Path 222" d="M91.919,113.357a1.643,1.643,0,1,1-1.643-1.643A1.643,1.643,0,0,1,91.919,113.357Z" transform="translate(-4.989 -37.26)" fill="#fff"/>
          <path id="Path_223" data-name="Path 223" d="M266.306,426.251a2.32,2.32,0,1,1-2.32-2.319A2.32,2.32,0,0,1,266.306,426.251Z" transform="translate(-10.959 -48.032)" fill="#fff"/>
          <path id="Path_224" data-name="Path 224" d="M330.02,433.747a5.01,5.01,0,1,1-5.01-5.01A5.01,5.01,0,0,1,330.02,433.747Z" transform="translate(-12.972 -48.198)" fill="#fff"/>
          <ellipse id="Ellipse_20" data-name="Ellipse 20" cx="3.825" cy="3.825" rx="3.825" ry="3.825" transform="translate(338.964 361.544)" fill="#fff"/>
          <path id="Path_225" data-name="Path 225" d="M394.942,412.586a.8.8,0,1,1-.8-.8A.8.8,0,0,1,394.942,412.586Z" transform="translate(-15.502 -47.613)" fill="#fff"/>
          <ellipse id="Ellipse_21" data-name="Ellipse 21" cx="1.703" cy="1.703" rx="1.703" ry="1.703" transform="translate(395.695 380.539)" fill="#fff"/>
          <ellipse id="Ellipse_22" data-name="Ellipse 22" cx="1.931" cy="1.931" rx="1.931" ry="1.931" transform="translate(360.774 353.166)" fill="#fff"/>
          <path id="Path_226" data-name="Path 226" d="M434.669,388.392a2.168,2.168,0,1,1-2.168-2.168A2.167,2.167,0,0,1,434.669,388.392Z" transform="translate(-16.778 -46.731)" fill="#fff"/>
          <path id="Path_227" data-name="Path 227" d="M424.267,405.02a1.745,1.745,0,1,1-1.745-1.744A1.745,1.745,0,0,1,424.267,405.02Z" transform="translate(-16.449 -47.319)" fill="#fff"/>
          <ellipse id="Ellipse_23" data-name="Ellipse 23" cx="1.05" cy="1.05" rx="1.05" ry="1.05" transform="translate(454.106 359.445)" fill="#fff"/>
          <path id="Path_228" data-name="Path 228" d="M462.387,426.941a1.676,1.676,0,1,1-1.676-1.676A1.676,1.676,0,0,1,462.387,426.941Z" transform="translate(-17.769 -48.078)" fill="#fff"/>
          <path id="Path_229" data-name="Path 229" d="M383.068,437.628a3.7,3.7,0,1,1-3.7-3.7A3.7,3.7,0,0,1,383.068,437.628Z" transform="translate(-14.892 -48.377)" fill="#fff"/>
          <ellipse id="Ellipse_24" data-name="Ellipse 24" cx="1.168" cy="1.168" rx="1.168" ry="1.168" transform="translate(417.891 394.566)" fill="#fff"/>
          <path id="Path_230" data-name="Path 230" d="M468.043,390.289a2.222,2.222,0,1,1-2.222-2.222A2.222,2.222,0,0,1,468.043,390.289Z" transform="translate(-17.926 -46.795)" fill="#fff"/>
          <path id="Path_231" data-name="Path 231" d="M449.63,366.831a3.148,3.148,0,1,1-3.149-3.148A3.148,3.148,0,0,1,449.63,366.831Z" transform="translate(-17.227 -45.953)" fill="#fff"/>
          <path id="Path_232" data-name="Path 232" d="M483,343.154a2.592,2.592,0,1,1-2.592-2.592A2.592,2.592,0,0,1,483,343.154Z" transform="translate(-18.417 -45.155)" fill="#fff"/>
          <path id="Path_233" data-name="Path 233" d="M467.965,368.482a3.323,3.323,0,1,1-3.323-3.324A3.323,3.323,0,0,1,467.965,368.482Z" transform="translate(-17.848 -46.004)" fill="#fff"/>
          <path id="Path_234" data-name="Path 234" d="M487.621,380.242a.644.644,0,1,1-.644-.643A.644.644,0,0,1,487.621,380.242Z" transform="translate(-18.711 -46.502)" fill="#fff"/>
          <ellipse id="Ellipse_25" data-name="Ellipse 25" cx="3.544" cy="3.544" rx="3.544" ry="3.544" transform="translate(226.57 373.451)" fill="#fff"/>
          <ellipse id="Ellipse_26" data-name="Ellipse 26" cx="3.378" cy="3.378" rx="3.378" ry="3.378" transform="translate(156.732 375.9)" fill="#fff"/>
          <ellipse id="Ellipse_27" data-name="Ellipse 27" cx="2.213" cy="2.213" rx="2.213" ry="2.213" transform="translate(171.215 343.495)" fill="#fff"/>
          <path id="Path_235" data-name="Path 235" d="M186.12,416.482a1.1,1.1,0,1,1-1.1-1.1A1.1,1.1,0,0,1,186.12,416.482Z" transform="translate(-8.277 -47.737)" fill="#fff"/>
          <path id="Path_236" data-name="Path 236" d="M151.14,418.284a2.9,2.9,0,1,1-2.9-2.9A2.9,2.9,0,0,1,151.14,418.284Z" transform="translate(-6.945 -47.737)" fill="#fff"/>
          <path id="Path_237" data-name="Path 237" d="M126.454,438.493a2.894,2.894,0,1,1-2.894-2.894A2.894,2.894,0,0,1,126.454,438.493Z" transform="translate(-6.094 -48.434)" fill="#fff"/>
          <path id="Path_238" data-name="Path 238" d="M86.8,431.529a2.792,2.792,0,1,1-2.792-2.792A2.792,2.792,0,0,1,86.8,431.529Z" transform="translate(-4.733 -48.198)" fill="#fff"/>
          <path id="Path_239" data-name="Path 239" d="M91.575,414.07a2.288,2.288,0,1,1-2.287-2.288A2.288,2.288,0,0,1,91.575,414.07Z" transform="translate(-4.933 -47.613)" fill="#fff"/>
          <path id="Path_240" data-name="Path 240" d="M44.286,419.115a2.128,2.128,0,1,1-2.128-2.129A2.128,2.128,0,0,1,44.286,419.115Z" transform="translate(-3.312 -47.792)" fill="#fff"/>
          <ellipse id="Ellipse_28" data-name="Ellipse 28" cx="2.896" cy="2.896" rx="2.896" ry="2.896" transform="translate(36.717 327.303)" fill="#fff"/>
          <ellipse id="Ellipse_29" data-name="Ellipse 29" cx="2.561" cy="2.561" rx="2.561" ry="2.561" transform="translate(42.51 293.203)" fill="#fff"/>
          <ellipse id="Ellipse_30" data-name="Ellipse 30" cx="3.505" cy="3.505" rx="3.505" ry="3.505" transform="translate(18.988 275.259)" fill="#fff"/>
          <ellipse id="Ellipse_31" data-name="Ellipse 31" cx="2.253" cy="2.253" rx="2.253" ry="2.253" transform="translate(4.506 312.177)" fill="#fff"/>
          <ellipse id="Ellipse_32" data-name="Ellipse 32" cx="3.862" cy="3.862" rx="3.862" ry="3.862" transform="translate(9.011 341.696)" fill="#fff"/>
          <path id="Path_241" data-name="Path 241" d="M61.094,260.157a1.221,1.221,0,1,1-1.221-1.221A1.222,1.222,0,0,1,61.094,260.157Z" transform="translate(-3.955 -42.339)" fill="#fff"/>
          <path id="Path_242" data-name="Path 242" d="M18.793,228.915a3.063,3.063,0,1,1-3.064-3.063A3.063,3.063,0,0,1,18.793,228.915Z" transform="translate(-2.368 -41.198)" fill="#fff"/>
          <path id="Path_243" data-name="Path 243" d="M15.684,278.44a2.175,2.175,0,1,1-2.175-2.175A2.174,2.174,0,0,1,15.684,278.44Z" transform="translate(-2.322 -42.937)" fill="#fff"/>
          <ellipse id="Ellipse_33" data-name="Ellipse 33" cx="1.913" cy="1.913" rx="1.913" ry="1.913" transform="translate(48.919 199.858)" fill="#fff"/>
          <path id="Path_244" data-name="Path 244" d="M7.292,250.911a2.646,2.646,0,1,1-2.645-2.646A2.646,2.646,0,0,1,7.292,250.911Z" transform="translate(-2 -41.971)" fill="#fff"/>
          <path id="Path_245" data-name="Path 245" d="M32.289,286.441a3.748,3.748,0,1,1-3.748-3.747A3.748,3.748,0,0,1,32.289,286.441Z" transform="translate(-2.786 -43.159)" fill="#fff"/>
          <path id="Path_246" data-name="Path 246" d="M28.483,151.578a2.688,2.688,0,1,1-2.688-2.688A2.688,2.688,0,0,1,28.483,151.578Z" transform="translate(-2.728 -38.542)" fill="#fff"/>
          <ellipse id="Ellipse_34" data-name="Ellipse 34" cx="1.662" cy="1.662" rx="1.662" ry="1.662" transform="translate(56.643 105.561)" fill="#fff"/>
          <path id="Path_247" data-name="Path 247" d="M83.595,103.158a3.725,3.725,0,1,1-3.725-3.725A3.725,3.725,0,0,1,83.595,103.158Z" transform="translate(-4.558 -36.836)" fill="#fff"/>
          <path id="Path_248" data-name="Path 248" d="M26.587,91.621A4.023,4.023,0,1,1,22.564,87.6,4.023,4.023,0,0,1,26.587,91.621Z" transform="translate(-2.571 -36.428)" fill="#fff"/>
          <ellipse id="Ellipse_35" data-name="Ellipse 35" cx="2.414" cy="2.414" rx="2.414" ry="2.414" transform="translate(135.491 64.801)" fill="#fff"/>
          <path id="Path_249" data-name="Path 249" d="M217.183,53.69a2.092,2.092,0,1,1-2.091-2.091A2.092,2.092,0,0,1,217.183,53.69Z" transform="translate(-9.28 -35.186)" fill="#fff"/>
          <ellipse id="Ellipse_36" data-name="Ellipse 36" cx="0.965" cy="0.965" rx="0.965" ry="0.965" transform="translate(249.42 1.931)" fill="#fff"/>
          <ellipse id="Ellipse_37" data-name="Ellipse 37" cx="2.253" cy="2.253" rx="2.253" ry="2.253" transform="translate(214.019 35.079)" fill="#fff"/>
          <ellipse id="Ellipse_38" data-name="Ellipse 38" cx="1.931" cy="1.931" rx="1.931" ry="1.931" transform="translate(294.155 24.137)" fill="#fff"/>
          <ellipse id="Ellipse_39" data-name="Ellipse 39" cx="2.414" cy="2.414" rx="2.414" ry="2.414" transform="translate(283.856 42.481)" fill="#fff"/>
          <path id="Path_250" data-name="Path 250" d="M371.949,60.194a3.177,3.177,0,1,1-3.177-3.176A3.177,3.177,0,0,1,371.949,60.194Z" transform="translate(-14.545 -35.373)" fill="#fff"/>
          <path id="Path_251" data-name="Path 251" d="M284.9,37.047a2.448,2.448,0,1,1-2.448-2.448A2.448,2.448,0,0,1,284.9,37.047Z" transform="translate(-11.592 -34.599)" fill="#fff"/>
          <ellipse id="Ellipse_40" data-name="Ellipse 40" cx="2.896" cy="2.896" rx="2.896" ry="2.896" transform="translate(323.12 7.723)" fill="#fff"/>
          <path id="Path_252" data-name="Path 252" d="M380.817,89.852a2.253,2.253,0,1,1-2.253-2.253A2.252,2.252,0,0,1,380.817,89.852Z" transform="translate(-14.915 -36.428)" fill="#fff"/>
          <ellipse id="Ellipse_41" data-name="Ellipse 41" cx="2.253" cy="2.253" rx="2.253" ry="2.253" transform="translate(398.429 35.079)" fill="#fff"/>
          <path id="Path_253" data-name="Path 253" d="M454.561,58.667a2.736,2.736,0,1,1-2.735-2.735A2.736,2.736,0,0,1,454.561,58.667Z" transform="translate(-17.426 -35.335)" fill="#fff"/>
          <ellipse id="Ellipse_42" data-name="Ellipse 42" cx="3.138" cy="3.138" rx="3.138" ry="3.138" transform="translate(457.198 57.447)" fill="#fff"/>
          <path id="Path_254" data-name="Path 254" d="M459.468,124.426a2.623,2.623,0,1,1-2.623-2.622A2.623,2.623,0,0,1,459.468,124.426Z" transform="translate(-17.603 -37.608)" fill="#fff"/>
          <path id="Path_255" data-name="Path 255" d="M436.036,137.615a1.851,1.851,0,1,1-1.851-1.851A1.851,1.851,0,0,1,436.036,137.615Z" transform="translate(-16.847 -38.089)" fill="#fff"/>
          <path id="Path_256" data-name="Path 256" d="M487.609,143.352a3.754,3.754,0,1,1-3.754-3.753A3.754,3.754,0,0,1,487.609,143.352Z" transform="translate(-18.496 -38.222)" fill="#fff"/>
          <path id="Path_257" data-name="Path 257" d="M469.421,171.986a2.438,2.438,0,1,1-2.438-2.439A2.438,2.438,0,0,1,469.421,171.986Z" transform="translate(-17.959 -39.255)" fill="#fff"/>
          <path id="Path_258" data-name="Path 258" d="M491.87,187.1a1.1,1.1,0,1,1-1.1-1.1A1.1,1.1,0,0,1,491.87,187.1Z" transform="translate(-18.826 -39.823)" fill="#fff"/>
          <ellipse id="Ellipse_43" data-name="Ellipse 43" cx="2.414" cy="2.414" rx="2.414" ry="2.414" transform="translate(449.024 158.019)" fill="#fff"/>
          <ellipse id="Ellipse_44" data-name="Ellipse 44" cx="3.822" cy="3.822" rx="3.822" ry="3.822" transform="translate(456.205 193.822)" fill="#fff"/>
          <path id="Path_259" data-name="Path 259" d="M458.79,253.8a3.426,3.426,0,1,1-3.426-3.426A3.426,3.426,0,0,1,458.79,253.8Z" transform="translate(-17.524 -42.044)" fill="#fff"/>
          <ellipse id="Ellipse_45" data-name="Ellipse 45" cx="1.736" cy="1.736" rx="1.736" ry="1.736" transform="translate(469.113 220.07)" fill="#fff"/>
          <path id="Path_260" data-name="Path 260" d="M488.4,280.332a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,488.4,280.332Z" transform="translate(-18.63 -43.001)" fill="#fff"/>
          <path id="Path_261" data-name="Path 261" d="M468.043,335.068a2.222,2.222,0,1,1-2.222-2.222A2.221,2.221,0,0,1,468.043,335.068Z" transform="translate(-17.926 -44.889)" fill="#fff"/>
          <path id="Path_262" data-name="Path 262" d="M493.889,384.376a3.444,3.444,0,1,1-3.444-3.444A3.444,3.444,0,0,1,493.889,384.376Z" transform="translate(-18.734 -46.548)" fill="#fff"/>
          <path id="Path_263" data-name="Path 263" d="M437.284,418.874a2.352,2.352,0,1,1-2.353-2.353A2.353,2.353,0,0,1,437.284,418.874Z" transform="translate(-16.856 -47.776)" fill="#fff"/>
          <ellipse id="Ellipse_46" data-name="Ellipse 46" cx="4.285" cy="4.285" rx="4.285" ry="4.285" transform="translate(276.776 387.164)" fill="#fff"/>
          <ellipse id="Ellipse_47" data-name="Ellipse 47" cx="2.589" cy="2.589" rx="2.589" ry="2.589" transform="translate(297.373 365.369)" fill="#fff"/>
          <ellipse id="Ellipse_48" data-name="Ellipse 48" cx="2.092" cy="2.092" rx="2.092" ry="2.092" transform="translate(222.386 395.734)" fill="#fff"/>
          <ellipse id="Ellipse_49" data-name="Ellipse 49" cx="2.815" cy="2.815" rx="2.815" ry="2.815" transform="translate(176.742 399.918)" fill="#fff"/>
          <ellipse id="Ellipse_50" data-name="Ellipse 50" cx="2.142" cy="2.142" rx="2.142" ry="2.142" transform="translate(188.909 387.164)" fill="#fff"/>
          <ellipse id="Ellipse_51" data-name="Ellipse 51" cx="2.353" cy="2.353" rx="2.353" ry="2.353" transform="translate(141.292 402.733)" fill="#fff"/>
          <path id="Path_264" data-name="Path 264" d="M118.646,423.03a3.085,3.085,0,1,1-3.085-3.085A3.084,3.084,0,0,1,118.646,423.03Z" transform="translate(-5.812 -47.894)" fill="#fff"/>
          <path id="Path_265" data-name="Path 265" d="M103.734,452.8a2.2,2.2,0,1,1-2.2-2.2A2.2,2.2,0,0,1,103.734,452.8Z" transform="translate(-5.358 -48.952)" fill="#fff"/>
          <path id="Path_266" data-name="Path 266" d="M62.24,454.676a1.794,1.794,0,1,1-1.794-1.795A1.795,1.795,0,0,1,62.24,454.676Z" transform="translate(-3.955 -49.031)" fill="#fff"/>
          <path id="Path_267" data-name="Path 267" d="M62.793,425.211a2.071,2.071,0,1,1-2.071-2.071A2.071,2.071,0,0,1,62.793,425.211Z" transform="translate(-3.955 -48.005)" fill="#fff"/>
        </g>
      </g>
    </g>
    <g id="Group_1936" data-name="Group 1936" transform="translate(-2 1)">
      <g id="Group_1897" data-name="Group 1897" transform="translate(-32 -44)">
        <text id="_21_classes_You_pay_8_376" data-name="21 classes
You pay ₹ 8,376" transform="translate(1030 1151)" fill="#fff" font-size="16" font-family="Roboto-Medium, Roboto" font-weight="500" letter-spacing="0.02em"><tspan x="-39.764" y="0">21 classes</tspan><tspan x="-58.713" y="22">You pay ₹ 8,376</tspan></text>
      </g>
      <text id="_349_per_class" data-name="₹ 349 per class" transform="translate(897 1045)" fill="#fff" font-size="28" font-family="Roboto-Black, Roboto" font-weight="800" letter-spacing="0.02em"><tspan x="0" y="0">₹ 349 per class</tspan></text>
      <text id="FREE_DEMO_CLASS" data-name="FREE DEMO CLASS" transform="translate(944 1073)" fill="#ffd438" font-size="12" font-family="Roboto-Black, Roboto" font-weight="800" letter-spacing="0.02em"><tspan x="0" y="0">FREE DEMO CLASS</tspan></text>
      <g id="Group_1935" data-name="Group 1935" transform="translate(-26 -8)">
        <g id="Rectangle_87" data-name="Rectangle 87" transform="translate(960 1157)" fill="#002f5a" stroke="#002f5a" stroke-width="1">
          <rect width="128" height="44" rx="4" stroke="none"/>
          <rect x="0.5" y="0.5" width="127" height="43" rx="3.5" fill="none"/>
        </g>
        <text id="Enroll_Now" data-name="Enroll Now" transform="translate(984 1186)" fill="#fff" font-size="16" font-family="Roboto-Medium, Roboto" font-weight="500" letter-spacing="0.02em"><tspan x="0" y="0">Enroll Now</tspan></text>
      </g>
    </g>
  </g>
</svg>
