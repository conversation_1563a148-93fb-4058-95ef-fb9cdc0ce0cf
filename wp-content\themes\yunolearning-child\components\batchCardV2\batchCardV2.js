Vue.component('yuno-batch-card-v2', {
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    template: `
        <article class="batchCardV2">
            <div class="cardHeader">
                <div class="batchDate">
                    <p class="month caption1">{{ manageDate(data.starts_on_iso, 'month') }}</p>
                    <p class="date h3">{{ manageDate(data.starts_on_iso, 'date') }}</p>
                    <p class="day caption2">{{ manageDate(data.starts_on_iso, 'day') }}</p>
                </div>
                <div class="batchInfo">
                    <div class="timeAmount">
                        <p class="time h4">{{ data.batch_start_time }}</p>
                        <p class="amount h4" v-if="data.price">₹{{ data.price }}</p>
                    </div>
                    <p class="caption2">Duration: {{ data.duration_weeks }}</p>
                    <div class="hasArray">
                        <span class="caption2" v-for="(day, i) in data.class_days" :class="{active: day.is_available}">
                            {{day.label}}
                        </span>
                    </div>
                    <ul class="colView">
                        <li class="hasPipe">
                            <div class="ctaLook caption2">
                                <span class="material-icons-outlined">
                                    {{ data.batch_personalisation === 'one_to_many' ? 'groups' : 'safety_divider' }}
                                </span>
                                {{ data.batch_personalisation === 'one_to_many' ? 'Group classes' : '1-to-1' }}
                            </div>
                        </li>
                        <li>
                            <p class="caption2">Online</p>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="cardFooter">
                <div>
                    <p class="caption3">{{ data.instructor.name }}</p>
                    <ul class="colView">
                        <li class="hasPipe">
                            <p class="caption2 fontColorDarkVariant">{{ data.instructor.rating }}</p>
                            <span class="material-icons">star</span>
                        </li>
                        <li>
                            <p class="caption2 fontColorDarkVariant">{{ data.successful_enrollment }} active students</p>
                        </li>
                    </ul>
                </div>
                <div>
                    <img :src="data.instructor.image" :alt="data.instructor.name" class="avatar" width="48" height="48">
                </div>
            </div>
        </article>
    `,
    data() {
        return {
            
        }
    },
    computed: {

    },
    async created() {

    },
    destroyed() {

    },
    mounted() {

    },
    methods: {
        /**
         * Converts a given date into a specific format based on the provided type.
         * @param {string} date - The date to be converted.
         * @param {string} type - The type of conversion to be performed. Possible values: 'month', 'date', 'day'.
         * @returns {string|null} - The converted date value or null if the type is invalid.
         */
        manageDate(date, type) {
            const dateObj = new Date(date);
            const typeToFunctionMap = {
                month: () => dateObj.toLocaleString('default', { month: 'short' }),
                date: () => dateObj.getDate(),
                day: () => dateObj.toLocaleString('default', { weekday: 'short' }),
            };

            return typeToFunctionMap[type]?.() || null;
        },
    }
});