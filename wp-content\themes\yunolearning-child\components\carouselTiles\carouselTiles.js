const YUNOCarouselTiles = (function($) {
    
    const carouselTiles = function() {
        Vue.component('star-rating', VueStarRating.default)

        Vue.component('yuno-carousel-tiles', {
            props: ["options"],
            template: `
                <section id="carouselTiles" class="carouselTiles" :class="{'noTopGap': !options.topGap, 'noBottomGap': !options.topGap}">
                    <div class="container">
                        <h2 class="carouselTilesTitle">{{options.title}}</h2>
                        <div :id="options.sectionID" :class="options.sectionID">
                            <div 
                                v-for="(slide, slideIndex) in options.list"
                                :key="slideIndex"
                                class="slide">
                                <article class="cardItem">
                                    <a :href="slide.instructor_profile_url">
                                        <figure class="cardImg">
                                            <img :src="slide.instructor_image" :alt="slide.instructor_name">
                                            <figcaption>
                                                <h3>{{slide.instructor_name}}</h3>
                                                <div class="ratingWrapper" :class="{'alignC': slide.reviews_avg_rating === 0}">
                                                    <div class="total" v-if="slide.reviews_avg_rating !== 0">{{slide.reviews_avg_rating}}</div>
                                                    <star-rating :rating="slide.reviews_avg_rating" :read-only="true" :increment="0.01" :star-size="18" :show-rating="false"></star-rating>
                                                    <div class="total" v-if="slide.reviews_count !== 0">{{slide.reviews_count}} Reviews</div>
                                                </div>
                                            </figcaption>
                                        </figure>
                                    </a>
                                </article>
                            </div>
                        </div>
                        <div class="carouselTilesControls">
                            <button class="prev" type="button" data-controls="prev" tabindex="-1" aria-controls="tns1">
                                <i class="fa fa-chevron-left" aria-hidden="true"></i>
                            </button>
                            <button class="next" type="button" data-controls="next" tabindex="-1" aria-controls="tns1">
                                <i class="fa fa-chevron-right" aria-hidden="true"></i>
                            </button>
                        </div>
                    </div>
                </section>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                
            },
            created() {
                
            },
            mounted() {
                const sectionId = this.$props.options.sectionID;

                let slider = tns({
                    container: "#"+ sectionId +"",
                    controlsContainer: ".carouselTilesControls",
                    loop: false,
                    responsive: {
                        500: {
                            items: 1
                        },
                        768: {
                            items: 3
                        },
                        992: {
                            items: 4
                        },
                        1200: {
                            items: 4
                        }
                    },
                    swipeAngle: false,
                    speed: 400,
                    nav: false,
                    mouseDrag: true,
                    autoplay: false,
                    autoplayTimeout: 6000,
                    nonce: yunoNonce
                });
            },
            methods: {
                
            }
        });
    };

    return {
        carouselTiles: carouselTiles
    };
})(jQuery);

