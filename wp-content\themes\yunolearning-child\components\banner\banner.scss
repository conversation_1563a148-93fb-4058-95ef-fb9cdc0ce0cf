@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";
.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.ylIcon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'yuno-icon' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    
    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
    font-family: 'Material Icons Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
  
  .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

.dark87 {
    @include setFontColor($primaryCopyColor, 0.87);
}

.dark60 {
    @include setFontColor($primaryCopyColor, 0.6);
}

#app {
    .banner {
        padding: $gapLargest 0;
        @extend .dark87;
        
        @media (min-width: 768px) {
            padding: 100px 0 0;
        }

        .container {
            background-color: $primary;
            border-radius: 0;
            padding: $gap15;
            text-align: center;

            @media (min-width: 768px) {
                border-radius: 4px;
                padding: $gapLargest;
                text-align: left;
            }
        }

        .wrapper {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: space-between;
            color: white;

            h2 {
                @include setFont($headline1, 62px, 700, $gapSmall);
            }

            small {
                @include setFont($headline5, 28px, 700, 0);
            }

            .yunoWhiteCTA {
                height: 56px;
                @include setFont($subtitle1, normal, 500, 0);
                padding-left: $gapLargest;
                padding-right: $gapLargest;
                @extend .dark87;

                &:hover {
                    text-decoration: none;
                    color: $primary;
                }
            }
        }

        .lftCol {
            flex: 0 0 100%;

            @media (min-width: 768px) {
                flex: 0 0 auto;
            }
        }

        .ritCol {
            flex: 0 0 100%;
            margin-top: $gap15;

            @media (min-width: 768px) {
                flex: 0 0 auto;
            }
        }
    }
}