/*! vue-carousel v1.0.5 | (c) 2018-present <PERSON> | MIT */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).VueCarousel=e()}(this,function(){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a(t,e,o){return e in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}function i(e,t){var o,a=Object.keys(e);return Object.getOwnPropertySymbols&&(o=Object.getOwnPropertySymbols(e),t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,o)),a}function n(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?i(Object(o),!0).forEach(function(t){a(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):i(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}
/*!
 * create-vue-component v1.1.0
 * https://github.com/fengyuanchen/create-vue-component
 *
 * Copyright 2018-present Chen Fengyuan
 * Released under the MIT license
 *
 * Date: 2018-06-28T13:45:18.559Z
 */var o="function"==typeof Symbol&&"symbol"===e(Symbol.iterator)?function(t){return e(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":e(t)},r="function"==typeof Symbol&&"symbol"===o(Symbol.iterator)?function(t){return void 0===t?"undefined":o(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":void 0===t?"undefined":o(t)};function s(t){return"object"===(void 0===t?"undefined":r(t))&&null!==t}var t=Object.prototype,c=t.hasOwnProperty,l=t.toString;function d(t){return"string"==typeof t&&0<t.trim().length}function u(t){return function(t){if(!s(t))return!1;try{var e=t.constructor,o=e.prototype;return e&&o&&c.call(o,"isPrototypeOf")}catch(t){return!1}}(t)&&(d(t.template)||"function"==typeof t.render||d(t.el)||s(o=t.el)&&1===o.nodeType&&-1<l.call(o).indexOf("Element")||u(t.extends)||(e=t.mixins,Array.isArray(e)&&0<e.length&&t.mixins.some(u)));var e,o}var f=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var o=arguments[e];for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(t[a]=o[a])}return t};var p="undefined"!=typeof window&&void 0!==window.document,m=!(!p||!window.document.documentElement)&&"ontouchstart"in window.document.documentElement,h=p&&"PointerEvent"in window,_=h?"pointerdown":m?"touchstart":"mousedown",b=h?"pointermove":m?"touchmove":"mousemove",v=h?"pointerup":m?"touchend":"mouseup",y=h?"pointerenter":"mouseenter",g=h?"pointerleave":"mouseleave",w={name:"Carousel",props:{autoplay:{type:Boolean,default:!0},controls:{type:[Boolean,String],default:"hover"},data:{type:Array,default:void 0},direction:{type:String,default:"left"},indicators:{type:[Boolean,String],default:!0},indicatorTrigger:{type:String,default:"click"},indicatorType:{type:String,default:"line"},interval:{type:Number,default:5e3},pauseOnEnter:{type:Boolean,default:!0},slideOnSwipe:{type:Boolean,default:!0},tag:{type:String,default:"div"}},data:function(){return{endX:0,endY:0,index:0,list:[],playing:!1,sliding:!1,startX:0,startY:0,timeout:0}},watch:{data:function(){this.init()}},created:function(){this.init()},mounted:function(){var t=this;document.addEventListener("visibilitychange",this.onVisibilityChange=function(){t.playing&&("visible"===document.visibilityState?t.cycle():t.pause())}),this.autoplay&&this.play()},beforeDestroy:function(){document.removeEventListener("visibilitychange",this.onVisibilityChange)},methods:{init:function(){var t,i=this,e=this.data,r=[];e&&0<e.length&&(t=e.length-1,this.index>t&&(this.index=t),e.forEach(function(t,e){var o=e===i.index,a=n(n({},t&&void 0!==t.content?t:{content:t}),{},{active:o,bottom:!1,left:!1,raw:t,right:!1,sliding:o,toBottom:!1,toLeft:!1,toRight:!1,toTop:!1,top:!1});r.push(a)})),this.list=r},play:function(){this.playing||(this.playing=!0,this.cycle())},cycle:function(){var t=this;this.playing&&(this.pause(),this.timeout=setTimeout(function(){t.next(function(){t.cycle()})},this.interval))},pause:function(){clearTimeout(this.timeout),this.timeout=0},stop:function(){this.playing&&(this.pause(),this.playing=!1)},prev:function(t){this.slideTo(this.index-1,t)},next:function(t){this.slideTo(this.index+1,t)},slide:function(t,e,o){var a=this,i=1<arguments.length&&void 0!==e&&e,r=2<arguments.length&&void 0!==o?o:function(){};if(document.hidden||this.sliding)r();else{this.sliding=!0;var n=this.list,s=n.length-1;if(s<t?t=0:t<0&&(t=s),t!==this.index){var c=n[this.index],l=n[t];switch(this.direction){case"up":l.bottom=!i,l.top=i;break;case"right":l.left=!i,l.right=i;break;case"down":l.top=!i,l.bottom=i;break;default:l.right=!i,l.left=i}this.$nextTick(function(){switch(a.$el.offsetWidth,a.direction){case"up":c.toTop=!i,c.toBottom=i,l.toTop=!i,l.toBottom=i;break;case"right":c.toRight=!i,c.toLeft=i,l.toRight=!i,l.toLeft=i;break;case"down":c.toBottom=!i,c.toTop=i,l.toBottom=!i,l.toTop=i;break;default:c.toLeft=!i,c.toRight=i,l.toLeft=!i,l.toRight=i}c.sliding=!1,l.sliding=!0,setTimeout(function(){c.active=!1,c.top=!1,c.right=!1,c.bottom=!1,c.left=!1,c.toTop=!1,c.toRight=!1,c.toBottom=!1,c.toLeft=!1,l.active=!0,l.top=!1,l.right=!1,l.bottom=!1,l.left=!1,l.toTop=!1,l.toRight=!1,l.toBottom=!1,l.toLeft=!1,a.index=t,a.sliding=!1,r()},600)})}else r()}},slideTo:function(t,e){var o,a;t!==this.index&&(o=this.direction,a=t<this.index,"right"!==o&&"down"!==o||(a=!a),this.slide(t,a,e))},slideStart:function(t){var e=t.touches?t.touches[0]:null;this.playing&&this.pauseOnEnter&&this.stop(),this.startX=e?e.pageX:t.pageX,this.startY=e?e.pageY:t.pageY},slideMove:function(t){var e=t.touches?t.touches[0]:null;t.preventDefault(),this.endX=e?e.pageX:t.pageX,this.endY=e?e.pageY:t.pageY},slideEnd:function(){var t=this,e=this.endX-this.startX,o=this.endY-this.startY;if(this.endX=this.startX,this.endY=this.startY,0!=e||0!=o){var a=this.$el.offsetWidth/5,i=this.$el.offsetHeight/5,r=o<-i,n=a<e,s=i<o,c=e<-a,l=function(){t.playing&&t.pauseOnEnter&&t.play()},d=!1,u=!1;switch(this.direction){case"up":d=s,u=r;break;case"right":d=c,u=n;break;case"down":d=r,u=s;break;default:d=n,u=c}d?this.prev(l):u?this.next(l):l()}}},render:function(i){var t,e,o,r=this;return i(this.tag,{class:(a(t={carousel:!0},"carousel--".concat(this.direction),this.direction),a(t,"carousel--slidable",this.slideOnSwipe),a(t,"carousel--controls","hover"===this.controls),a(t,"carousel--indicators","hover"===this.indicators),t),on:n(n(n({},this.$listeners),this.pauseOnEnter?(a(e={},y,this.pause),a(e,g,this.cycle),e):{}),this.slideOnSwipe?(a(o={},_,this.slideStart),a(o,b,this.slideMove),a(o,v,this.slideEnd),o):{})},0===this.list.length?[]:[i("ul",{class:"carousel__list"},this.list.map(function(t,e){return i("li",{attrs:{"data-index":e},class:{carousel__item:!0,"carousel__item--active":t.active,"carousel__item--top":t.top,"carousel__item--right":t.right,"carousel__item--bottom":t.bottom,"carousel__item--left":t.left,"carousel__item--to-top":t.toTop,"carousel__item--to-right":t.toRight,"carousel__item--to-bottom":t.toBottom,"carousel__item--to-left":t.toLeft}},[i(function(e,t){var o,a=1<arguments.length&&void 0!==t?t:{},i={};return u(e)?i=f({},e):"function"==typeof e?i.render=function(t){return e.call(this,t,a.data,this)}:(o=a.tag||"span",i.template="<"+o+">"+e+"</"+o+">"),i}(t.content,{data:t.raw}))])})),this.indicators?i("ol",{class:a({carousel__indicators:!0},"carousel__indicators--".concat(this.indicatorType),this.indicatorType)},this.list.map(function(t,e){return i("li",{attrs:{"data-slide-to":e},class:{carousel__indicator:!0,"carousel__indicator--active":t.sliding},on:(a={},"hover"===r.indicatorTrigger?(a.touchstart=o,a[y]=o):a.click=o,a)});function o(){r.slideTo(e)}var a})):"",this.controls?i("button",{attrs:{type:"button","data-slide":"prev"},class:"carousel__control carousel__control--prev",on:{click:function(){-1<["right","down"].indexOf(r.direction)?r.next():r.prev()}}}):"",this.controls?i("button",{attrs:{type:"button","data-slide":"next"},class:"carousel__control carousel__control--next",on:{click:function(){-1<["right","down"].indexOf(r.direction)?r.prev():r.next()}}}):""])}};function x(t,e,o,a,i,r,n,s,c,l){"boolean"!=typeof n&&(c=s,s=n,n=!1);var d,u,f,p="function"==typeof o?o.options:o;return t&&t.render&&(p.render=t.render,p.staticRenderFns=t.staticRenderFns,p._compiled=!0,i&&(p.functional=!0)),a&&(p._scopeId=a),r?(d=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),e&&e.call(this,c(t)),t&&t._registeredComponents&&t._registeredComponents.add(r)},p._ssrRegister=d):e&&(d=n?function(t){e.call(this,l(t,this.$root.$options.shadowRoot))}:function(t){e.call(this,s(t))}),d&&(p.functional?(u=p.render,p.render=function(t,e){return d.call(e),u(t,e)}):(f=p.beforeCreate,p.beforeCreate=f?[].concat(f,d):[d])),o}var S,O="undefined"!=typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());function T(t){return function(t,e){var o=O?e.media||"default":t,a=X[o]||(X[o]={ids:new Set,styles:[]});{var i,r,n,s;a.ids.has(t)||(a.ids.add(t),i=e.source,e.map&&(i+="\n/*# sourceURL="+e.map.sources[0]+" */",i+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e.map))))+" */"),a.element||(a.element=document.createElement("style"),a.element.type="text/css",e.media&&a.element.setAttribute("media",e.media),void 0===S&&(S=document.head||document.getElementsByTagName("head")[0]),S.appendChild(a.element)),"styleSheet"in a.element?(a.styles.push(i),a.element.styleSheet.cssText=a.styles.filter(Boolean).join("\n")):(r=a.ids.size-1,n=document.createTextNode(i),(s=a.element.childNodes)[r]&&a.element.removeChild(s[r]),s.length?a.element.insertBefore(n,s[r]):a.element.appendChild(n)))}}}var X={};var E=x({},function(t){t&&t("data-v-730f2b7a_0",{source:'.carousel[data-v-730f2b7a]{position:relative;user-select:none}.carousel--slidable[data-v-730f2b7a]{touch-action:none}.carousel--down>.carousel__indicators[data-v-730f2b7a],.carousel--up>.carousel__indicators[data-v-730f2b7a]{bottom:auto;flex-direction:column;left:auto;right:0;top:50%;transform:translate(0,-50%)}.carousel--down>.carousel__indicators>.carousel__indicator[data-v-730f2b7a]::before,.carousel--up>.carousel__indicators>.carousel__indicator[data-v-730f2b7a]::before{height:100%;width:.125rem}.carousel--down>.carousel__indicators--disc>.carousel__indicator[data-v-730f2b7a],.carousel--up>.carousel__indicators--disc>.carousel__indicator[data-v-730f2b7a]{height:.75rem;width:1.5rem}.carousel--down>.carousel__indicators--disc>.carousel__indicator[data-v-730f2b7a]::before,.carousel--up>.carousel__indicators--disc>.carousel__indicator[data-v-730f2b7a]::before{height:.5rem;width:.5rem}.carousel--right>.carousel__indicators[data-v-730f2b7a]{flex-direction:row-reverse}.carousel--down>.carousel__indicators[data-v-730f2b7a]{flex-direction:column-reverse}.carousel--controls:hover>.carousel__control[data-v-730f2b7a]{opacity:.5;transform:translateX(0);z-index:1}.carousel--controls:hover>.carousel__control[data-v-730f2b7a]:focus,.carousel--controls:hover>.carousel__control[data-v-730f2b7a]:hover{opacity:1}.carousel--controls>.carousel__control[data-v-730f2b7a]{opacity:0;z-index:-1}.carousel--controls>.carousel__control--prev[data-v-730f2b7a]{transform:translateX(-50%)}.carousel--controls>.carousel__control--next[data-v-730f2b7a]{transform:translateX(50%)}.carousel--indicators:hover>.carousel__indicators[data-v-730f2b7a]{opacity:1;z-index:1}.carousel--indicators>.carousel__indicators[data-v-730f2b7a]{opacity:0;transition:opacity .15s;z-index:-1}.carousel__list[data-v-730f2b7a]{margin:0;overflow:hidden;padding:0;position:relative;width:100%}.carousel__item[data-v-730f2b7a]{display:none;margin:0}.carousel__item--active[data-v-730f2b7a],.carousel__item--bottom[data-v-730f2b7a],.carousel__item--left[data-v-730f2b7a],.carousel__item--right[data-v-730f2b7a],.carousel__item--top[data-v-730f2b7a]{display:block;transition:transform .6s ease-in-out;width:100%}.carousel__item--bottom[data-v-730f2b7a],.carousel__item--left[data-v-730f2b7a],.carousel__item--right[data-v-730f2b7a],.carousel__item--top[data-v-730f2b7a]{left:0;position:absolute;top:0}.carousel__item--top[data-v-730f2b7a]{transform:translateY(-100%)}.carousel__item--top.carousel__item--to-bottom[data-v-730f2b7a]{transform:translateY(0)}.carousel__item--right[data-v-730f2b7a]{transform:translateX(100%)}.carousel__item--right.carousel__item--to-left[data-v-730f2b7a]{transform:translateX(0)}.carousel__item--bottom[data-v-730f2b7a]{transform:translateY(100%)}.carousel__item--bottom.carousel__item--to-top[data-v-730f2b7a]{transform:translateY(0)}.carousel__item--left[data-v-730f2b7a]{transform:translateX(-100%)}.carousel__item--left.carousel__item--to-right[data-v-730f2b7a]{transform:translateX(0)}.carousel__item--active[data-v-730f2b7a]{transform:translate(0,0);z-index:1}.carousel__item--active.carousel__item--to-top[data-v-730f2b7a]{transform:translateY(-100%)}.carousel__item--active.carousel__item--to-right[data-v-730f2b7a]{transform:translateX(100%)}.carousel__item--active.carousel__item--to-bottom[data-v-730f2b7a]{transform:translateY(100%)}.carousel__item--active.carousel__item--to-left[data-v-730f2b7a]{transform:translateX(-100%)}.carousel__indicators[data-v-730f2b7a]{bottom:0;display:flex;justify-content:center;left:50%;list-style:none;margin:0;padding:0;position:absolute;transform:translateX(-50%);z-index:1}.carousel__indicators--disc>.carousel__indicator[data-v-730f2b7a]{width:.75rem}.carousel__indicators--disc>.carousel__indicator[data-v-730f2b7a]::before{border-radius:50%;height:.5rem;width:.5rem}.carousel__indicator[data-v-730f2b7a]{cursor:pointer;height:1.5rem;margin:.125rem;opacity:.5;position:relative;transition:opacity .15s;width:1.5rem}.carousel__indicator[data-v-730f2b7a]::before{background-color:#fff;content:"";display:block;height:.125rem;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:100%}.carousel__indicator--active[data-v-730f2b7a]{opacity:1}.carousel__control[data-v-730f2b7a]{background-color:rgba(0,0,0,.5);border:0;border-radius:50%;color:#fff;cursor:pointer;height:2rem;margin-top:-1rem;opacity:.5;padding:.5rem;position:absolute;top:50%;transition:all .15s;width:2rem}.carousel__control[data-v-730f2b7a]:focus,.carousel__control[data-v-730f2b7a]:hover{opacity:1}.carousel__control[data-v-730f2b7a]:focus{outline:0}.carousel__control[data-v-730f2b7a]::before{border:.0625rem solid transparent;border-radius:.125rem;content:"";display:block;height:.5rem;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%) rotate(45deg);width:.5rem}.carousel__control--prev[data-v-730f2b7a]{left:1rem}.carousel__control--prev[data-v-730f2b7a]::before{border-bottom-color:#fff;border-left-color:#fff;margin-left:.125rem}.carousel__control--next[data-v-730f2b7a]{right:1rem}.carousel__control--next[data-v-730f2b7a]::before{border-right-color:#fff;border-top-color:#fff;margin-left:-.125rem}',map:void 0,media:void 0})},w,"data-v-730f2b7a",void 0,void 0,!1,T,void 0,void 0);return E.install=function(t){t.component(E.name,E)},"undefined"!=typeof window&&window.Vue&&window.Vue.use(E),E});