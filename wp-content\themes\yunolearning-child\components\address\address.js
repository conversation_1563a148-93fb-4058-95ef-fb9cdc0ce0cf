const YUNOAddress = (function($) {
    
    const address = function() {
        Vue.component('yuno-address', {
            props: ["data"],
            template: `
                <div class="row">
                    <div class="col-12 col-md-12 col-lg-12">
                        <template v-if="data.loading">
                            <div class="addressCard">
                                <div class="header">
                                    <h2 class="addressType"><b-skeleton active width="80px"></b-skeleton></h2>
                                </div>
                                <p><b-skeleton active width="200px" height="80px"></b-skeleton></p>
                            </div>
                        </template>
                        <template v-else-if="data.success && data.error === null">
                            <div class="addressCard">
                                <div class="header">
                                    <h2 class="addressType">{{data.data.address_type}}</h2>
                                    <ul class="actionList">
                                        <li>
                                            <b-tooltip label="Edit address"
                                                type="is-light"
                                                position="is-top">
                                                <a href="#" @click="onNewAddress(false)"><i class="fa fa-pencil" aria-hidden="true"></i></a>
                                            </b-tooltip>
                                        </li>
                                    </ul>
                                </div>
                                <p>{{data.data.flat_house_number}}, {{data.data.street}}, {{data.data.landmark}}</p>
                                <p>{{data.data.city}} - {{data.data.pin_code}}, {{data.data.state}}, {{data.data.country}}</p>
                            </div>
                        </template>
                        <template v-else-if="data.success">
                            <div class="action">
                                <p>{{data.errorMsg}}</p>
                                <b-button
                                    @click="onNewAddress(true)" 
                                    class="yunoPrimaryCTA wired">
                                    <i class="fa fa-plus" aria-hidden="true"></i> Add Address
                                </b-button>
                            </div>
                        </template>
                    </div>
                    
                </div>
            `,
            data() {
                return {
                    
                }
            },
            computed: {
                ...Vuex.mapState([
                    'countries',
                    'states',
                    'cities'
                ]),
            },
            created() {
                
            },
            mounted() {
                
            },
            methods: {
                onNewAddress(isNewAddress) {
                    Event.$emit('onNewAddress', isNewAddress);
                }
            }
        });
    };

    return {
        address: address
    };
})(jQuery);

