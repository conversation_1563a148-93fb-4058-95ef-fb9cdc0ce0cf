class UploadAdapter {
    constructor( loader ) {
        // The file loader instance to use during the upload.
        this.loader = loader;
    }

    // Starts the upload process.
    upload() {
        return this.loader.file
            .then( file => new Promise( ( resolve, reject ) => {
                this._initRequest();
                this._initListeners( resolve, reject, file );
                this._sendRequest( file );
            } ) );
    }

    // Aborts the upload process.
    abort() {
        if ( this.xhr ) {
            this.xhr.abort();
        }
    }

    // Initializes the XMLHttpRequest object using the URL passed to the constructor.
    _initRequest() {
        const xhr = this.xhr = new XMLHttpRequest();

        xhr.open( 'POST', YUNOCommon.config.imageUpload(), true );
        xhr.responseType = 'json';
    }

    // Initializes XMLHttpRequest listeners.
    _initListeners( resolve, reject, file ) {
        const xhr = this.xhr;
        const loader = this.loader;
        const genericErrorText = `Couldn't upload file: ${ file.name }.`;

        xhr.addEventListener( 'error', () => reject( genericErrorText ) );
        xhr.addEventListener( 'abort', () => reject() );
        xhr.addEventListener( 'load', () => {
            const response = xhr.response;

            if ( !response || response.error ) {
                return reject( response && response.error ? response.error.message : genericErrorText );
            }

            resolve( {
                default: response.url
            } );
        } );

        if ( xhr.upload ) {
            xhr.upload.addEventListener( 'progress', evt => {
                if ( evt.lengthComputable ) {
                    loader.uploadTotal = evt.total;
                    loader.uploaded = evt.loaded;
                }
            } );
        }
    }

    // Prepares the data and sends the request.
    _sendRequest( file ) {
        // Prepare the form data.
        const data = new FormData();

        // data.append( 'upload', file );
        data.append("image", file);
        // data.append("upload_preset", YUNOCommon.config.cloudinaryImageUpload(true).upload_preset);

        // Send the request.
        this.xhr.send( data );
    }
}

Vue.component('yuno-ck-editor', {
    props: ["editor", "config", "payload", "data"],
    template: `
    <validation-provider 
        tag="div" 
        id="resourceDescription"
        :class="{'editorOnFocus': description.isEditorFocus}"
        :rules="{required:true}" 
        v-slot="{ errors, classes }">
        <b-field :label="data.title"></b-field>
        <div class="editorWrapper" :class="classes">
            <ckeditor 
                :editor="editor" 
                @ready="onEditorReady"
                v-model="payload[data.field]" 
                :config="config">
            </ckeditor>
            <p class="error">{{errors[0]}}</p>
        </div>
    </validation-provider>
    `,
    data() {
        return {
            description: {
                isEditorFocus: false
            }
        }
    },
    computed: {
        ...Vuex.mapState([
            'loader'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onEditorReady(e) {
            e.plugins.get( 'FileRepository' ).createUploadAdapter = ( loader ) => {
                return new UploadAdapter( loader );
            };
        },
        onEditorFocus(field) {
            field.isEditorFocus = true;
            YUNOCommon.scrollToElement("#createResource", 500);
        },
        onEditorBlur(field, elementID) {
            field.isEditorFocus = false;
            setTimeout(() => {
                YUNOCommon.scrollToElement("#"+ elementID +"", 500);    
            }, 100);
        }
    }
});