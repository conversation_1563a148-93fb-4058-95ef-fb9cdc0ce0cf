@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
    .categoryTaxonomy {
        background: $whiteBG;
        border: 1px solid $grey;
        border-top: 0;
    
        &:first-child {
            border-top: 1px solid $grey;    
        }
    
        .collapse-trigger {
            display: block;
            padding: 0;
    
            .b-radio {
                margin: 0;
            }
        }
    
        .collapseHeader {
            position: relative;
    
            &.menuDown {
                box-shadow: rgba(0,0,0,0.1) 0 4px 6px;
            }
    
            .b-radio {
                display: flex;
                width: 100%;
                padding: $gap15 $gap15;
    
                + .error {
                    display: none;
                    margin: 0;
                    padding: 0 0 $gap15 $gapLargest + $gap15;
                }
    
                &.invalid {
                    + .error {
                        display: block;
                    }
                }
            }
    
            .fa {
                position: absolute;
                right: $gap15;
                top: calc(50% - 8px);
            }
        }
    
        .collapse-content {
            display: block;
            padding: $gap15 $gapLargest;
        }
    
        .collapse {
            margin-bottom: $gap15;
    
            &:last-child {
                margin-bottom: 0;
            }

            .collapse-trigger {
                position: relative;
                padding-left: 18px;

                .fa {
                    position: absolute;
                    left: 0;
                    top: 3px;
                }
            }
    
            .collapse-content {
                padding-bottom: 0;
                position: relative;
    
                &:before {
                    content: "";
                    width: 1px;
                    height: 100%;
                    background-color: $grey;
                    position: absolute;
                    left: 4px;
                    top: 0;
                }
            }
    
            .sub2Content {
                .field {
                    display: flex;
                    align-items: center;
                    position: relative;
    
                    &:before {
                        content: "";
                        width: 100%;
                        height: 1px;
                        background-color: $grey;
                        position: absolute;
                        left: -25px;
                        top: 10px;
                        z-index: 1;
                    }
    
                    &:after {
                        content: "";
                        background: $whiteBG;
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        left: 0;
                        top: 0;
                        z-index: 2;
                    }
    
                    &:last-child {
                        margin-bottom: 0;
                    }
    
                    .b-checkbox {
                        position: relative;
                        z-index: 3;
                    }
                }
            }
    
            .trigger {
                .field {
                    display: flex;
                    align-items: center;
                }
                .b-checkbox {
                    margin: 0;
                }
            }
        }
    }
}