@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
    .batchCardV2 {
        .fontColorDark {
            color: $onSurface;
        }
        
        .fontColorDarkVariant {
            color: $onSurfaceVariant;
        }
        
        .fontColorDarkVariant2 {
            @include setFontColor($primaryCopyColor, 0.38);
        }

        .h3 {
            @include setFont($headline4, 32px, 500);
        }

        .h4 {
            @include setFont($subtitle1, 20px, 500);
        }
    
        .caption1 {
            @include setFont($subtitle2, 18px);
        }
    
        .caption2 {
            @include setFont($caption1, 16px);
        }

        .caption3 {
            @include setFont($subtitle2, 18px, 500);
        }

        border-radius: 4px;
        border: 1px solid #E6E6E6;
        background: #FFF;
        padding: $gap15;

        .cardHeader {
            display: flex;
        }

        .batchDate {
            width: 75px;
            margin-right: $gap15;
            border-radius: 4px;
            border: 1px solid #E6E6E6;
            background: #FFF;
            overflow: hidden;
            text-align: center;

            .month {
                background: #201A19;
                color: #FFF;
                padding: 4px 0;
            }

            .date {
                margin-top: $gapSmall;
            }

            .day {
                margin-bottom: $gapSmall;
            }
        }

        .batchInfo {
            flex: 0 0 calc(100% - 90px);
        }

        .timeAmount {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #E6E6E6;
            padding-bottom: $gapSmaller;
            margin-bottom: $gapSmaller;
        }

        .hasArray {
            .caption2 {
                margin-right: $gapSmall;
                color: #201A1933;

                &.active {
                    @extend .fontColorDark;
                }
            }
        }

        .colView {
            display: flex;
            flex-wrap: wrap;
            margin: $gapSmall (-$gap15) 0;

            li {
                padding: 0 $gap15;
                display: flex;
                flex: 0 0 100%;
                margin-bottom: $gapSmaller;
                align-items: center;

                @media (min-width: 768px) {
                    flex: 0 0 auto;                    
                    margin-bottom: 0;
                }

                .ctaLook {
                    display: flex;
                    align-items: center;
                    border-radius: 100px;
                    background: $bg;
                    padding: $gapSmaller $gapSmall;

                    .material-icons-outlined {
                        font-size: 18px;
                        margin-right: $gapSmaller;
                    }
                }

                &.hasPipe {
                    position: relative;

                    @media (min-width: 768px) {
                        &::after {
                            content: "";
                            width: 1px;
                            height: 40%;
                            background: #E6E6E6;
                            position: absolute;
                            right: 0;
                            top: 30%;
                        }
    
                        &:last-child {
                            padding-right: 0;
                            margin-right: 0;
    
                            &::after {
                                display: none;
                            }
                        }
                    }
                }

                &.hasSeparator {
                    .caption {
                        margin: 0;
                        position: relative;
                        padding-right: $gapSmall;
                        margin-right: $gapSmall;

                        @media (min-width: 768px) {
                            &::after {
                                content: "";
                                width: 1px;
                                height: 80%;
                                background: #E6E6E6;
                                position: absolute;
                                right: 0;
                                top: 5%;
                            }
    
                            &:last-child {
                                padding-right: 0;
                                margin-right: 0;
    
                                &::after {
                                    display: none;
                                }
                            }
                        }

                        
                    }
                }
            }
        }

        .cardFooter {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-top: $gapLargest;

            .colView {
                margin: $gapSmaller 0 0;

                li {
                    &:first-child {
                        padding-left: 0;
                    }

                    .material-icons {
                        color: #F9B600;
                        font-size: 18px;
                        margin-left: $gapSmaller;
                    }
                }
            }

            .caption3 {
                text-transform: capitalize;
                flex: 0 0 100%;
            }

            .avatar {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                font-size: 0;
                background-color: #E6E6E6;
            }
        }
    }

}