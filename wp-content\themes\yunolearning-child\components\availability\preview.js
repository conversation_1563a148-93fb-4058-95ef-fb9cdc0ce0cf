const YUNOAvailabilityPreview = (function($) {
    const availabilityPreview = function() {
        
        Vue.component('yuno-availability-preview', {
            props: ["data", "options"],
            template: `
                <b-modal 
                    :active.sync="options.modal" 
                    :width="800" 
                    :can-cancel="['escape', 'x']"
                    class="yunoModal preview">
                        <div class="modalHeader">
                            <h2 class="modalTitle">{{options.title}}</h2>
                        </div>
                        <div class="modalBody">
                            <div class="wrapper noBG">
                                <ul class="legends">
                                    <li class="yes">Available</li>
                                    <li class="no">Unavailable</li>
                                </ul>
                                <yuno-table :data="instructorAvailabilityGrid" :options="tableOptions"></yuno-table>
                            </div>
                        </div>
                </b-modal>
            `,
            data() {
                return {
                    tableOptions: {
                        isFluid: false,
                        pageLoading: false,
                        apiPaginated: false,
                        totalResult: "",
                        perPage: 50,
                        // defaultSort: "sun",
                        // sortDirection: "desc",
                        limit: 20,
                        offset: 0,
                        hasStriped: false
                    }
                }
            },
            computed: {
                ...Vuex.mapState([
                    'timeSlots',
                    'instructorAvailabilityGrid'
                ])
            },
            async created() {
                
            },
            mounted() {
                this.fetchResources(true);
            },
            methods: {
                additionalRow(rows) {
                },
                additionalCols(cols) {
                    let slot = {
                        field: "slot",
                        label: "",
                        sortable: true,
                        hasSlot: true
                    }

                    cols.push(slot);

                    for (let i = 0; i < cols.length; i++) {
                        const col = cols[i];
                        
                        if (col.field === "slot") {
                            col.hasTag = true;
                        }

                        if (col.field === "sun") {
                            col.hasTag = true;
                        }

                        if (col.field === "mon") {
                            col.hasTag = true;
                        }

                        if (col.field === "tue") {
                            col.hasTag = true;
                        }

                        if (col.field === "wed") {
                            col.hasTag = true;
                        }

                        if (col.field === "thu") {
                            col.hasTag = true;
                        }

                        if (col.field === "fri") {
                            col.hasTag = true;
                        }

                        if (col.field === "sat") {
                            col.hasTag = true;
                        }
                    };
                },
                gotResources(options) {
                    const module = this.instructorAvailabilityGrid;

                    if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                        let getData = options.response.data.data,
                            rows = getData.rows,
                            cols = getData.columns,
                            count = options.response.data.count;

                        this.additionalCols(cols);
                        this.additionalRow(rows)

                        this.tableOptions.pageLoading = false;
                        this.tableOptions.totalResult = count;
                        module.data = getData;
                    } else {
                        module.data = [];
                        this.tableOptions.totalResult = 0;
                    }
                },
                fetchResources(moduleLoading) {
                    this.instructorAvailabilityGrid.data = [];
                    this.instructorAvailabilityGrid.success = false;

                    const instance = this;
                    const options = {
                        apiURL: YUNOCommon.config.availabilityGridAPI(isLoggedIn),
                        module: "gotData",
                        store: "instructorAvailabilityGrid",
                        moduleLoading: moduleLoading,
                        addToModule: false,
                        callback: true,
                        callbackFunc: function(options) {
                            return instance.gotResources(options)
                        }
                    };

                    this.$store.dispatch('fetchData', options);
                },
            }
        });
    };

    return {
        availabilityPreview: availabilityPreview
    };
})(jQuery);



