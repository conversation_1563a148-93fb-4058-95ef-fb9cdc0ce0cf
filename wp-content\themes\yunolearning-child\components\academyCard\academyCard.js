Vue.component("yuno-academy-card", {
    props: {
        data: {
            type: Object,
            required: false,
        },
        isSkeleton: {
            type: Boolean,
            default: false,
        },
        cta: {
            type: Object,
            required: false,
        },
    },
    template: `
        <div class="sectionWrapper hasBorder gap15 academyCard makeGrid isWrap">
            <template v-if="isSkeleton">
                <figure class="mediaImg m-right-large-times-2 width120 m-bottom-small-times-3-mobile">
                    <b-skeleton height="120px" width="120px"></b-skeleton>
                </figure>
                <div class="academyContent calc140">
                    <h2 class="headline6 onSurface m-bottom-small-times-1">
                        <b-skeleton active width="200px"></b-skeleton>
                    </h2>
                    <ul class="makeGrid withPipe m-top-small-times-1">
                        <li class="subtitle2 noBold onSurfaceVariant makeGrid">
                            <b-skeleton active width="80px"></b-skeleton>
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant makeGrid">
                            <b-skeleton active width="80px"></b-skeleton>
                        </li>
                    </ul>
                    <ul class="makeGrid withPipe m-top-small-times-1">
                        <li class="subtitle2 noBold onSurfaceVariant makeGrid">
                            <b-skeleton active width="80px"></b-skeleton>
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant makeGrid">
                            <b-skeleton active width="80px"></b-skeleton>
                        </li>
                    </ul>
                </div>
            </template>
            <template v-else>
                <figure class="mediaImg m-right-large-times-2 width120 m-bottom-small-times-3-mobile">
                    <img 
                        :src="data.logo_url === '' ? 'https://via.placeholder.com/120' : data.logo_url" 
                        :alt="data.name" class="radiusBorder"
                    >
                </figure>
                <div class="academyContent calc140">
                    <h2 class="headline6 onSurface m-bottom-small-times-1">{{ data.name }}</h2>
                    <div class="makeGrid" v-if="data.org.name !== ''">
                        <div class="roundImg16 m-right-small-times-1 makeGrid">
                            <img 
                                :src="data.org.fav_icon === '' ? 'https://via.placeholder.com/16' : data.org.fav_icon" 
                                :alt="data.org.name" 
                                width="16" 
                                height="16"
                            >
                        </div>
                        <p class="subtitle2 noBold onSurfaceVariant">{{ data.org.name }}</p>
                    </div>
                    <ul class="makeGrid withPipe m-top-small-times-1">
                        <li class="subtitle2 noBold onSurfaceVariant">
                            {{ data.active_learners_count }} active enrollments
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant">
                            {{ data.past_learners_count }} past
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant" v-if="false">
                            1027 learners
                        </li>
                    </ul>
                    <ul class="makeGrid withPipe m-top-small-times-1">
                        <li class="subtitle2 noBold onSurfaceVariant" v-if="false">
                            Signed up: Jan 24, 2023
                        </li>
                        <li class="subtitle2 noBold onSurfaceVariant">
                            Courses: {{ data.courses_count }}
                        </li>
                    </ul>
                    <div class="makeGrid vAlignCenter m-top-small-times-1">
                        <div class="usersList" v-if="false">
                            <span v-for="(learner, j) in 6" :key="j" v-if="j <= 5">
                                <img src="https://via.placeholder.com/24" alt="sss" width="24" height="24">
                            </span>
                        </div>
                        <p class="caption1 noBold onSurfaceVariant"> {{ data.mapped_instructors_count }} {{ data.mapped_instructors_count > 1 ? "instructors" : "instructor" }}</p>
                    </div>
                </div>
                <div class="ctaWrapper">
                      <b-button tag="a" :href="cta.url" :target="cta.target"
                          class="yunoSecondaryCTA button fat">
                          {{ cta.label }}
                      </b-button>
                  </div>
            </template>
        </div>
    `,
    data() {
        return {};
    },
    computed: {
        ...Vuex.mapState(["user"]),
    },
    async created() { },
    destroyed() { },
    mounted() { },
    methods: {},
});
