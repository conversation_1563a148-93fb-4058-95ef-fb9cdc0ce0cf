@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
	.batchCard {
		.innerWrapper {
			border: 1px solid $grey;
			border-radius: 4px;	
			height: 100%;
			overflow: hidden;

			.noLPad {
				padding-left: 0;
			}

			.noRPad {
				padding-right: 0;
			}
		}

		.batchTitle {
			color: $primaryCopyColor;
			font-size: $fontSizeLarger;
			padding: $gap15 0;
			margin: 0;

			a {
				color: $primaryCopyColor;	
			}
		}

		.batchStart {
			text-align: center;
			padding-top: $gap15;


			.date {
				font-size: $fontSizeLargest + 14;
			    font-weight: 600;
			    line-height: $fontSizeLargest + 14;;
			}

			.month {
				@include setFontColor($primaryCopyColor, 0.5);
			    font-size: $fontSizeLarger + 6;
			    text-transform: uppercase;
			    margin-bottom: $gapSmall;
			    line-height: $fontSizeLarger + 6;
			}

			.year {
				@include setBGColor($primaryCopyColor, 0.1);
			    font-size: $fontSizeSmall;
			    border-radius: 3px;
			    display: inline-block;
			    padding: 3px 8px;
			}
		}

		.startsIn {
			font-size: $fontSizeSmall;
			padding: $gap15 0 0 $gap15;

			@media (min-width: 992px) {
				padding: $gap15 0 0;
			}
		}

		.batchInsight {
			font-size: $fontSizeSmall;
			padding-top: $gap15;

			&.demo {
				padding: 0 0 $gap15;
			}

			li {
			    position: relative;
			    padding-left: $gapLargest * 2;
			    margin-bottom: $gapSmaller;
			    font-size: $fontSizeSmall;

				&.fatSize {
					font-weight: 600;
					font-size: $fontSizeLarger;
					margin-bottom: $gapSmall;
				}

			    .batchLabel {
			    	@include setFontColor($primaryCopyColor, 0.5);
				    position: absolute;
				    left: 0;
				    top: 0;
			    }
			}
		}

		.instructorDetails {
		    @include setBGColor($primaryCopyColor, 0.05);
		    padding: $gapSmaller $gap15;
		    margin-top: $gap15;
		    display: flex;
		    align-items: center;

		    .img {
		    	width: 32px;
			    height: 32px;
			    overflow: hidden;
			    border-radius: 50%;

			    img {
			    	width: 32px;
			    	height: 32px;
			    }
		    }

		    .instructorInfo {
		    	font-size: $fontSizeSmaller;
			    padding-left: $gapSmaller;

			    .instructorLabel {
		    	    color: #abacad;
					display: block;
			    }

			    .name {
			    	a {
			    		color: $primaryCopyColor;
			    	}
			    }
		    }
		}

		.batchCardFooter {
			padding: $gap15 0;
			margin: $gap15 $gap15 0; 
			justify-content: space-between;	
			border-top: 1px solid $grey;


			.price {
				font-size: $fontSizeLarger + 6;
				font-weight: 500;
			}
		}

		.daysOfWeek {
			padding: $gapSmall 24px 0;

			.daysOfWeekRow {
				display: flex;
				flex-direction: column;
				padding-top: $gapSmall;

				.label {
					margin-bottom: $gapSmaller;
					@include setFontColor($primaryCopyColor, 0.5);
				}

				.tag {
					border-radius: 20px;
					font-size: 12px;
					padding: 4px 10px;
					color: $secondaryCopyColor;
					margin-right: $gapSmaller;
					margin-bottom: $gapSmaller;
					@include setBGColor($primaryCopyColor, 0.3);

					&.active {
						@include setBGColor($secondaryColor, 1);
					}

					&:last-child {
						margin-right: 0;
					}
	
					// &:nth-child(even) {
					// 	@include setBGColor($secondaryColor, 0.7);				
					// }
				}

			}

			@media (min-width: 768px) {
				padding: 0;
			}
		}

		.forCourseDetail {
			.enrollment {
				display: none;
			}
		}

		&:hover {
			.forCourseDetail {
				.enrollment {
					display: block;
				}
			}	
		}

		&.demo {
			.batchStart {
				padding-bottom: $gap15;
			}

			.enrollCtaWrapper {
				padding: 0 0 $gap15;
			}
		}
	}
}