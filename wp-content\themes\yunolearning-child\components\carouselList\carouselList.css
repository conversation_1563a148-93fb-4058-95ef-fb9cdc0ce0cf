#app .yunoCarouselList {
  padding: 30px 0;
}

@media (min-width: 768px) {
  #app .yunoCarouselList {
    padding: 60px 0;
  }
}

#app .yunoCarouselList > .container {
  position: relative;
  overflow: hidden;
  padding-bottom: 15px;
}

#app .yunoCarouselList .sectionTitle {
  font-size: 32px;
  text-align: center;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  #app .yunoCarouselList .sectionTitle {
    margin: 0 0 30px;
  }
}

#app .yunoCarouselList .tns-outer .tns-controls {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

#app .yunoCarouselList .tns-outer .tns-liveregion {
  display: none;
}

#app .yunoCarouselList .tns-outer .tns-ovh {
  margin: 0 -15px;
}

#app .yunoCarouselList .carouselList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
}

#app .yunoCarouselList .carouselListControls {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 10px;
}

#app .yunoCarouselList .carouselListControls button {
  border: 0;
  background: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 35px;
  height: 35px;
  margin: 0;
  padding: 0;
}

#app .yunoCarouselList .carouselListControls button:focus {
  outline: none;
}

#app .yunoCarouselList .carouselListControls button .fa {
  font-size: 18px;
}

@media (min-width: 768px) {
  #app .yunoCarouselList .carouselListControls {
    position: absolute;
    right: 10px;
    top: 0;
  }
}

#app .yunoCarouselList .carouselCard {
  padding: 0 15px;
}

#app .yunoCarouselList .carouselCard .innerWrap {
  border: 1px solid #E6E6E6;
  position: relative;
  border-radius: 4px;
  padding-bottom: 56px;
  overflow: hidden;
  height: 100%;
}

#app .yunoCarouselList .carouselCard:hover .innerWrap {
  -webkit-box-shadow: rgba(0, 0, 0, 0.117647) 0 0 20px;
          box-shadow: rgba(0, 0, 0, 0.117647) 0 0 20px;
}

#app .yunoCarouselList .carouselCard .cardImg img {
  width: 100%;
  height: auto;
}

#app .yunoCarouselList .carouselCard .cardBody {
  padding: 16px;
}

#app .yunoCarouselList .carouselCard .cardFooter {
  padding: 16px;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
}

#app .yunoCarouselList .carouselCard .cardFooter a {
  color: #002F5A;
  text-decoration: underline;
}

#app .yunoCarouselList .carouselCard .cardTitle {
  font-size: 18px;
  margin: 0 0 10px;
}

#app .yunoCarouselList .carouselCard .cardPrice {
  font-size: 16px;
  margin: 0;
  color: #a62027;
}
/*# sourceMappingURL=carouselList.css.map */