#app .addressCard {
  border: 1px solid #E6E6E6;
  padding: 15px;
  border-radius: 4px;
}

#app .addressCard:hover .actionList {
  visibility: visible;
}

#app .addressCard .header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
  text-transform: capitalize;
}

#app .addressCard .actionList {
  visibility: hidden;
}

#app .addressCard .actionList li {
  width: 28px;
  height: 28px;
  text-align: center;
}

#app .addressCard .actionList li a {
  color: #002F5A;
}

#app .addressCard .addressType {
  font-size: 24px;
  color: rgba(0, 0, 0, 0.5);
  font-family: "Roboto Slab";
  font-weight: 700;
}

#app .addressCard p {
  margin: 0;
}

#app .action {
  text-align: center;
}

#app .action p {
  margin-bottom: 30px;
}
/*# sourceMappingURL=address.css.map */