@import "variables";
@import "mixins";
@import "fonts";
@import "icons";

$font-sizes: (
    headline1: $headline1,
    headline2: $headline2,
    headline3: $headline3,
    headline4: $headline4,
    headline5: $headline5,
    headline6: $headline6,
    subtitle1: $subtitle1,
    subtitle2: $subtitle2,
    caption1: $caption1,
    overline: $overline
);

%smallCaption {
    @include setFont($caption1, 16px, 400, 0);
}

.body1 {
    @include setFont($body1, 28px, 500, 0);
}

$gaps: (
    largest: $gapLargest,
    larger: $gap15,
    large: $gapSmall,
    small: $gapSmaller,
);

$multipliers: (
    1,
    2,
    3,
    4,
    5
);



html body {
    @include setFontFamily('Roboto', 400);
    color: $onSurface;

    .to-top-container {
        display: none;
    }
}

dl, ol, ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.toast {
    flex-basis: auto;
}

.yunoLoader {
    display: none;

    &.isActive {
        display: flex;
    }

    &.withOverlay {
        width: 100%;
        height: 100%;
        @include setBGColor($whiteBG, 0.8);
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
        position: fixed;
        z-index: 7777;
    }

    .yunoSpinner {
        width: 100px;
        height: 100px;
        position: static;
    }
}

.yunoSpinner {
    display: block;
    position: absolute;
    left: 50%;
    top: 50%;
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: $primaryColor;

    -webkit-animation: spin 2s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 2s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */

    &:before {
        content: "";
        position: absolute;
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: $secondaryColor;

        -webkit-animation: spin 3s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
        animation: spin 3s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }

    &:after {
        content: "";
        position: absolute;
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        border-radius: 50%;
        border: 3px solid transparent;
        border-top-color: #f9c922;

        -webkit-animation: spin 1.5s linear infinite; /* Chrome, Opera 15+, Safari 5+ */
          animation: spin 1.5s linear infinite; /* Chrome, Firefox 16+, IE 10+, Opera */
    }
}

@-webkit-keyframes spin {
    0%   { 
        -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg);  /* IE 9 */
        transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
    }
    100% {
        -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg);  /* IE 9 */
        transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
    }
}
@keyframes spin {
    0%   { 
        -webkit-transform: rotate(0deg);  /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(0deg);  /* IE 9 */
        transform: rotate(0deg);  /* Firefox 16+, IE 10+, Opera */
    }
    100% {
        -webkit-transform: rotate(360deg);  /* Chrome, Opera 15+, Safari 3.1+ */
        -ms-transform: rotate(360deg);  /* IE 9 */
        transform: rotate(360deg);  /* Firefox 16+, IE 10+, Opera */
    }
}

#app {
    .onSurface {
        color: $onSurface;
    }
    
    .onSurfaceVariant {
        color: $onSurfaceVariant;
    }
    
    .greenColor {
        color: #25D366;
    }

    .primaryColor {
        color: $primary;
    }

    @each $tag, $lineheight in $font-sizes {
        .#{$tag} {
            // Directly use the font size from the map, line height from the loop, and a fixed font weight
            @include setFont(map-get($font-sizes, $tag), $lineheight + 4px, 500);

            &.bolder {
                font-weight: 700;
            }

            &.noBold {
                font-weight: 400;
            }

            &.alignC {
                text-align: center;
            }

            &.uppercase {
                text-transform: uppercase;
            }

            &.capitalize {
                text-transform: capitalize;
            }

            &.italic {
                font-style: italic;
            }

            &.ellipsis {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .makeGrid {
        display: flex;

        &.alignTop {
            align-items: flex-start;
        }

        &.hasPadding {
            padding: $gap15;
        }

        &.isWrap {
            flex-wrap: wrap;
        }

        &.vAlignCenter {
            align-items: center;
        }

        &.spaceBetween {
            justify-content: space-between;
        }

        &.skeletonWidthAuto {
            .b-skeleton {
                width: auto;
                margin-left: 3px;
            }
        }

        .roundImg16 {
            img {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                font-size: 0;
            }
        }

        .roundImg48 {
            img {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                font-size: 0;
            }
        }

        .width50 {
            flex: 0 0 50%;
        }

        .width70 {
            flex: 0 0 70%;
        }

        .width100 {
            flex: 0 0 100%;
        }

        .width120 {
            flex: 0 0 100%;

            @media (min-width: 768px) {
                flex: 0 0 120px;
            }
        }

        .calc140 {
            flex: 0 0 100%;

            @media (min-width: 768px) {
                flex: 0 0 calc(100% - 140px);
            }
        }

        &.withPipe {
            li {
                &:not(:last-child) {
                    &:after {
                        content: "|";
                        margin: 0 $gap15;
                        color: $grey;
                    }
                }
            }
        }

        .mediaImg {
            img {
                width: 100%;
                height: auto;
            }
        }
    }

    .usersList {
        display: flex;
        padding-left: 10px;
        margin-right: $gapSmall;

        span {
            width: 24px;
            height: 24px;
            background-color: $grey;
            border-radius: 50%;
            border: 1px solid white;
            margin-left: -10px;

            img {
                width: 24px;
                height: auto;
                border-radius: 50%;
                font-size: 0;
            }
        }
    }

    .overline {
        letter-spacing: 1px;
    }

    figure {
        margin: 0;
    }

    .borderBottom {
        border-bottom: 1px solid $grey;
    }

    a {
        color: $primary;

        &.underline {
            text-decoration: underline;
        }
    }

    // Iterates over each gap size and multiplier to generate margin utility classes
    // based on the provided $gaps and $multipliers maps.
    //
    // Parameters:
    // $gaps - A map of gap names to their corresponding sizes.
    // $multipliers - A list of multipliers to apply to each gap size.
    //
    // Example:
    // If $gaps: (small: 8px, medium: 16px) and $multipliers: (1, 2),
    // the following classes will be generated:
    // .m-top-small-times-1, .m-top-small-times-2, .m-top-large-times-1, .m-top-large-times-2,
    @each $name, $size in $gaps {
        @each $multiplier in $multipliers {
            .m-top-#{$name}-times-#{$multiplier} {
                margin-top: $size * $multiplier;
            }

            .m-right-#{$name}-times-#{$multiplier} {
                margin-right: $size * $multiplier;
            }

            .m-bottom-#{$name}-times-#{$multiplier} {
                margin-bottom: $size * $multiplier;
            }

            .m-left-#{$name}-times-#{$multiplier} {
                margin-left: $size * $multiplier;
            }
        }
    }

    // Iterates over each gap size and multiplier to generate padding utility classes
    // for top, right, bottom, and left padding.
    //
    // Variables:
    // $gaps - A map of gap names to their corresponding sizes.
    // $multipliers - A list of multipliers to apply to each gap size.
    //
    // Example:
    // If $gaps: (small: 8px, medium: 16px) and $multipliers: (1, 2),
    // the following classes will be generated:
    // .p-top-small-times-1, .p-top-small-times-2, .p-top-large-times-1, .p-top-large-times-2,
    @each $name, $size in $gaps {
        @each $multiplier in $multipliers {
            .p-top-#{$name}-times-#{$multiplier} {
                padding-top: $size * $multiplier;
            }

            .p-right-#{$name}-times-#{$multiplier} {
                padding-right: $size * $multiplier;
            }

            .p-bottom-#{$name}-times-#{$multiplier} {
                padding-bottom: $size * $multiplier;
            }

            .p-left-#{$name}-times-#{$multiplier} {
                padding-left: $size * $multiplier;
            }
        }
    }


    /**
        * Styles for the .sectionWrapper class.
        * 
        * This class is used to define the styling for a section wrapper element.
        * It sets the padding and border properties, and also provides additional styles
        * for elements with the .hasBorder, .hasTopGap, and .ctaWrapper classes.
        * 
    */
    .sectionWrapper {
        padding: $gapLarger;

        &.hasBorder {
            border: 1px solid #E6E6E6;
            border-radius: 4px;
        }

        &.hasTopGap,
        .ctaWrapper.hasTopGap {
            margin-top: $gapLarger;
        }

        &.hasBtmGap {
            margin-bottom: $gapLarger;
        }

        &.noPadding {
            padding: 0;
        }

        .ctaWrapper {
            &.isFlexbox {
                display: flex;
                gap: $gap15;

                &.alignC {
                    justify-content: center;
                }
            }

            &.stack {
                flex-direction: column;
            }
        }

        .sectionMedia {
            img {
                width: 100%;
                height: auto;
                border-radius: 4px;
            }

            iframe {
                width: 100%;
                height: auto;
            }
        }
    }

    /**
        * Styles for the .simpleHeader class.
        *
        * This class represents a simple header element and contains styles for its layout and appearance.
    */
    .simpleHeader {
        display: flex;
        justify-content: center;
        padding-top: $gapLarger;

        img {
            width: 85px;
            height: auto;
        }
    }

    .button {
        &.yunoSecondaryCTA {
            padding: $gapSmall $gapSmall * 2 ;
            border-radius: 4px;
            border: 1px solid $secondaryV1;
            background-color: #FFF;
            line-height: normal;
            font-size: $subtitle1;
            font-weight: 500;
            color: $onSurface;

            &:hover {
                text-decoration: none;
                border-color: $onSurface;
                background-color: $hover;
            }

            &:active {
                border-color: $onSurface;
            }
        }

        &.yunoPrimaryCTA {
            padding: $gapSmall $gapSmall * 2 ;
            border-radius: 4px;
            border: 1px solid $primary;
            background-color: $primary;
            line-height: normal;
            font-size: $subtitle1;
            font-weight: 500;
            color: #FFF;

            &:hover {
                text-decoration: none;
                background-color: $primaryV1;
            }
        }
        
        &.iconOnly {
            border: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        &.noBG {
            border: 0;
        }
    }

    .mainHeader {
        margin: $gap15 0;
        background-color: #FFF;

        @media (max-width: 767px) {
            margin: 30px 0 15px 0;
            padding-left: 18px;
        }

        .block {
            display: flex;
            flex-direction: column;

            @media (min-width: 768px) {
                padding: 0;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .action {
                margin-top: $gapSmall;

                @media (min-width: 768px) {
                    margin-top: 0;
                }
            }
        }

        .pageTitle {
            display: flex;
            align-items: center;
    
            a { 
                position: relative;
                top: 4px;
                font-size: 18px;
                margin-left: $gapSmaller;
    
                &:hover {
                    text-decoration: none;
                }
            }
        }
    }

    .field {
        .taginput {
			.taginput-container {
				border-color: #CCCCCC;
				padding: 0;
				flex-direction: column;
				display: flex;

				&.is-focused {
					box-shadow: none;
				}

				&.is-focusable {
					box-shadow: none;
				}

				&:focus, &:active {
					box-shadow: none;
					outline: none;
				}

				.autocomplete {
					width: 100%;
				}

				.tag {
					margin: $gapSmall $gap15 0;
					overflow: hidden;
					align-self: flex-start;
					max-width: calc(100% - 30px);

					> span {
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}

				input {
					border: 0;
					margin: 0;
					padding: 0 $gap15;
				}
			}

			&.inlineTags {
				.taginput-container {
					flex-direction: row;

					.tag {
						margin-bottom: $gapSmall;
						margin-right: 0;
					}
				}
			}

            .dropdown-menu {
                box-shadow: rgba(0,0,0,.117647) 0 0 10px;

                .dropdown-content {
                    box-shadow: none;

                    .dropdown-item {
                        @include setFontColor($primaryCopyColor, 0.5);
                        padding: $gapSmall $gap15;
                        border-bottom: 1px solid $whiteBG;
        
                        &.disabled {
                            @include setBGColor($primaryCopyColor, 0.2);
                            opacity: 0.5;
                            cursor: not-allowed;
                            border-bottom: 1px solid $whiteBG;
                        }
        
                        &.is-hovered {
                            @include setBGColor($primaryCopyColor, 0.8);
                            color: $secondaryCopyColor;
                        }
        
                        &:active, &:focus {
                            border: 0;
                            background: none;
                        }
                    }
                }
                .dropdown-item {
                    padding: 0;
                }
            }
		}
    }

    .field {
        .error {
            color: red;
            display: none;
        }

        &.hasInput {
            .field-body {
                .field.has-addons {
                    flex-wrap: wrap;
                    
                    .fieldWrapper {
                        flex: 0 0 100%;
                    }

                    .inputField {
                        margin-left: 34px;
                        width: 50%;

                        .field.has-addons {
                            .control {
                                flex: 0 0 100%;
                            }
                        }
                    }
                }
            }
        }

        &.hasFlexbox {
            .field-body {
                .field.has-addons {
                    flex-wrap: wrap;
                    .helper-text {
                        flex: 0 0 100%;
                    }

                    .fieldWrapper {
                        flex: 0 0 100%;
                        margin-top: $gapSmall;
                    }
                }
            }
        }

        .provider {

        }

        &.uploadField {
            .field {
                &.has-addons {
                    display: block;
                }
            }

            .helper {
                @extend %smallCaption;
                @extend .onSurfaceVariant;
            }

            .upload {
                margin-top: $gap15;

                .file-cta {
                    background-color: $primary;
                    color: white;

                    .material-icons-outlined {
                        font-size: 18px;
                        margin-right: $gapSmaller;
                    }
                }
            }
        }

        &.loading {
            height: 80px;
        }

        .taginput .taginput-container {
            border-color: #E6E6E6;
        }

        .label {
            @extend .body1;
            @extend .onSurface;
        }

        .b-radio.radio, .b-checkbox.checkbox {
            align-items: flex-start;
            
            input[type=radio]+.check, input[type=checkbox]+.check {
                border-color: #534342;
    
                &:before {
                    background-color: #534342;
                }
            }

            input[type=checkbox]:checked+.check {
                background-color: #534342;
            }
        }

        .radio-group {
            .b-radio {
                margin-bottom: 0;
                align-items: center;
            }

            .control-label {
                font-size: $subtitle2;
            }
        }

        .checkbox-group  {
            .b-checkbox.checkbox {
                margin-bottom: 0;
                align-items: center;
            }

            .control-label {
                font-size: $subtitle2;
            }
        }

        .b-radio.radio, .b-checkbox.checkbox {
            align-items: flex-start;
            
            input[type=radio]+.check, input[type=checkbox]+.check {
                border-color: #534342;
    
                &:before {
                    background-color: #534342;
                }
            }

            input[type=checkbox]:checked+.check {
                background-color: #534342;
            }
        }
        

        .control {
            input[type="text"] {
                border-color: #E6E6E6;
                height: 40px;
            }

            textarea {
                border-color: #E6E6E6;
            }

            .select {
                select {
                    border-color: #E6E6E6;    
                }
            }

            &.invalid {
                & + .error {
                    display: block;
                }
            }
        }

        .makeItGrid {
            display: flex;
            flex-wrap: wrap;
            margin: $gapSmaller (-$gap15) 0;

            .field {
                flex: 0 0 33.3%;
                padding: 0 $gap15;
                margin: 0;
            }

            .error {
                flex: 0 0 100%;
                padding: 0 $gap15;
            }
        }
    }
    .yunoFooter {

        .button {

            &.yunoSecondaryCTA {
                padding: $gapSmall $gapSmall * 2 ;
                border-radius: 4px;
                border: 1px solid $primary;
                background-color: $primary;
                line-height: normal;
                font-size: $subtitle1;
                font-weight: 500;
                color: #FFF;
    
                &:hover {
                    text-decoration: none;
                    background-color: $primaryV1;
                }
            }
        }
    }

    .b-steps {
        .steps .step-items {
            .step-item {
                &.is-previous {
                    .step-marker {
                        background-color: $onSurface;
                    }
                }

                &.is-active {
                    .step-marker {
                        border-color: $onSurface;
                        color: $onSurface;
                    }
                }

                a {
                    color: $onSurface;
                }

                .step-link {
                    gap: $gapSmall;
                }
            }
        }

        .ctaWrapper {
            flex: 0 0 100%;
            gap: $gap15;
            display: flex;
        }
    }
}

.modal {
    align-items: center;
    display: none;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    position: fixed;
    z-index: 40;

    &.yunoModal {
        .yunoLoader {
            height: 200px;
            position: relative;
        }
        .modal-close {
            position: absolute;
            right: 10px;
            top: 10px;
            background-color: $primaryCopyColor;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            justify-content: center;
            align-items: center;

            &:after, &:before {
                background-color: transparent;
            }

            &:before {
                display: none;
            }

            &:after {
                content: "\f00d";
                @extend .fa;
                color: $secondaryCopyColor;
                font-size: $fontSizeLarger;
                position: static;
                width: auto;
                height: auto;
                transform: none;
            }
        }

        .modalHeader {
            .modalTitle {
                font-size: $fontSizeLarger + 2;
                font-weight: 500;
                margin-bottom: $gapLargest;
            }
        }

        .modalFooter {
            margin-top: $gapLargest;

            .button {
                min-width: 120px;
            }
        }

        &.loginSignupModal, &.lightTheme {
            .modal-content {
                padding: 0;
                border-radius: 4px;
            }
            .modalHeader {
                .modalTitle {
                    @include setFontColor($primaryCopyColor, 0.87);
                    font-size: $headline6;
                    padding: 10px 40px 10px 20px;
                    line-height: 24px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                    margin-bottom: 0;
                }
            }

            .modalBody {
                padding: $gapLarger;

                .modalCaption {
                    font-size: $subtitle1;
                    @include setFontColor($primaryCopyColor, 0.87);
                    text-align: left;
                    padding-bottom: $gap15;
                    font-weight: 500;
                }
            }

            .modal-close {
                background-color: $whiteBG;
                @include setFontColor($primaryCopyColor, 0.6);
                right: 20px;
                top: 7px;

                &::after {
                    content: "\e5cd";
                    @extend .material-icons-outlined;
                    @include setFontColor($primaryCopyColor, 0.6);
                }
            }
        }
    }
}