@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.ylIcon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'yuno-icon' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    
    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
    font-family: 'Material Icons Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
  
  .material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

#app {
    .buyingOptions {
        background: rgb(147,14,26);
        background: linear-gradient(212deg, rgba(147,14,26,1) 0%, rgba(0,47,90,1) 100%);
        padding-bottom: $gapLargest;

        .buyingCardWrapper {
            padding-bottom: $gapLargest;

            @media (min-width: 768px) {
                padding-bottom: 0;
            }
        }

        .buyingCard {
            background-color: $whiteBG;
            border-radius: 4px;
            padding: $gapLargest;
            text-align: center;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            

            .cardTitle {
                font-size: $fontSizeLarger + 6;
                font-weight: 500;
                margin-bottom: $gap15;
                line-height: 34px;
            }

            .cardPrice {
                font-size: $fontSizeLargest - 4;
                font-weight: 600;

                .noBold {
                    font-weight: normal;
                }
            }

            .cardNote {
                font-size: $fontSizeSmall;
                @include setFontColor($primaryCopyColor, 0.4);
                margin: $gap15 0 $gapLargest;

                &.customGap {
                    margin: (-$gapSmall) 0 $gap15 0;
                }
            }

            .featureList {
                margin: 0 auto;
                display: inline-block;

                li {
                    text-align: left;
                    position: relative;
                    padding-left: $gapLargest;
                    font-size: $fontSizeLarger;
                    margin-bottom: $gapSmall;

                    &:before {
                        content: "checkThin";
                        @extend .ylIcon;
                        font-size: 12px;
                        position: absolute;
                        left: 0;
                        top: 7px;
                    }
                }
            }
        }

        @media (min-width: 768px) {
            padding-bottom: $gapLargest * 2;
        }
    }
}