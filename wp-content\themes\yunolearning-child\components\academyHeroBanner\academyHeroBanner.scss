@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
    .academyHeroBanner {
        padding: $gap15 * 3 0;
        color: white;
        background-size: cover;
        background-position: center center;
        position: relative;

        .container {
            z-index: 3;
        }
        

        &::before {
            content: "";
            position: absolute;
            z-index: 2;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(29, 29, 31, 0.6);
        }

        .title {
            @include setFont($headline1, 62px, 700, $gapSmall);
            color: white;
        }

        .categories {
            display: flex;

            li {
                margin-right: 3px;
                @include setFont($body2, normal, 500);

                &.label {
                    margin-right: $gapSmaller;
                } 
            }
        }

        .description {
            padding: $gapLargest 0 0;
            @include setFont($body1, 24px);
            margin-bottom: $gap15;
        }

        .ctaWrapper {
            display: flex;
        }
    }
}