.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon, #app .buyingOptions .buyingCard .featureList li:before {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

#app .buyingOptions {
  background: #930e1a;
  background: linear-gradient(212deg, #930e1a 0%, #002f5a 100%);
  padding-bottom: 30px;
}

#app .buyingOptions .buyingCardWrapper {
  padding-bottom: 30px;
}

@media (min-width: 768px) {
  #app .buyingOptions .buyingCardWrapper {
    padding-bottom: 0;
  }
}

#app .buyingOptions .buyingCard {
  background-color: #FFF;
  border-radius: 4px;
  padding: 30px;
  text-align: center;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#app .buyingOptions .buyingCard .cardTitle {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 15px;
  line-height: 34px;
}

#app .buyingOptions .buyingCard .cardPrice {
  font-size: 28px;
  font-weight: 600;
}

#app .buyingOptions .buyingCard .cardPrice .noBold {
  font-weight: normal;
}

#app .buyingOptions .buyingCard .cardNote {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  margin: 15px 0 30px;
}

#app .buyingOptions .buyingCard .cardNote.customGap {
  margin: -10px 0 15px 0;
}

#app .buyingOptions .buyingCard .featureList {
  margin: 0 auto;
  display: inline-block;
}

#app .buyingOptions .buyingCard .featureList li {
  text-align: left;
  position: relative;
  padding-left: 30px;
  font-size: 18px;
  margin-bottom: 10px;
}

#app .buyingOptions .buyingCard .featureList li:before {
  content: "checkThin";
  font-size: 12px;
  position: absolute;
  left: 0;
  top: 7px;
}

@media (min-width: 768px) {
  #app .buyingOptions {
    padding-bottom: 60px;
  }
}
/*# sourceMappingURL=buyingOptions.css.map */