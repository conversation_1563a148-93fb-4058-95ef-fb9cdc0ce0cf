@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

#app {
	.classCard {
		border: 1px solid $grey;
		border-radius: 4px;			
		padding: $gap15;
		margin-bottom: $gapLargest;

		&:last-child {
			margin-bottom: 0;
		}

		@media (min-width: 768px) {
			padding: $gapLargest;	
		}

		.classID {
			display: inline-flex;
			background-color: $grey;
			font-size: 12px;
			align-items: center;
			padding: $gapSmaller $gap15;
			border-radius: 20px;
			margin-top: $gapSmall;
			overflow: hidden;
			position: relative;
			cursor: pointer;

			.idField {
				position: absolute;
				left: -99999px;
			}
			
			.idLabel {
				padding-right: $gapSmaller;
				font-weight: 500;
			}

			.material-icons {
				font-size: 14px;
				padding-left: $gapSmaller;
			}
		}

		.actionList {
			display: flex;
			margin-top: $gap15;

			@media (min-width: 768px) {
				justify-content: center;
			}

			li {
				$size: 36px;

				width: $size;
				height: $size;
				margin-right: $gapSmall;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;

				&:last-child {
					margin-right: 0;
				}

				.fa {
					font-size: $fontSizeLarger;
				}
			}
		}

		.classSchedule {
			margin-right: $gap15;
			text-align: center;

			@media (min-width: 768px) {
				margin-right: $gapLargest;
			}

			.date {
				font-size: $fontSizeLargest;
			    font-weight: 600;
			    line-height: $fontSizeLargest;;
			}

			.month {
				@include setFontColor($primaryCopyColor, 0.5);
			    font-size: $fontSizeLarger;
			    text-transform: uppercase;
			    margin-bottom: $gapSmall;
			    line-height: $fontSizeLarger;
			}

			.year {
				@include setBGColor($primaryCopyColor, 0.1);
			    font-size: $fontSizeSmall;
			    border-radius: 3px;
			    display: inline-block;
			    padding: 3px 8px;
			}

			&.noMD-LG {
				@media (min-width: 768px) {
					display: none;
				}
			}
			&.noSM {
				display: none;

				@media (min-width: 768px) {
					display: block;
				}
			}
		}

		.classIntro {
			display: flex;
			align-items: center;
			flex-wrap: wrap;

			.wrapper {
				padding-bottom: $gap15;

				@media (min-width: 768px) {
					padding-bottom: 0;
				}
			}

			.classTitle {
				color: $primaryCopyColor;
				font-size: $fontSizeLarger;

				a {
					color: $primaryCopyColor;	
				}
			}
			.classType {
				line-height: 14px;
			    background: #edc264;
			    display: inline-block;
			    text-transform: uppercase;
			    padding: 2px $gapSmall;
			    border-radius: 30px;
			    font-size: $fontSizeSmallest;
			}
		}

		.classMeta {
			flex: 0 0 75%;

			li {
				display: flex;
				font-size: $fontSizeSmall;
				margin-bottom: $gapSmaller;

				&:last-child {
					margin-bottom: 0;
				}

				.caption {
					@include setFontColor($primaryCopyColor, 0.5);
					padding-right: $gapSmall;
					flex: 0 0 33%;
				}
			}

			@media (min-width: 768px) {
				flex: 0 0 50%;

				li {
					.caption {
						flex: 0 0 50%;
					}
				}
			}
		}

		.classMetaWrapper {
			display: flex;
			height: 100%;
			align-items: center;
			flex-wrap: wrap;

			@media (min-width: 768px) {
				justify-content: space-between;
			}

			.ctaWrapper {
				flex: 0 0 100%;
				padding-top: $gap15;


				@media (min-width: 768px) {
					padding-top: 0;
					flex: 0 0 auto;
				}
			}
		}

		.classIntroWrapper {
			display: flex;
		}
	}

	.yunoModal {
		&.learnerDashboard {
			.modalMsg {
				color: #000;
				margin-bottom: $gap15;
			}
	
			.classFields {
				> li {
					font-size: $fontSizeLarge;
					line-height: normal;
					@include setFontColor($primaryCopyColor, 0.5);
					margin-bottom: $gap15;
		
					.listSubtitle {
						margin-bottom: $gapSmall;
						font-size: $fontSizeSmall;
					}
		
					.caption {
						font-weight: 500;
						color: $primaryCopyColor;
						display: block;
						margin-bottom: $gapSmaller
					}
		
					&:last-child {
						margin-bottom: 0;
					}
		
					.selectedLearners {
						margin-top: $gapSmall;
						max-height: 245px;
						overflow-y: auto;
		
						li {
							padding: $gapSmaller $gapSmall;
							font-weight: 400;
		
							.caption {
								font-weight: 400;
								margin: 0;
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
								font-size: $fontSizeSmall;
							}
						}
					}
		
					.clipboard {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: $gap15;
		
						.control {
							flex: 0 0 85%;
						}
		
						.trigger {
							margin-left: $gap15;
							cursor: pointer;
							width: 36px;
							height: 36px;
							display: flex;
							justify-content: center;
							align-items: center;
						}
					}
				}
			}
		}
		
	}
}