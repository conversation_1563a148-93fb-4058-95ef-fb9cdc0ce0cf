.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'yuno-icon' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.dark87, #app .bannerV2 {
  color: rgba(0, 0, 0, 0.87);
}

.dark60 {
  color: rgba(0, 0, 0, 0.6);
}

#app .bannerV2 {
  padding: 30px 0;
  background-color: #FFDEA6;
}

@media (min-width: 768px) {
  #app .bannerV2 {
    padding: 0 0;
  }
}

#app .bannerV2 .container {
  padding: 30px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  height: 400px;
}

#app .bannerV2 .container .row {
  height: 100%;
}

#app .bannerV2 .wrapper {
  color: #201A19;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

#app .bannerV2 .wrapper h2 {
  font-size: 40px;
  line-height: 52px;
  font-weight: 500;
  margin-bottom: 10px;
}

#app .bannerV2 .wrapper small {
  font-size: 24px;
  line-height: 28px;
  font-weight: 700;
  margin-bottom: 0;
}

#app .bannerV2 .wrapper .yunoSecondaryCTA {
  height: 56px;
  line-height: 40px;
  padding-left: 30px;
  padding-right: 30px;
  border-radius: 4px;
  font-size: 20px;
}

#app .bannerV2 .wrapper .secondaryCTA {
  text-align: center;
  margin-top: 15px;
}

#app .bannerV2 .wrapper .secondaryCTA .smallerBody {
  color: #534342;
  margin-bottom: 10px;
}

#app .bannerV2 .wrapper .secondaryCTA .plainLink {
  text-decoration: underline;
}
/*# sourceMappingURL=bannerV2.css.map */